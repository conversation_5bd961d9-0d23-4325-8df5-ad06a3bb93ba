import { useState, useEffect } from 'react';
import { accountingAPI } from '../../services/api';
import './CreateAccountModal.css';

const CreateAccountModal = ({ isOpen, onClose, onAccountCreated }) => {
  const [formData, setFormData] = useState({
    code: '',
    name: '',
    account_type: 'asset_current',
    reconcile: false,
    active: true,
    group: '',
    currency: ''
  });
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [accountGroups, setAccountGroups] = useState([]);

  // Account type options matching Odoo's structure
  const accountTypes = [
    { value: 'asset_receivable', label: 'Receivable', category: 'Assets' },
    { value: 'asset_cash', label: 'Bank and Cash', category: 'Assets' },
    { value: 'asset_current', label: 'Current Assets', category: 'Assets' },
    { value: 'asset_non_current', label: 'Non-current Assets', category: 'Assets' },
    { value: 'asset_prepayments', label: 'Prepayments', category: 'Assets' },
    { value: 'asset_fixed', label: 'Fixed Assets', category: 'Assets' },
    
    { value: 'liability_payable', label: 'Payable', category: 'Liabilities' },
    { value: 'liability_credit_card', label: 'Credit Card', category: 'Liabilities' },
    { value: 'liability_current', label: 'Current Liabilities', category: 'Liabilities' },
    { value: 'liability_non_current', label: 'Non-current Liabilities', category: 'Liabilities' },
    
    { value: 'equity', label: 'Equity', category: 'Equity' },
    { value: 'equity_unaffected', label: 'Current Year Earnings', category: 'Equity' },
    
    { value: 'income', label: 'Income', category: 'Income' },
    { value: 'income_other', label: 'Other Income', category: 'Income' },
    
    { value: 'expense', label: 'Expenses', category: 'Expenses' },
    { value: 'expense_depreciation', label: 'Depreciation', category: 'Expenses' },
    { value: 'expense_direct_cost', label: 'Cost of Revenue', category: 'Expenses' },
  ];

  useEffect(() => {
    if (isOpen) {
      loadInitialData();
    }
  }, [isOpen]);

  const loadInitialData = async () => {
    try {
      // Load account groups
      const groupsResponse = await accountingAPI.getAccountGroups().catch(() => ({ results: [] }));
      setAccountGroups(groupsResponse.results || groupsResponse);

      // Reset form when modal opens
      // For now, we'll use a hardcoded company ID that we know exists
      // TODO: Load companies dynamically when companies API is available
      setFormData({
        code: '',
        name: '',
        account_type: 'asset_current',
        reconcile: false,
        active: true,
        group: '',
        currency: '',
        company: 'd7a75b6b-dea7-479f-995b-a0c194d79377' // Demo ERP Company ID
      });
      setErrors({});
    } catch (error) {
      console.error('Error loading initial data:', error);
      // Reset form even if loading fails
      setFormData({
        code: '',
        name: '',
        account_type: 'asset_current',
        reconcile: false,
        active: true,
        group: '',
        currency: '',
        company: 'd7a75b6b-dea7-479f-995b-a0c194d79377' // Demo ERP Company ID
      });
      setErrors({});
    }
  };



  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.code.trim()) {
      newErrors.code = 'Account code is required';
    } else if (!/^\d+$/.test(formData.code.trim())) {
      newErrors.code = 'Account code must contain only numbers';
    }
    
    if (!formData.name.trim()) {
      newErrors.name = 'Account name is required';
    }
    
    if (!formData.account_type) {
      newErrors.account_type = 'Account type is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setLoading(true);
    try {
      const accountData = {
        ...formData,
        code: formData.code.trim(),
        name: formData.name.trim(),
      };
      
      // Remove empty fields
      Object.keys(accountData).forEach(key => {
        if (accountData[key] === '' || accountData[key] === null) {
          delete accountData[key];
        }
      });
      
      console.log('Creating account:', accountData);
      const newAccount = await accountingAPI.createAccount(accountData);
      console.log('Account created:', newAccount);
      
      // Notify parent component
      if (onAccountCreated) {
        onAccountCreated(newAccount);
      }
      
      // Close modal
      onClose();
      
    } catch (error) {
      console.error('Error creating account:', error);
      if (error.response?.data) {
        // Handle API validation errors
        const apiErrors = error.response.data;
        setErrors(apiErrors);
      } else {
        setErrors({ general: 'Failed to create account. Please try again.' });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    onClose();
  };

  if (!isOpen) return null;

  // Group account types by category for better UX
  const groupedAccountTypes = accountTypes.reduce((acc, type) => {
    if (!acc[type.category]) {
      acc[type.category] = [];
    }
    acc[type.category].push(type);
    return acc;
  }, {});

  return (
    <div className="o_action_manager">
      <div className="o_form_view o_view_controller">
        <div className="o_form_view_container">

          {/* Form Header */}
          <div className="o_control_panel d-flex">
            <div className="o_control_panel_main">
              <div className="o_control_panel_breadcrumbs">
                <div className="o_control_panel_main_buttons d-flex gap-1 d-empty">
                  <div className="o_form_buttons_view">
                    <button
                      type="button"
                      className="btn btn-primary o_form_button_save"
                      onClick={handleSubmit}
                      disabled={loading}
                    >
                      {loading ? (
                        <>
                          <i className="fa fa-spinner fa-spin me-1"></i>
                          Saving...
                        </>
                      ) : (
                        'Save'
                      )}
                    </button>
                    <button
                      type="button"
                      className="btn btn-secondary o_form_button_cancel"
                      onClick={handleCancel}
                      disabled={loading}
                    >
                      Discard
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Form Content */}
          <div className="o_content">
            <div className="o_form_renderer o_form_editable">
              <div className="o_form_sheet_bg">
                <div className="o_form_sheet position-relative">

                  {/* Error Display */}
                  {errors.general && (
                    <div className="alert alert-danger" role="alert">
                      <i className="fa fa-exclamation-triangle me-2"></i>
                      {errors.general}
                    </div>
                  )}

                  {/* Form Title */}
                  <div className="oe_title">
                    <h1>
                      <div className="o_row">
                        <div className="o_field_widget o_field_char o_field_widget_text">
                          <input
                            type="text"
                            className="o_input"
                            placeholder="Account Name"
                            name="name"
                            value={formData.name}
                            onChange={handleInputChange}
                            disabled={loading}
                          />
                        </div>
                      </div>
                    </h1>
                  </div>

                  {/* Main Form Group */}
                  <div className="o_group">
                    <div className="o_inner_group">

                      {/* Code Field */}
                      <div className="o_wrap_field d-flex d-sm-contents flex-column mb-3 mb-sm-0">
                        <div className="o_cell o_wrap_label flex-grow-1 flex-sm-grow-0 w-100 text-break text-900">
                          <label className="o_form_label" htmlFor="code">Code</label>
                        </div>
                        <div className="o_cell o_wrap_input flex-grow-1 flex-sm-grow-0 text-break" style={{width: '100%'}}>
                          <input
                            type="text"
                            id="code"
                            name="code"
                            className={`o_input ${errors.code ? 'is-invalid' : ''}`}
                            value={formData.code}
                            onChange={handleInputChange}
                            placeholder="e.g., 101000"
                            maxLength="10"
                            disabled={loading}
                          />
                          {errors.code && (
                            <div className="invalid-feedback d-block">
                              {errors.code}
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Type Field */}
                      <div className="o_wrap_field d-flex d-sm-contents flex-column mb-3 mb-sm-0">
                        <div className="o_cell o_wrap_label flex-grow-1 flex-sm-grow-0 w-100 text-break text-900">
                          <label className="o_form_label" htmlFor="account_type">Type</label>
                        </div>
                        <div className="o_cell o_wrap_input flex-grow-1 flex-sm-grow-0 text-break" style={{width: '100%'}}>
                          <select
                            id="account_type"
                            name="account_type"
                            className={`o_input ${errors.account_type ? 'is-invalid' : ''}`}
                            value={formData.account_type}
                            onChange={handleInputChange}
                            disabled={loading}
                          >
                            {Object.entries(groupedAccountTypes).map(([category, types]) => (
                              <optgroup key={category} label={category}>
                                {types.map(type => (
                                  <option key={type.value} value={type.value}>
                                    {type.label}
                                  </option>
                                ))}
                              </optgroup>
                            ))}
                          </select>
                          {errors.account_type && (
                            <div className="invalid-feedback d-block">
                              {errors.account_type}
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Account Group Field */}
                      {accountGroups.length > 0 && (
                        <div className="o_wrap_field d-flex d-sm-contents flex-column mb-3 mb-sm-0">
                          <div className="o_cell o_wrap_label flex-grow-1 flex-sm-grow-0 w-100 text-break text-900">
                            <label className="o_form_label" htmlFor="group">Account Group</label>
                          </div>
                          <div className="o_cell o_wrap_input flex-grow-1 flex-sm-grow-0 text-break" style={{width: '100%'}}>
                            <select
                              id="group"
                              name="group"
                              className="o_input"
                              value={formData.group}
                              onChange={handleInputChange}
                              disabled={loading}
                            >
                              <option value="">Select a group (optional)</option>
                              {accountGroups.map(group => (
                                <option key={group.id} value={group.id}>
                                  {group.name}
                                </option>
                              ))}
                            </select>
                          </div>
                        </div>
                      )}

                    </div>
                  </div>

                  {/* Options Group */}
                  <div className="o_group">
                    <div className="o_inner_group">
                      <div className="o_horizontal_separator">Options</div>

                      {/* Allow Reconciliation */}
                      <div className="o_wrap_field d-flex d-sm-contents flex-column mb-3 mb-sm-0">
                        <div className="o_cell o_wrap_label flex-grow-1 flex-sm-grow-0 w-100 text-break text-900">
                          <label className="o_form_label">Allow Reconciliation</label>
                        </div>
                        <div className="o_cell o_wrap_input flex-grow-1 flex-sm-grow-0 text-break" style={{width: '100%'}}>
                          <div className="o_field_boolean">
                            <div className="custom-control custom-checkbox">
                              <input
                                type="checkbox"
                                className="custom-control-input"
                                id="reconcile"
                                name="reconcile"
                                checked={formData.reconcile}
                                onChange={handleInputChange}
                                disabled={loading}
                              />
                              <label className="custom-control-label" htmlFor="reconcile"></label>
                            </div>
                          </div>
                          <div className="form-text text-muted">
                            Check this if the account should be reconciled (e.g., bank accounts, receivables)
                          </div>
                        </div>
                      </div>

                      {/* Active */}
                      <div className="o_wrap_field d-flex d-sm-contents flex-column mb-3 mb-sm-0">
                        <div className="o_cell o_wrap_label flex-grow-1 flex-sm-grow-0 w-100 text-break text-900">
                          <label className="o_form_label">Active</label>
                        </div>
                        <div className="o_cell o_wrap_input flex-grow-1 flex-sm-grow-0 text-break" style={{width: '100%'}}>
                          <div className="o_field_boolean">
                            <div className="custom-control custom-checkbox">
                              <input
                                type="checkbox"
                                className="custom-control-input"
                                id="active"
                                name="active"
                                checked={formData.active}
                                onChange={handleInputChange}
                                disabled={loading}
                              />
                              <label className="custom-control-label" htmlFor="active"></label>
                            </div>
                          </div>
                          <div className="form-text text-muted">
                            Uncheck to archive this account
                          </div>
                        </div>
                      </div>

                    </div>
                  </div>

                </div>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
};

export default CreateAccountModal;
