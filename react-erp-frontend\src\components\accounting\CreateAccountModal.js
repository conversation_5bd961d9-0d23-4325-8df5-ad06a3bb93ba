import { useState, useEffect } from 'react';
import { accountingAPI } from '../../services/api';
import './CreateAccountModal.css';

const CreateAccountModal = ({ isOpen, onClose, onAccountCreated }) => {
  const [formData, setFormData] = useState({
    code: '',
    name: '',
    account_type: 'asset_current',
    reconcile: false,
    active: true,
    group: '',
    currency: ''
  });
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [accountGroups, setAccountGroups] = useState([]);

  // Account type options matching Odoo's structure
  const accountTypes = [
    { value: 'asset_receivable', label: 'Receivable', category: 'Assets' },
    { value: 'asset_cash', label: 'Bank and Cash', category: 'Assets' },
    { value: 'asset_current', label: 'Current Assets', category: 'Assets' },
    { value: 'asset_non_current', label: 'Non-current Assets', category: 'Assets' },
    { value: 'asset_prepayments', label: 'Prepayments', category: 'Assets' },
    { value: 'asset_fixed', label: 'Fixed Assets', category: 'Assets' },
    
    { value: 'liability_payable', label: 'Payable', category: 'Liabilities' },
    { value: 'liability_credit_card', label: 'Credit Card', category: 'Liabilities' },
    { value: 'liability_current', label: 'Current Liabilities', category: 'Liabilities' },
    { value: 'liability_non_current', label: 'Non-current Liabilities', category: 'Liabilities' },
    
    { value: 'equity', label: 'Equity', category: 'Equity' },
    { value: 'equity_unaffected', label: 'Current Year Earnings', category: 'Equity' },
    
    { value: 'income', label: 'Income', category: 'Income' },
    { value: 'income_other', label: 'Other Income', category: 'Income' },
    
    { value: 'expense', label: 'Expenses', category: 'Expenses' },
    { value: 'expense_depreciation', label: 'Depreciation', category: 'Expenses' },
    { value: 'expense_direct_cost', label: 'Cost of Revenue', category: 'Expenses' },
  ];

  useEffect(() => {
    if (isOpen) {
      loadInitialData();
    }
  }, [isOpen]);

  const loadInitialData = async () => {
    try {
      // Load account groups
      const groupsResponse = await accountingAPI.getAccountGroups().catch(() => ({ results: [] }));
      setAccountGroups(groupsResponse.results || groupsResponse);

      // Reset form when modal opens
      // For now, we'll use a hardcoded company ID that we know exists
      // TODO: Load companies dynamically when companies API is available
      setFormData({
        code: '',
        name: '',
        account_type: 'asset_current',
        reconcile: false,
        active: true,
        group: '',
        currency: '',
        company: 'd7a75b6b-dea7-479f-995b-a0c194d79377' // Demo ERP Company ID
      });
      setErrors({});
    } catch (error) {
      console.error('Error loading initial data:', error);
      // Reset form even if loading fails
      setFormData({
        code: '',
        name: '',
        account_type: 'asset_current',
        reconcile: false,
        active: true,
        group: '',
        currency: '',
        company: 'd7a75b6b-dea7-479f-995b-a0c194d79377' // Demo ERP Company ID
      });
      setErrors({});
    }
  };



  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.code.trim()) {
      newErrors.code = 'Account code is required';
    } else if (!/^\d+$/.test(formData.code.trim())) {
      newErrors.code = 'Account code must contain only numbers';
    }
    
    if (!formData.name.trim()) {
      newErrors.name = 'Account name is required';
    }
    
    if (!formData.account_type) {
      newErrors.account_type = 'Account type is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setLoading(true);
    try {
      const accountData = {
        ...formData,
        code: formData.code.trim(),
        name: formData.name.trim(),
      };
      
      // Remove empty fields
      Object.keys(accountData).forEach(key => {
        if (accountData[key] === '' || accountData[key] === null) {
          delete accountData[key];
        }
      });
      
      console.log('Creating account:', accountData);
      const newAccount = await accountingAPI.createAccount(accountData);
      console.log('Account created:', newAccount);
      
      // Notify parent component
      if (onAccountCreated) {
        onAccountCreated(newAccount);
      }
      
      // Close modal
      onClose();
      
    } catch (error) {
      console.error('Error creating account:', error);
      if (error.response?.data) {
        // Handle API validation errors
        const apiErrors = error.response.data;
        setErrors(apiErrors);
      } else {
        setErrors({ general: 'Failed to create account. Please try again.' });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    onClose();
  };

  if (!isOpen) return null;

  // Group account types by category for better UX
  const groupedAccountTypes = accountTypes.reduce((acc, type) => {
    if (!acc[type.category]) {
      acc[type.category] = [];
    }
    acc[type.category].push(type);
    return acc;
  }, {});

  return (
    <div className="odoo-page">
      {/* Top Control Panel */}
      <div className="odoo-control-panel">
        <div className="odoo-control-buttons">
          <button
            className="odoo-btn odoo-btn-primary"
            onClick={handleSubmit}
            disabled={loading}
          >
            {loading ? 'Saving...' : 'Save'}
          </button>
          <button
            className="odoo-btn odoo-btn-secondary"
            onClick={handleCancel}
            disabled={loading}
          >
            Discard
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="odoo-content">
        <div className="odoo-form">

          {/* Error Display */}
          {errors.general && (
            <div className="alert alert-danger">
              {errors.general}
            </div>
          )}

          {/* Account Name - Large Title Input */}
          <div className="account-name-section">
            <input
              type="text"
              className="account-name-input"
              placeholder="Account Name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              disabled={loading}
            />
          </div>

          {/* Form Fields */}
          <div className="form-fields">

            {/* Code Field */}
            <div className="field-row">
              <label>Code</label>
              <input
                type="text"
                name="code"
                className={`field-input ${errors.code ? 'error' : ''}`}
                value={formData.code}
                onChange={handleInputChange}
                placeholder="e.g., 101000"
                maxLength="10"
                disabled={loading}
              />
              {errors.code && (
                <div className="field-error">{errors.code}</div>
              )}
            </div>

            {/* Type Field */}
            <div className="field-row">
              <label>Type</label>
              <select
                name="account_type"
                className={`field-input ${errors.account_type ? 'error' : ''}`}
                value={formData.account_type}
                onChange={handleInputChange}
                disabled={loading}
              >
                {Object.entries(groupedAccountTypes).map(([category, types]) => (
                  <optgroup key={category} label={category}>
                    {types.map(type => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </optgroup>
                ))}
              </select>
              {errors.account_type && (
                <div className="field-error">{errors.account_type}</div>
              )}
            </div>

            {/* Account Group Field */}
            {accountGroups.length > 0 && (
              <div className="field-row">
                <label>Account Group</label>
                <select
                  name="group"
                  className="field-input"
                  value={formData.group}
                  onChange={handleInputChange}
                  disabled={loading}
                >
                  <option value="">Select a group (optional)</option>
                  {accountGroups.map(group => (
                    <option key={group.id} value={group.id}>
                      {group.name}
                    </option>
                  ))}
                </select>
              </div>
            )}

          </div>

          {/* Options Section */}
          <div className="options-section">
            <div className="section-title">OPTIONS</div>

            {/* Allow Reconciliation */}
            <div className="checkbox-row">
              <label className="checkbox-label">Allow Reconciliation</label>
              <input
                type="checkbox"
                name="reconcile"
                checked={formData.reconcile}
                onChange={handleInputChange}
                disabled={loading}
              />
              <div className="field-help">
                Check this if the account should be reconciled (e.g., bank accounts, receivables)
              </div>
            </div>

            {/* Active */}
            <div className="checkbox-row">
              <label className="checkbox-label">Active</label>
              <input
                type="checkbox"
                name="active"
                checked={formData.active}
                onChange={handleInputChange}
                disabled={loading}
              />
              <div className="field-help">
                Uncheck to archive this account
              </div>
            </div>

          </div>

        </div>
      </div>
    </div>
  );
};

export default CreateAccountModal;
