import { useState, useEffect } from 'react';
import { accountingAPI } from '../../services/api';
import { Heading, Code, Label, FocusArea } from '../common/Typography';
import './CreateAccountModal.css';

const CreateAccountModal = ({ isOpen, onClose, onAccountCreated }) => {
  const [formData, setFormData] = useState({
    code: '',
    name: '',
    account_type: 'asset_current',
    reconcile: false,
    active: true,
    group: '',
    currency: ''
  });
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [accountGroups, setAccountGroups] = useState([]);

  // Account type options matching Odoo's structure
  const accountTypes = [
    { value: 'asset_receivable', label: 'Receivable', category: 'Assets' },
    { value: 'asset_cash', label: 'Bank and Cash', category: 'Assets' },
    { value: 'asset_current', label: 'Current Assets', category: 'Assets' },
    { value: 'asset_non_current', label: 'Non-current Assets', category: 'Assets' },
    { value: 'asset_prepayments', label: 'Prepayments', category: 'Assets' },
    { value: 'asset_fixed', label: 'Fixed Assets', category: 'Assets' },
    
    { value: 'liability_payable', label: 'Payable', category: 'Liabilities' },
    { value: 'liability_credit_card', label: 'Credit Card', category: 'Liabilities' },
    { value: 'liability_current', label: 'Current Liabilities', category: 'Liabilities' },
    { value: 'liability_non_current', label: 'Non-current Liabilities', category: 'Liabilities' },
    
    { value: 'equity', label: 'Equity', category: 'Equity' },
    { value: 'equity_unaffected', label: 'Current Year Earnings', category: 'Equity' },
    
    { value: 'income', label: 'Income', category: 'Income' },
    { value: 'income_other', label: 'Other Income', category: 'Income' },
    
    { value: 'expense', label: 'Expenses', category: 'Expenses' },
    { value: 'expense_depreciation', label: 'Depreciation', category: 'Expenses' },
    { value: 'expense_direct_cost', label: 'Cost of Revenue', category: 'Expenses' },
  ];

  useEffect(() => {
    if (isOpen) {
      loadInitialData();
    }
  }, [isOpen]);

  const loadInitialData = async () => {
    try {
      // Load account groups
      const groupsResponse = await accountingAPI.getAccountGroups().catch(() => ({ results: [] }));
      setAccountGroups(groupsResponse.results || groupsResponse);

      // Reset form when modal opens
      // For now, we'll use a hardcoded company ID that we know exists
      // TODO: Load companies dynamically when companies API is available
      setFormData({
        code: '',
        name: '',
        account_type: 'asset_current',
        reconcile: false,
        active: true,
        group: '',
        currency: '',
        company: 'd7a75b6b-dea7-479f-995b-a0c194d79377' // Demo ERP Company ID
      });
      setErrors({});
    } catch (error) {
      console.error('Error loading initial data:', error);
      // Reset form even if loading fails
      setFormData({
        code: '',
        name: '',
        account_type: 'asset_current',
        reconcile: false,
        active: true,
        group: '',
        currency: '',
        company: 'd7a75b6b-dea7-479f-995b-a0c194d79377' // Demo ERP Company ID
      });
      setErrors({});
    }
  };



  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.code.trim()) {
      newErrors.code = 'Account code is required';
    } else if (!/^\d+$/.test(formData.code.trim())) {
      newErrors.code = 'Account code must contain only numbers';
    }
    
    if (!formData.name.trim()) {
      newErrors.name = 'Account name is required';
    }
    
    if (!formData.account_type) {
      newErrors.account_type = 'Account type is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setLoading(true);
    try {
      const accountData = {
        ...formData,
        code: formData.code.trim(),
        name: formData.name.trim(),
      };
      
      // Remove empty fields
      Object.keys(accountData).forEach(key => {
        if (accountData[key] === '' || accountData[key] === null) {
          delete accountData[key];
        }
      });
      
      console.log('Creating account:', accountData);
      const newAccount = await accountingAPI.createAccount(accountData);
      console.log('Account created:', newAccount);
      
      // Notify parent component
      if (onAccountCreated) {
        onAccountCreated(newAccount);
      }
      
      // Close modal
      onClose();
      
    } catch (error) {
      console.error('Error creating account:', error);
      if (error.response?.data) {
        // Handle API validation errors
        const apiErrors = error.response.data;
        setErrors(apiErrors);
      } else {
        setErrors({ general: 'Failed to create account. Please try again.' });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    onClose();
  };

  if (!isOpen) return null;

  // Group account types by category for better UX
  const groupedAccountTypes = accountTypes.reduce((acc, type) => {
    if (!acc[type.category]) {
      acc[type.category] = [];
    }
    acc[type.category].push(type);
    return acc;
  }, {});

  return (
    <div className="o_action_manager">
      <div className="o_form_view o_view_controller">
        <div className="o_form_view_container">

          {/* Control Panel */}
          <div className="o_control_panel">
            <div className="o_control_panel_main">
              <div className="o_control_panel_breadcrumbs">
                <div className="o_control_panel_main_buttons">
                  <div className="o_form_buttons_view">
                    <button
                      type="button"
                      className="btn btn-primary o_form_button_save"
                      onClick={handleSubmit}
                      disabled={loading}
                    >
                      {loading ? 'Saving...' : 'Save'}
                    </button>
                    <button
                      type="button"
                      className="btn btn-secondary o_form_button_cancel"
                      onClick={handleCancel}
                      disabled={loading}
                    >
                      Discard
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Form Content */}
          <div className="o_content">
            <div className="o_form_renderer o_form_editable">
              <div className="o_form_sheet_bg">
                <div className="o_form_sheet">

                  {/* Error Display */}
                  {errors.general && (
                    <div className="alert alert-danger">
                      {errors.general}
                    </div>
                  )}

                  {/* Title Section - Matching Odoo Structure */}
                  <div>
                    <h1 style={{fontSize: '1.9rem'}}>
                      <div className="row">
                        <div className="col col-md-auto">
                          <Label htmlFor="code" size="small">Code</Label>
                          <FocusArea level="primary">
                            <input
                              type="text"
                              id="code"
                              name="code"
                              className={`form-control oe_inline odoo-input-code ${errors.code ? 'is-invalid' : ''}`}
                              value={formData.code}
                              onChange={handleInputChange}
                              placeholder="e.g. 101000"
                              maxLength="10"
                              disabled={loading}
                            />
                          </FocusArea>
                        </div>
                      </div>
                      <div className="row">
                        <div className="col col-md-8">
                          <Label htmlFor="name" size="small">Account Name</Label>
                          <FocusArea level="primary">
                            <input
                              type="text"
                              id="name"
                              name="name"
                              className="form-control odoo-input-large"
                              value={formData.name}
                              onChange={handleInputChange}
                              placeholder="e.g. Current Assets"
                              style={{width: '80% !important'}}
                              disabled={loading}
                            />
                          </FocusArea>
                        </div>
                      </div>
                    </h1>
                  </div>

                  {/* Notebook/Tabs */}
                  <div className="o_notebook">
                    <div className="tab-content">
                      <div className="tab-pane active">
                        <div className="o_group">
                          <div className="o_inner_group">

                            {/* Account Type */}
                            <div className="o_wrap_field">
                              <div className="o_cell o_wrap_label">
                                <label className="o_form_label">Type</label>
                              </div>
                              <div className="o_cell o_wrap_input">
                                <select
                                  name="account_type"
                                  className={`form-control o_input ${errors.account_type ? 'is-invalid' : ''}`}
                                  value={formData.account_type}
                                  onChange={handleInputChange}
                                  disabled={loading}
                                >
                                  {Object.entries(groupedAccountTypes).map(([category, types]) => (
                                    <optgroup key={category} label={category}>
                                      {types.map(type => (
                                        <option key={type.value} value={type.value}>
                                          {type.label}
                                        </option>
                                      ))}
                                    </optgroup>
                                  ))}
                                </select>
                              </div>
                            </div>

                            {/* Account Group */}
                            {accountGroups.length > 0 && (
                              <div className="o_wrap_field">
                                <div className="o_cell o_wrap_label">
                                  <label className="o_form_label">Account Group</label>
                                </div>
                                <div className="o_cell o_wrap_input">
                                  <select
                                    name="group"
                                    className="form-control o_input"
                                    value={formData.group}
                                    onChange={handleInputChange}
                                    disabled={loading}
                                  >
                                    <option value="">Select a group (optional)</option>
                                    {accountGroups.map(group => (
                                      <option key={group.id} value={group.id}>
                                        {group.name}
                                      </option>
                                    ))}
                                  </select>
                                </div>
                              </div>
                            )}

                            {/* Allow Reconciliation */}
                            <div className="o_wrap_field">
                              <div className="o_cell o_wrap_label">
                                <label className="o_form_label">Allow Reconciliation</label>
                              </div>
                              <div className="o_cell o_wrap_input">
                                <div className="o_field_boolean">
                                  <input
                                    type="checkbox"
                                    name="reconcile"
                                    checked={formData.reconcile}
                                    onChange={handleInputChange}
                                    disabled={loading}
                                  />
                                </div>
                              </div>
                            </div>

                            {/* Active */}
                            <div className="o_wrap_field">
                              <div className="o_cell o_wrap_label">
                                <label className="o_form_label">Active</label>
                              </div>
                              <div className="o_cell o_wrap_input">
                                <div className="o_field_boolean">
                                  <input
                                    type="checkbox"
                                    name="active"
                                    checked={formData.active}
                                    onChange={handleInputChange}
                                    disabled={loading}
                                  />
                                </div>
                              </div>
                            </div>

                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                </div>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
};

export default CreateAccountModal;
