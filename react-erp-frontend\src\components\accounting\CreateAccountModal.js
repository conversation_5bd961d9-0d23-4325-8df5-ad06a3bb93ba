import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { accountingAPI } from '../../services/api';
import Button from '../common/Button/Button';
import './CreateAccountModal.css';

const CreateAccountModal = ({ isOpen, onClose, onAccountCreated }) => {
  const { user } = useAuth();
  const [formData, setFormData] = useState({
    code: '',
    name: '',
    account_type: 'asset_current',
    reconcile: false,
    active: true,
    group: '',
    currency: ''
  });
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [accountGroups, setAccountGroups] = useState([]);

  // Account type options matching Odoo's structure
  const accountTypes = [
    { value: 'asset_receivable', label: 'Receivable', category: 'Assets' },
    { value: 'asset_cash', label: 'Bank and Cash', category: 'Assets' },
    { value: 'asset_current', label: 'Current Assets', category: 'Assets' },
    { value: 'asset_non_current', label: 'Non-current Assets', category: 'Assets' },
    { value: 'asset_prepayments', label: 'Prepayments', category: 'Assets' },
    { value: 'asset_fixed', label: 'Fixed Assets', category: 'Assets' },
    
    { value: 'liability_payable', label: 'Payable', category: 'Liabilities' },
    { value: 'liability_credit_card', label: 'Credit Card', category: 'Liabilities' },
    { value: 'liability_current', label: 'Current Liabilities', category: 'Liabilities' },
    { value: 'liability_non_current', label: 'Non-current Liabilities', category: 'Liabilities' },
    
    { value: 'equity', label: 'Equity', category: 'Equity' },
    { value: 'equity_unaffected', label: 'Current Year Earnings', category: 'Equity' },
    
    { value: 'income', label: 'Income', category: 'Income' },
    { value: 'income_other', label: 'Other Income', category: 'Income' },
    
    { value: 'expense', label: 'Expenses', category: 'Expenses' },
    { value: 'expense_depreciation', label: 'Depreciation', category: 'Expenses' },
    { value: 'expense_direct_cost', label: 'Cost of Revenue', category: 'Expenses' },
  ];

  useEffect(() => {
    if (isOpen) {
      loadAccountGroups();
      // Reset form when modal opens
      setFormData({
        code: '',
        name: '',
        account_type: 'asset_current',
        reconcile: false,
        active: true,
        group: '',
        currency: ''
      });
      setErrors({});
    }
  }, [isOpen]);

  const loadAccountGroups = async () => {
    try {
      const response = await accountingAPI.getAccountGroups();
      setAccountGroups(response.results || response);
    } catch (error) {
      console.error('Error loading account groups:', error);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.code.trim()) {
      newErrors.code = 'Account code is required';
    } else if (!/^\d+$/.test(formData.code.trim())) {
      newErrors.code = 'Account code must contain only numbers';
    }
    
    if (!formData.name.trim()) {
      newErrors.name = 'Account name is required';
    }
    
    if (!formData.account_type) {
      newErrors.account_type = 'Account type is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setLoading(true);
    try {
      const accountData = {
        ...formData,
        code: formData.code.trim(),
        name: formData.name.trim(),
      };
      
      // Remove empty fields
      Object.keys(accountData).forEach(key => {
        if (accountData[key] === '' || accountData[key] === null) {
          delete accountData[key];
        }
      });
      
      console.log('Creating account:', accountData);
      const newAccount = await accountingAPI.createAccount(accountData);
      console.log('Account created:', newAccount);
      
      // Notify parent component
      if (onAccountCreated) {
        onAccountCreated(newAccount);
      }
      
      // Close modal
      onClose();
      
    } catch (error) {
      console.error('Error creating account:', error);
      if (error.response?.data) {
        // Handle API validation errors
        const apiErrors = error.response.data;
        setErrors(apiErrors);
      } else {
        setErrors({ general: 'Failed to create account. Please try again.' });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    onClose();
  };

  if (!isOpen) return null;

  // Group account types by category for better UX
  const groupedAccountTypes = accountTypes.reduce((acc, type) => {
    if (!acc[type.category]) {
      acc[type.category] = [];
    }
    acc[type.category].push(type);
    return acc;
  }, {});

  return (
    <div className="modal-overlay">
      <div className="modal-container create-account-modal">
        <div className="modal-header">
          <h2>
            <i className="fas fa-plus-circle"></i>
            Create Account
          </h2>
          <button className="modal-close" onClick={handleCancel}>
            <i className="fas fa-times"></i>
          </button>
        </div>

        <form onSubmit={handleSubmit} className="modal-body">
          {errors.general && (
            <div className="error-message general-error">
              <i className="fas fa-exclamation-triangle"></i>
              {errors.general}
            </div>
          )}

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="code" className="required">
                Code
              </label>
              <input
                type="text"
                id="code"
                name="code"
                value={formData.code}
                onChange={handleInputChange}
                className={errors.code ? 'error' : ''}
                placeholder="e.g., 1000"
                maxLength="10"
              />
              {errors.code && (
                <span className="error-text">{errors.code}</span>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="name" className="required">
                Account Name
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className={errors.name ? 'error' : ''}
                placeholder="e.g., Cash"
                maxLength="100"
              />
              {errors.name && (
                <span className="error-text">{errors.name}</span>
              )}
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="account_type" className="required">
                Type
              </label>
              <select
                id="account_type"
                name="account_type"
                value={formData.account_type}
                onChange={handleInputChange}
                className={errors.account_type ? 'error' : ''}
              >
                {Object.entries(groupedAccountTypes).map(([category, types]) => (
                  <optgroup key={category} label={category}>
                    {types.map(type => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </optgroup>
                ))}
              </select>
              {errors.account_type && (
                <span className="error-text">{errors.account_type}</span>
              )}
            </div>

            {accountGroups.length > 0 && (
              <div className="form-group">
                <label htmlFor="group">
                  Account Group
                </label>
                <select
                  id="group"
                  name="group"
                  value={formData.group}
                  onChange={handleInputChange}
                >
                  <option value="">Select a group (optional)</option>
                  {accountGroups.map(group => (
                    <option key={group.id} value={group.id}>
                      {group.name}
                    </option>
                  ))}
                </select>
              </div>
            )}
          </div>

          <div className="form-row">
            <div className="form-group checkbox-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  name="reconcile"
                  checked={formData.reconcile}
                  onChange={handleInputChange}
                />
                <span className="checkmark"></span>
                Allow Reconciliation
                <small>Check this if the account should be reconciled (e.g., bank accounts, receivables)</small>
              </label>
            </div>

            <div className="form-group checkbox-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  name="active"
                  checked={formData.active}
                  onChange={handleInputChange}
                />
                <span className="checkmark"></span>
                Active
                <small>Uncheck to archive this account</small>
              </label>
            </div>
          </div>
        </form>

        <div className="modal-footer">
          <Button
            variant="secondary"
            onClick={handleCancel}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleSubmit}
            loading={loading}
            disabled={loading}
          >
            {loading ? 'Creating...' : 'Create Account'}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CreateAccountModal;
