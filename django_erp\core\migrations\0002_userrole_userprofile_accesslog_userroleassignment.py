# Generated by Django 4.2.21 on 2025-07-21 18:11

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('hr', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserRole',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=100, unique=True)),
                ('code', models.CharField(choices=[('admin', 'System Administrator'), ('manager', 'Manager'), ('accountant', 'Accountant'), ('sales_user', 'Sales User'), ('sales_manager', 'Sales Manager'), ('purchase_user', 'Purchase User'), ('purchase_manager', 'Purchase Manager'), ('inventory_user', 'Inventory User'), ('inventory_manager', 'Inventory Manager'), ('hr_user', 'HR User'), ('hr_manager', 'HR Manager'), ('project_user', 'Project User'), ('project_manager', 'Project Manager'), ('manufacturing_user', 'Manufacturing User'), ('manufacturing_manager', 'Manufacturing Manager'), ('readonly', 'Read Only User')], max_length=50, unique=True)),
                ('description', models.TextField(blank=True)),
                ('can_access_accounting', models.BooleanField(default=False)),
                ('can_access_sales', models.BooleanField(default=False)),
                ('can_access_purchases', models.BooleanField(default=False)),
                ('can_access_inventory', models.BooleanField(default=False)),
                ('can_access_hr', models.BooleanField(default=False)),
                ('can_access_project', models.BooleanField(default=False)),
                ('can_access_manufacturing', models.BooleanField(default=False)),
                ('can_access_crm', models.BooleanField(default=False)),
                ('can_create', models.BooleanField(default=True)),
                ('can_read', models.BooleanField(default=True)),
                ('can_update', models.BooleanField(default=True)),
                ('can_delete', models.BooleanField(default=False)),
                ('can_approve_orders', models.BooleanField(default=False)),
                ('can_manage_users', models.BooleanField(default=False)),
                ('can_view_reports', models.BooleanField(default=True)),
                ('can_export_data', models.BooleanField(default=False)),
                ('company_restricted', models.BooleanField(default=False, help_text='Restrict access to specific companies')),
                ('allowed_companies', models.ManyToManyField(blank=True, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Role',
                'verbose_name_plural': 'User Roles',
                'db_table': 'core_user_role',
            },
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('employee_id', models.CharField(blank=True, max_length=50, null=True, unique=True)),
                ('phone', models.CharField(blank=True, max_length=20)),
                ('mobile', models.CharField(blank=True, max_length=20)),
                ('language', models.CharField(default='en_US', max_length=10)),
                ('timezone', models.CharField(default='UTC', max_length=50)),
                ('avatar', models.ImageField(blank=True, null=True, upload_to='avatars/')),
                ('is_active_employee', models.BooleanField(default=True)),
                ('hire_date', models.DateField(blank=True, null=True)),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hr.hrdepartment')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Profile',
                'verbose_name_plural': 'User Profiles',
                'db_table': 'core_user_profile',
            },
        ),
        migrations.CreateModel(
            name='AccessLog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('action', models.CharField(choices=[('login', 'Login'), ('logout', 'Logout'), ('create', 'Create Record'), ('read', 'Read Record'), ('update', 'Update Record'), ('delete', 'Delete Record'), ('export', 'Export Data'), ('import', 'Import Data'), ('approve', 'Approve Record'), ('reject', 'Reject Record')], max_length=20)),
                ('model_name', models.CharField(blank=True, max_length=100)),
                ('object_id', models.CharField(blank=True, max_length=100)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('details', models.JSONField(blank=True, default=dict)),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Access Log',
                'verbose_name_plural': 'Access Logs',
                'db_table': 'core_access_log',
                'ordering': ['-create_date'],
            },
        ),
        migrations.CreateModel(
            name='UserRoleAssignment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('valid_from', models.DateField(blank=True, null=True)),
                ('valid_to', models.DateField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('role', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.userrole')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='role_assignments', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Role Assignment',
                'verbose_name_plural': 'User Role Assignments',
                'db_table': 'core_user_role_assignment',
                'unique_together': {('user', 'role', 'company')},
            },
        ),
    ]
