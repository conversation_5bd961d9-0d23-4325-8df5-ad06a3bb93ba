import React, { useState, useEffect } from 'react';
import { settingsAPI } from '../../services/api';
import Button from '../common/Button/Button';
import CreateFiscalYearModal from './CreateFiscalYearModal';
import './FiscalYearSettings.css';

const FiscalYearSettings = ({ onDataChange }) => {
  const [fiscalYears, setFiscalYears] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);

  useEffect(() => {
    loadFiscalYears();
  }, []);

  const loadFiscalYears = async () => {
    try {
      setLoading(true);
      console.log('Loading fiscal years from API...');

      const response = await settingsAPI.getFiscalYears();
      console.log('Fiscal years API response:', response);

      const fiscalYearsData = response.results || response;
      setFiscalYears(fiscalYearsData);
    } catch (error) {
      console.error('Error loading fiscal years:', error);
      // Fallback to mock data
      setFiscalYears([
        {
          id: 1,
          name: 'FY 2024',
          code: 'FY2024',
          date_start: '2024-01-01',
          date_end: '2024-12-31',
          state: 'open',
          active: true,
          is_current: true
        },
        {
          id: 2,
          name: 'FY 2023',
          code: 'FY2023',
          date_start: '2023-01-01',
          date_end: '2023-12-31',
          state: 'closed',
          active: true,
          is_current: false
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleActive = async (fiscalYearId) => {
    try {
      const fiscalYear = fiscalYears.find(fy => fy.id === fiscalYearId);
      const newActiveState = !fiscalYear.active;

      console.log('Toggling fiscal year active state:', fiscalYearId, newActiveState);

      // Update via API
      await settingsAPI.updateFiscalYear(fiscalYearId, { active: newActiveState });

      // Update local state
      setFiscalYears(prev => prev.map(fy =>
        fy.id === fiscalYearId
          ? { ...fy, active: newActiveState }
          : fy
      ));
      onDataChange();
    } catch (error) {
      console.error('Error toggling fiscal year active state:', error);
      alert('Failed to update fiscal year status. Please try again.');
    }
  };

  const handleOpenYear = async (fiscalYearId) => {
    try {
      console.log('Opening fiscal year:', fiscalYearId);
      await settingsAPI.openFiscalYear(fiscalYearId);

      // Update local state
      setFiscalYears(prev => prev.map(fy =>
        fy.id === fiscalYearId
          ? { ...fy, state: 'open' }
          : fy
      ));
      onDataChange();
    } catch (error) {
      console.error('Error opening fiscal year:', error);
      alert('Failed to open fiscal year. Please try again.');
    }
  };

  const handleCloseYear = async (fiscalYearId) => {
    try {
      console.log('Closing fiscal year:', fiscalYearId);
      await settingsAPI.closeFiscalYear(fiscalYearId);

      // Update local state
      setFiscalYears(prev => prev.map(fy =>
        fy.id === fiscalYearId
          ? { ...fy, state: 'closed' }
          : fy
      ));
      onDataChange();
    } catch (error) {
      console.error('Error closing fiscal year:', error);
      alert('Failed to close fiscal year. Please try again.');
    }
  };

  const handleFiscalYearCreated = (newFiscalYear) => {
    console.log('Fiscal year created:', newFiscalYear);
    // Add the new fiscal year to the list
    setFiscalYears(prev => [newFiscalYear, ...prev]);
    onDataChange();
  };

  const getStateColor = (state) => {
    switch (state) {
      case 'open': return 'success';
      case 'closed': return 'danger';
      case 'draft': return 'warning';
      default: return 'secondary';
    }
  };

  const filteredFiscalYears = fiscalYears.filter(fy =>
    fy.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    fy.code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="fiscal-year-settings loading">
        <div className="loading-spinner">
          <i className="fas fa-spinner fa-spin"></i>
          <p>Loading fiscal years...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="fiscal-year-settings">
      {/* Header Actions */}
      <div className="fiscal-year-header">
        <div className="search-section">
          <div className="search-box">
            <i className="fas fa-search"></i>
            <input
              type="text"
              placeholder="Search fiscal years..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        <div className="header-actions">
          <Button
            variant="primary"
            icon="fas fa-plus"
            onClick={() => setShowCreateModal(true)}
          >
            Create Fiscal Year
          </Button>
        </div>
      </div>

      {/* Fiscal Year List */}
      <div className="fiscal-year-list">
        <div className="list-header">
          <h3>
            <i className="fas fa-calendar-alt"></i>
            Fiscal Years ({filteredFiscalYears.length})
          </h3>
          <p>Manage your accounting periods and fiscal year settings</p>
        </div>

        <div className="fiscal-year-table">
          <div className="table-header">
            <div className="col-name">Fiscal Year</div>
            <div className="col-period">Period</div>
            <div className="col-state">State</div>
            <div className="col-status">Status</div>
            <div className="col-actions">Actions</div>
          </div>

          <div className="table-body">
            {filteredFiscalYears.map(fy => (
              <div key={fy.id} className={`fiscal-year-row ${!fy.active ? 'inactive' : ''}`}>
                <div className="col-name">
                  <div className="fy-info">
                    <div className="fy-icon">
                      <i className="fas fa-calendar"></i>
                    </div>
                    <div className="fy-details">
                      <h4>{fy.name}</h4>
                      <span className="fy-code">{fy.code}</span>
                      {fy.is_current && (
                        <span className="current-badge">
                          <i className="fas fa-star"></i>
                          Current
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                <div className="col-period">
                  <div className="period-info">
                    <span className="period-dates">
                      {new Date(fy.date_start).toLocaleDateString()} - {new Date(fy.date_end).toLocaleDateString()}
                    </span>
                    <span className="period-duration">
                      {Math.ceil((new Date(fy.date_end) - new Date(fy.date_start)) / (1000 * 60 * 60 * 24))} days
                    </span>
                  </div>
                </div>

                <div className="col-state">
                  <span className={`state-badge ${getStateColor(fy.state)}`}>
                    {fy.state.charAt(0).toUpperCase() + fy.state.slice(1)}
                  </span>
                </div>

                <div className="col-status">
                  <div className="status-controls">
                    <label className="toggle-switch">
                      <input
                        type="checkbox"
                        checked={fy.active}
                        onChange={() => handleToggleActive(fy.id)}
                      />
                      <span className="toggle-slider"></span>
                    </label>
                    <span className={`status-label ${fy.active ? 'active' : 'inactive'}`}>
                      {fy.active ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>

                <div className="col-actions">
                  <div className="action-buttons">
                    {fy.state === 'draft' && (
                      <button
                        className="btn-action success"
                        onClick={() => handleOpenYear(fy.id)}
                        title="Open fiscal year"
                      >
                        <i className="fas fa-play"></i>
                      </button>
                    )}
                    {fy.state === 'open' && (
                      <button
                        className="btn-action danger"
                        onClick={() => handleCloseYear(fy.id)}
                        title="Close fiscal year"
                      >
                        <i className="fas fa-lock"></i>
                      </button>
                    )}
                    <button
                      className="btn-action"
                      onClick={() => console.log('Edit fiscal year', fy.id)}
                      title="Edit fiscal year"
                    >
                      <i className="fas fa-edit"></i>
                    </button>
                    <button
                      className="btn-action danger"
                      onClick={() => console.log('Delete fiscal year', fy.id)}
                      title="Delete fiscal year"
                      disabled={fy.state === 'open'}
                    >
                      <i className="fas fa-trash"></i>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {filteredFiscalYears.length === 0 && (
          <div className="empty-state">
            <i className="fas fa-calendar-alt"></i>
            <h3>No fiscal years found</h3>
            <p>Try adjusting your search criteria or create a new fiscal year.</p>
          </div>
        )}
      </div>

      {/* Fiscal Year Info */}
      <div className="fiscal-year-info">
        <div className="info-card">
          <h4>
            <i className="fas fa-info-circle"></i>
            Fiscal Year Information
          </h4>
          <ul>
            <li>Draft fiscal years can be edited and opened</li>
            <li>Open fiscal years are active for transactions</li>
            <li>Closed fiscal years are locked and cannot be modified</li>
            <li>Only one fiscal year should be open at a time</li>
          </ul>
        </div>
      </div>

      {/* Create Fiscal Year Modal */}
      <CreateFiscalYearModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onFiscalYearCreated={handleFiscalYearCreated}
      />
    </div>
  );
};

export default FiscalYearSettings;
