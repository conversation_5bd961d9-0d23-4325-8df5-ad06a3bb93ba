import React, { useState, useEffect } from 'react';
import Button from '../common/Button/Button';

const FiscalYearSettings = ({ onDataChange }) => {
  const [fiscalYears, setFiscalYears] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadFiscalYears();
  }, []);

  const loadFiscalYears = async () => {
    try {
      setLoading(true);
      // Mock fiscal years data
      const mockFiscalYears = [
        {
          id: 1,
          name: 'FY 2024',
          start_date: '2024-01-01',
          end_date: '2024-12-31',
          active: true,
          current: true
        },
        {
          id: 2,
          name: 'FY 2023',
          start_date: '2023-01-01',
          end_date: '2023-12-31',
          active: false,
          current: false
        }
      ];
      setFiscalYears(mockFiscalYears);
    } catch (error) {
      console.error('Error loading fiscal years:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="loading-spinner">
        <i className="fas fa-spinner fa-spin"></i>
        <p>Loading fiscal years...</p>
      </div>
    );
  }

  return (
    <div className="fiscal-year-settings">
      <div className="settings-section">
        <h3>
          <i className="fas fa-calendar-alt"></i>
          Fiscal Year Management
        </h3>
        <p>Configure your accounting periods and fiscal year settings.</p>
        
        <div className="fiscal-year-list">
          {fiscalYears.map(fy => (
            <div key={fy.id} className="fiscal-year-item">
              <div className="fy-info">
                <h4>{fy.name}</h4>
                <span className="fy-period">
                  {new Date(fy.start_date).toLocaleDateString()} - {new Date(fy.end_date).toLocaleDateString()}
                </span>
              </div>
              <div className="fy-status">
                {fy.current && (
                  <span className="current-badge">Current</span>
                )}
                <span className={`status ${fy.active ? 'active' : 'inactive'}`}>
                  {fy.active ? 'Active' : 'Closed'}
                </span>
              </div>
            </div>
          ))}
        </div>
        
        <div className="section-actions">
          <Button variant="primary" icon="fas fa-plus">
            Create Fiscal Year
          </Button>
        </div>
      </div>
    </div>
  );
};

export default FiscalYearSettings;
