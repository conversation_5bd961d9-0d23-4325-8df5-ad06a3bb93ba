from django.contrib import admin
from .models import (
    AccountGroup, AccountAccount, AccountJournal, AccountMove,
    AccountMoveLine, AccountPayment, AccountPaymentTerm, AccountFiscalPosition,
    AccountIncoterms, PaymentProvider, AccountTax
)


@admin.register(AccountGroup)
class AccountGroupAdmin(admin.ModelAdmin):
    list_display = ['name', 'code_prefix_start', 'code_prefix_end', 'parent_id', 'company_id']
    list_filter = ['parent_id', 'company_id']
    search_fields = ['name', 'code_prefix_start', 'code_prefix_end']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'parent_id', 'company_id']
        }),
        ('Code Prefix', {
            'fields': ['code_prefix_start', 'code_prefix_end']
        }),
    ]


@admin.register(AccountAccount)
class AccountAccountAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'account_type', 'company_id', 'deprecated']
    list_filter = ['account_type', 'company_id', 'deprecated', 'reconcile']
    search_fields = ['code', 'name']
    fieldsets = [
        ('Basic Information', {
            'fields': ['code', 'name', 'account_type', 'company_id']
        }),
        ('Configuration', {
            'fields': ['reconcile', 'deprecated']
        }),
        ('Group', {
            'fields': ['group_id']
        }),
        ('Additional', {
            'fields': ['note']
        }),
    ]


@admin.register(AccountTax)
class AccountTaxAdmin(admin.ModelAdmin):
    list_display = ['name', 'type_tax_use', 'amount', 'amount_type', 'company_id', 'active']
    list_filter = ['type_tax_use', 'amount_type', 'company_id', 'active']
    search_fields = ['name', 'description']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'active', 'type_tax_use', 'company_id']
        }),
        ('Tax Computation', {
            'fields': ['amount_type', 'amount']
        }),
        ('Additional', {
            'fields': ['description']
        }),
    ]


@admin.register(AccountJournal)
class AccountJournalAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'type', 'company_id', 'active']
    list_filter = ['type', 'company_id', 'active']
    search_fields = ['name', 'code']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'code', 'type', 'active']
        }),
        ('Company', {
            'fields': ['company_id']
        }),
        ('Accounts', {
            'fields': ['default_account_id']
        }),
        ('Sequence', {
            'fields': ['sequence']
        }),
    ]


@admin.register(AccountMove)
class AccountMoveAdmin(admin.ModelAdmin):
    list_display = ['name', 'partner_id', 'date', 'journal_id', 'state', 'amount_total', 'approval_status']
    list_filter = ['state', 'move_type', 'journal_id', 'company_id', 'approval_status', 'date']
    search_fields = ['name', 'ref', 'partner_id__name']
    date_hierarchy = 'date'
    readonly_fields = ['amount_untaxed', 'amount_tax', 'amount_total', 'amount_residual']

    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'ref', 'date', 'journal_id', 'company_id']
        }),
        ('Partner', {
            'fields': ['partner_id']
        }),
        ('Type & State', {
            'fields': ['move_type', 'state', 'approval_status']
        }),
        ('Amounts', {
            'fields': ['amount_untaxed', 'amount_tax', 'amount_total', 'amount_residual', 'currency_id']
        }),
        ('Additional', {
            'fields': ['to_check']
        }),
        ('Approval', {
            'fields': ['approved_by', 'approved_date']
        }),
    ]

    actions = ['action_post', 'action_approve']

    def action_post(self, request, queryset):
        for move in queryset.filter(state='draft'):
            move.action_post()
        self.message_user(request, f"Posted {queryset.count()} journal entries.")
    action_post.short_description = "Post selected journal entries"

    def action_approve(self, request, queryset):
        for move in queryset.filter(approval_status='pending'):
            move.action_approve()
        self.message_user(request, f"Approved {queryset.count()} journal entries.")
    action_approve.short_description = "Approve selected journal entries"


@admin.register(AccountMoveLine)
class AccountMoveLineAdmin(admin.ModelAdmin):
    list_display = ['move_id', 'account_id', 'partner_id', 'debit', 'credit', 'balance']
    list_filter = ['account_id', 'move_id__journal_id', 'date', 'reconciled']
    search_fields = ['name', 'ref', 'partner_id__name', 'account_id__name']
    date_hierarchy = 'date'
    readonly_fields = ['balance']

    fieldsets = [
        ('Basic Information', {
            'fields': ['move_id', 'account_id', 'name', 'ref']
        }),
        ('Partner', {
            'fields': ['partner_id']
        }),
        ('Amounts', {
            'fields': ['debit', 'credit', 'balance', 'currency_id', 'amount_currency']
        }),
        ('Dates', {
            'fields': ['date', 'date_maturity']
        }),
        ('Reconciliation', {
            'fields': ['reconciled', 'full_reconcile_id']
        }),
        ('Product & Quantity', {
            'fields': ['quantity', 'price_unit', 'discount']
        }),
        ('Tax Information', {
            'fields': ['tax_ids', 'tax_line_id', 'tax_base_amount']
        }),
    ]


@admin.register(AccountPayment)
class AccountPaymentAdmin(admin.ModelAdmin):
    list_display = ['name', 'partner_id', 'amount', 'currency_id', 'payment_type', 'state', 'date']
    list_filter = ['payment_type', 'partner_type', 'state', 'journal_id', 'date']
    search_fields = ['name', 'ref', 'partner_id__name']
    date_hierarchy = 'date'

    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'ref', 'date', 'journal_id']
        }),
        ('Payment Details', {
            'fields': ['payment_type', 'partner_type', 'partner_id']
        }),
        ('Amount', {
            'fields': ['amount', 'currency_id']
        }),
        ('State', {
            'fields': ['state']
        }),
        ('Additional', {
            'fields': ['communication', 'company_id']
        }),
    ]

    actions = ['action_post', 'action_cancel']

    def action_post(self, request, queryset):
        for payment in queryset.filter(state='draft'):
            payment.action_post()
        self.message_user(request, f"Posted {queryset.count()} payments.")
    action_post.short_description = "Post selected payments"

    def action_cancel(self, request, queryset):
        for payment in queryset.filter(state='posted'):
            payment.action_cancel()
        self.message_user(request, f"Cancelled {queryset.count()} payments.")
    action_cancel.short_description = "Cancel selected payments"


@admin.register(AccountPaymentTerm)
class AccountPaymentTermAdmin(admin.ModelAdmin):
    list_display = ['name', 'active', 'company_id']
    list_filter = ['active', 'company_id']
    search_fields = ['name']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'active', 'company_id']
        }),
        ('Additional', {
            'fields': ['note']
        }),
    ]


@admin.register(AccountFiscalPosition)
class AccountFiscalPositionAdmin(admin.ModelAdmin):
    list_display = ['name', 'active', 'auto_apply', 'company_id']
    list_filter = ['active', 'auto_apply', 'company_id']
    search_fields = ['name']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'active', 'company_id']
        }),
        ('Application', {
            'fields': ['auto_apply', 'vat_required']
        }),
        ('Geographic Scope', {
            'fields': ['country_id', 'country_group_id', 'state_ids']
        }),
        ('Additional', {
            'fields': ['note']
        }),
    ]


@admin.register(AccountIncoterms)
class AccountIncotermsAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'active']
    list_filter = ['active']
    search_fields = ['name', 'code']


@admin.register(PaymentProvider)
class PaymentProviderAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'state', 'company_id']
    list_filter = ['state', 'company_id']
    search_fields = ['name', 'code']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'code', 'company_id']
        }),
        ('State', {
            'fields': ['state']
        }),
        ('Configuration', {
            'fields': ['pre_msg', 'pending_msg', 'done_msg', 'cancel_msg']
        }),
    ]
