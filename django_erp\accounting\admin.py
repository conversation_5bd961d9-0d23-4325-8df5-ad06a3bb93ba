from django.contrib import admin
from .models import (
    AccountGroup, AccountAccount, AccountJournal, AccountMove,
    AccountMoveLine, AccountPayment, AccountPaymentTerm, AccountFiscalPosition,
    AccountIncoterms, PaymentProvider, AccountTax
)


@admin.register(AccountGroup)
class AccountGroupAdmin(admin.ModelAdmin):
    list_display = ['name', 'code_prefix_start', 'code_prefix_end', 'parent', 'company']
    list_filter = ['parent', 'company']
    search_fields = ['name', 'code_prefix_start', 'code_prefix_end']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'parent', 'company']
        }),
        ('Code Prefix', {
            'fields': ['code_prefix_start', 'code_prefix_end']
        }),
    ]


@admin.register(AccountAccount)
class AccountAccountAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'account_type', 'company', 'deprecated']
    list_filter = ['account_type', 'company', 'deprecated', 'reconcile']
    search_fields = ['code', 'name']
    fieldsets = [
        ('Basic Information', {
            'fields': ['code', 'name', 'account_type', 'company']
        }),
        ('Configuration', {
            'fields': ['reconcile', 'deprecated']
        }),
        ('Group', {
            'fields': ['group']
        }),
        ('Additional', {
            'fields': ['note']
        }),
    ]


@admin.register(AccountTax)
class AccountTaxAdmin(admin.ModelAdmin):
    list_display = ['name', 'type_tax_use', 'amount', 'amount_type', 'company', 'active']
    list_filter = ['type_tax_use', 'amount_type', 'company', 'active']
    search_fields = ['name', 'description']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'active', 'type_tax_use', 'company']
        }),
        ('Tax Computation', {
            'fields': ['amount_type', 'amount']
        }),
        ('Additional', {
            'fields': ['description']
        }),
    ]


@admin.register(AccountJournal)
class AccountJournalAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'type', 'company', 'active']
    list_filter = ['type', 'company', 'active']
    search_fields = ['name', 'code']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'code', 'type', 'active']
        }),
        ('Company', {
            'fields': ['company']
        }),
        ('Accounts', {
            'fields': ['default_account']
        }),
        ('Sequence', {
            'fields': ['sequence']
        }),
    ]


@admin.register(AccountMove)
class AccountMoveAdmin(admin.ModelAdmin):
    list_display = ['name', 'partner', 'date', 'journal', 'state', 'amount_total', 'approval_status']
    list_filter = ['state', 'move_type', 'journal', 'company', 'approval_status', 'date']
    search_fields = ['name', 'ref', 'partner_id__name']
    date_hierarchy = 'date'
    readonly_fields = ['amount_untaxed', 'amount_tax', 'amount_total', 'amount_residual']

    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'ref', 'date', 'journal', 'company']
        }),
        ('Partner', {
            'fields': ['partner']
        }),
        ('Type & State', {
            'fields': ['move_type', 'state', 'approval_status']
        }),
        ('Amounts', {
            'fields': ['amount_untaxed', 'amount_tax', 'amount_total', 'amount_residual', 'currency']
        }),
        ('Additional', {
            'fields': ['to_check']
        }),
        ('Approval', {
            'fields': ['approved_by', 'approved_date']
        }),
    ]

    actions = ['action_post', 'action_approve']

    def action_post(self, request, queryset):
        for move in queryset.filter(state='draft'):
            move.action_post()
        self.message_user(request, f"Posted {queryset.count()} journal entries.")
    action_post.short_description = "Post selected journal entries"

    def action_approve(self, request, queryset):
        for move in queryset.filter(approval_status='pending'):
            move.action_approve()
        self.message_user(request, f"Approved {queryset.count()} journal entries.")
    action_approve.short_description = "Approve selected journal entries"


@admin.register(AccountMoveLine)
class AccountMoveLineAdmin(admin.ModelAdmin):
    list_display = ['move', 'account', 'partner', 'debit', 'credit', 'balance']
    list_filter = ['account', 'move_id__journal', 'date', 'reconciled']
    search_fields = ['name', 'ref', 'partner_id__name', 'account_id__name']
    date_hierarchy = 'date'
    readonly_fields = ['balance']

    fieldsets = [
        ('Basic Information', {
            'fields': ['move', 'account', 'name', 'ref']
        }),
        ('Partner', {
            'fields': ['partner']
        }),
        ('Amounts', {
            'fields': ['debit', 'credit', 'balance', 'currency', 'amount_currency']
        }),
        ('Dates', {
            'fields': ['date', 'date_maturity']
        }),
        ('Reconciliation', {
            'fields': ['reconciled', 'full_reconcile']
        }),
        ('Product & Quantity', {
            'fields': ['quantity', 'price_unit', 'discount']
        }),
        ('Tax Information', {
            'fields': ['taxs', 'tax_line', 'tax_base_amount']
        }),
    ]


@admin.register(AccountPayment)
class AccountPaymentAdmin(admin.ModelAdmin):
    list_display = ['name', 'partner', 'amount', 'currency', 'payment_type', 'state', 'date']
    list_filter = ['payment_type', 'partner_type', 'state', 'journal', 'date']
    search_fields = ['name', 'ref', 'partner_id__name']
    date_hierarchy = 'date'

    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'ref', 'date', 'journal']
        }),
        ('Payment Details', {
            'fields': ['payment_type', 'partner_type', 'partner']
        }),
        ('Amount', {
            'fields': ['amount', 'currency']
        }),
        ('State', {
            'fields': ['state']
        }),
        ('Additional', {
            'fields': ['communication', 'company']
        }),
    ]

    actions = ['action_post', 'action_cancel']

    def action_post(self, request, queryset):
        for payment in queryset.filter(state='draft'):
            payment.action_post()
        self.message_user(request, f"Posted {queryset.count()} payments.")
    action_post.short_description = "Post selected payments"

    def action_cancel(self, request, queryset):
        for payment in queryset.filter(state='posted'):
            payment.action_cancel()
        self.message_user(request, f"Cancelled {queryset.count()} payments.")
    action_cancel.short_description = "Cancel selected payments"


@admin.register(AccountPaymentTerm)
class AccountPaymentTermAdmin(admin.ModelAdmin):
    list_display = ['name', 'active', 'company']
    list_filter = ['active', 'company']
    search_fields = ['name']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'active', 'company']
        }),
        ('Additional', {
            'fields': ['note']
        }),
    ]


@admin.register(AccountFiscalPosition)
class AccountFiscalPositionAdmin(admin.ModelAdmin):
    list_display = ['name', 'active', 'auto_apply', 'company']
    list_filter = ['active', 'auto_apply', 'company']
    search_fields = ['name']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'active', 'company']
        }),
        ('Application', {
            'fields': ['auto_apply', 'vat_required']
        }),
        ('Geographic Scope', {
            'fields': ['country', 'country_group', 'states']
        }),
        ('Additional', {
            'fields': ['note']
        }),
    ]


@admin.register(AccountIncoterms)
class AccountIncotermsAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'active']
    list_filter = ['active']
    search_fields = ['name', 'code']


@admin.register(PaymentProvider)
class PaymentProviderAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'state', 'company']
    list_filter = ['state', 'company']
    search_fields = ['name', 'code']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'code', 'company']
        }),
        ('State', {
            'fields': ['state']
        }),
        ('Configuration', {
            'fields': ['pre_msg', 'pending_msg', 'done_msg', 'cancel_msg']
        }),
    ]
