# Generated by Django 4.2.21 on 2025-07-21 16:30

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('inventory', '0001_initial'),
        ('sales', '0001_initial'),
        ('purchases', '0001_initial'),
        ('core', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='HrSkill',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(help_text='Skill', max_length=255)),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='MrpBom',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('product_qty', models.DecimalField(decimal_places=4, default=1.0, help_text='Product Quantity', max_digits=20)),
                ('type', models.CharField(choices=[('normal', 'Manufacture this product'), ('phantom', 'Kit'), ('subcontract', 'Subcontracting')], default='normal', help_text='BOM Type', max_length=20)),
                ('code', models.CharField(blank=True, help_text='Reference', max_length=255)),
                ('version', models.CharField(blank=True, help_text='Version', max_length=255)),
                ('ready_to_produce', models.CharField(choices=[('all_available', 'When all components are available'), ('asap', 'When components for 1st operation are available')], default='all_available', help_text='Manufacturing Readiness', max_length=20)),
                ('active', models.BooleanField(default=True, help_text='Active')),
                ('sequence', models.IntegerField(default=1, help_text='Sequence')),
                ('consumption', models.CharField(choices=[('flexible', 'Allowed'), ('warning', 'Allowed with warning'), ('strict', 'Blocked')], default='flexible', help_text='Flexible Consumption', max_length=20)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('product', models.ForeignKey(blank=True, help_text='Product Variant', null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.product')),
                ('product_tmpl', models.ForeignKey(help_text='Product Template', on_delete=django.db.models.deletion.CASCADE, to='inventory.producttemplate')),
                ('product_uom', models.ForeignKey(help_text='Product Unit of Measure', on_delete=django.db.models.deletion.PROTECT, to='sales.productuom')),
            ],
        ),
        migrations.CreateModel(
            name='MrpRouting',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(help_text='Routing Name', max_length=255)),
                ('code', models.CharField(blank=True, help_text='Reference', max_length=50)),
                ('active', models.BooleanField(default=True, help_text='Active')),
                ('note', models.TextField(blank=True, help_text='Description')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='MrpWorkcenter',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(help_text='Work Center Name', max_length=255)),
                ('code', models.CharField(blank=True, help_text='Code', max_length=50)),
                ('active', models.BooleanField(default=True, help_text='Active')),
                ('sequence', models.IntegerField(default=1, help_text='Sequence')),
                ('color', models.IntegerField(default=0, help_text='Color Index')),
                ('time_efficiency', models.DecimalField(decimal_places=2, default=100.0, help_text='Time Efficiency (%)', max_digits=5)),
                ('capacity', models.DecimalField(decimal_places=2, default=1.0, help_text='Working Capacity', max_digits=10)),
                ('costs_hour', models.DecimalField(decimal_places=2, default=0.0, help_text='Cost per Hour', max_digits=20)),
                ('time_start', models.DecimalField(decimal_places=2, default=0.0, help_text='Setup Time (minutes)', max_digits=10)),
                ('time_stop', models.DecimalField(decimal_places=2, default=0.0, help_text='Cleanup Time (minutes)', max_digits=10)),
                ('default_capacity', models.DecimalField(decimal_places=2, default=1.0, help_text='Default Capacity', max_digits=10)),
                ('oee_target', models.DecimalField(decimal_places=2, default=90.0, help_text='OEE Target (%)', max_digits=5)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='ProductAttribute',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(help_text='Attribute Name', max_length=255)),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='QualityPoint',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(help_text='Quality Point', max_length=255)),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ProductAttributeValue',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(help_text='Attribute Value', max_length=255)),
                ('attribute', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mrp.productattribute')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='MrpUnbuild',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(default='New', help_text='Reference', max_length=255)),
                ('product_qty', models.DecimalField(decimal_places=4, default=1.0, help_text='Quantity', max_digits=20)),
                ('state', models.CharField(choices=[('draft', 'Draft'), ('done', 'Done')], default='draft', max_length=20)),
                ('bom', models.ForeignKey(blank=True, help_text='Bill of Materials', null=True, on_delete=django.db.models.deletion.SET_NULL, to='mrp.mrpbom')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('location', models.ForeignKey(help_text='Source Location', on_delete=django.db.models.deletion.PROTECT, related_name='unbuild_orders_src', to='inventory.stocklocation')),
                ('location_dest', models.ForeignKey(help_text='Destination Location', on_delete=django.db.models.deletion.PROTECT, related_name='unbuild_orders_dest', to='inventory.stocklocation')),
                ('lot', models.ForeignKey(blank=True, help_text='Lot/Serial Number', null=True, on_delete=django.db.models.deletion.SET_NULL, to='inventory.stocklot')),
                ('product', models.ForeignKey(help_text='Product to Unbuild', on_delete=django.db.models.deletion.CASCADE, to='inventory.product')),
                ('product_uom', models.ForeignKey(help_text='Unit of Measure', on_delete=django.db.models.deletion.PROTECT, to='sales.productuom')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='MrpRoutingWorkcenter',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(help_text='Operation Name', max_length=255)),
                ('sequence', models.IntegerField(default=100, help_text='Sequence')),
                ('note', models.TextField(blank=True, help_text='Description')),
                ('time_mode', models.CharField(choices=[('auto', 'Compute based on tracked time'), ('manual', 'Set duration manually')], default='auto', help_text='Duration Computation', max_length=20)),
                ('time_mode_batch', models.IntegerField(default=10, help_text='Based on')),
                ('time_cycle_manual', models.DecimalField(decimal_places=2, default=60.0, help_text='Manual Duration (minutes)', max_digits=10)),
                ('time_cycle', models.DecimalField(decimal_places=2, default=60.0, help_text='Duration (minutes)', max_digits=10)),
                ('batch', models.CharField(choices=[('no', 'Once all products are processed'), ('yes', 'Once some products are processed')], default='no', help_text='Next Operation', max_length=20)),
                ('batch_size', models.DecimalField(decimal_places=2, default=1.0, help_text='Batch Size', max_digits=10)),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('quality_points', models.ManyToManyField(blank=True, help_text='Quality Control Points', to='mrp.qualitypoint')),
                ('routing', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='operations', to='mrp.mrprouting')),
                ('skills', models.ManyToManyField(blank=True, help_text='Required Skills', to='mrp.hrskill')),
                ('workcenter', models.ForeignKey(help_text='Work Center', on_delete=django.db.models.deletion.CASCADE, to='mrp.mrpworkcenter')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='MrpProduction',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(default='New', help_text='Reference', max_length=255)),
                ('origin', models.CharField(blank=True, help_text='Source Document', max_length=255)),
                ('state', models.CharField(choices=[('draft', 'Draft'), ('confirmed', 'Confirmed'), ('progress', 'In Progress'), ('to_close', 'To Close'), ('done', 'Done'), ('cancel', 'Cancelled')], default='draft', max_length=20)),
                ('priority', models.CharField(choices=[('0', 'Not urgent'), ('1', 'Normal'), ('2', 'Urgent'), ('3', 'Very Urgent')], default='1', max_length=1)),
                ('product_qty', models.DecimalField(decimal_places=4, default=1.0, help_text='Quantity to Produce', max_digits=20)),
                ('qty_producing', models.DecimalField(decimal_places=4, default=0.0, help_text='Currently Producing', max_digits=20)),
                ('qty_produced', models.DecimalField(decimal_places=4, default=0.0, help_text='Quantity Produced', max_digits=20)),
                ('date_planned_start', models.DateTimeField(help_text='Planned Start Date')),
                ('date_planned_finished', models.DateTimeField(help_text='Planned End Date')),
                ('date_start', models.DateTimeField(blank=True, help_text='Actual Start Date', null=True)),
                ('date_finished', models.DateTimeField(blank=True, help_text='Actual End Date', null=True)),
                ('date_deadline', models.DateTimeField(blank=True, help_text='Deadline', null=True)),
                ('reservation_state', models.CharField(choices=[('confirmed', 'Waiting'), ('assigned', 'Available'), ('waiting', 'Waiting Another Move'), ('partially_available', 'Partially Available')], default='confirmed', help_text='Reservation State', max_length=20)),
                ('extra_cost', models.DecimalField(decimal_places=2, default=0.0, help_text='Extra Unit Cost', max_digits=20)),
                ('bom', models.ForeignKey(blank=True, help_text='Bill of Materials', null=True, on_delete=django.db.models.deletion.SET_NULL, to='mrp.mrpbom')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('location_dest', models.ForeignKey(help_text='Finished Products Location', on_delete=django.db.models.deletion.PROTECT, related_name='mrp_productions_dest', to='inventory.stocklocation')),
                ('location_src', models.ForeignKey(help_text='Raw Materials Location', on_delete=django.db.models.deletion.PROTECT, related_name='mrp_productions_src', to='inventory.stocklocation')),
                ('picking_type', models.ForeignKey(help_text='Operation Type', on_delete=django.db.models.deletion.PROTECT, to='purchases.stockpickingtype')),
                ('procurement_group', models.ForeignKey(blank=True, help_text='Procurement Group', null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.procurementgroup')),
                ('product', models.ForeignKey(help_text='Product to Produce', on_delete=django.db.models.deletion.CASCADE, to='inventory.product')),
                ('product_tmpl', models.ForeignKey(help_text='Product Template', on_delete=django.db.models.deletion.CASCADE, to='inventory.producttemplate')),
                ('product_uom', models.ForeignKey(help_text='Product Unit of Measure', on_delete=django.db.models.deletion.PROTECT, to='sales.productuom')),
                ('routing', models.ForeignKey(blank=True, help_text='Routing', null=True, on_delete=django.db.models.deletion.SET_NULL, to='mrp.mrprouting')),
                ('user', models.ForeignKey(blank=True, help_text='Responsible', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='MrpByproduct',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('product_qty', models.DecimalField(decimal_places=4, default=1.0, help_text='Quantity', max_digits=20)),
                ('sequence', models.IntegerField(default=1, help_text='Sequence')),
                ('cost_share', models.DecimalField(decimal_places=2, default=0.0, help_text='Cost Share (%)', max_digits=5)),
                ('bom', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='byproducts', to='mrp.mrpbom')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('operation', models.ForeignKey(blank=True, help_text='Produced in Operation', null=True, on_delete=django.db.models.deletion.SET_NULL, to='mrp.mrproutingworkcenter')),
                ('product', models.ForeignKey(help_text='Byproduct', on_delete=django.db.models.deletion.CASCADE, to='inventory.product')),
                ('product_uom', models.ForeignKey(help_text='Unit of Measure', on_delete=django.db.models.deletion.PROTECT, to='sales.productuom')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='MrpBomLine',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('product_qty', models.DecimalField(decimal_places=4, default=1.0, help_text='Quantity', max_digits=20)),
                ('sequence', models.IntegerField(default=1, help_text='Sequence')),
                ('manual_consumption', models.BooleanField(default=False, help_text='Manual Consumption')),
                ('attribute_values', models.ManyToManyField(blank=True, help_text='Apply on Variants', to='mrp.productattributevalue')),
                ('bom', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bom_lines', to='mrp.mrpbom')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('operation', models.ForeignKey(blank=True, help_text='Consumed in Operation', null=True, on_delete=django.db.models.deletion.SET_NULL, to='mrp.mrproutingworkcenter')),
                ('product', models.ForeignKey(help_text='Component', on_delete=django.db.models.deletion.CASCADE, to='inventory.product')),
                ('product_uom', models.ForeignKey(help_text='Unit of Measure', on_delete=django.db.models.deletion.PROTECT, to='sales.productuom')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddField(
            model_name='mrpbom',
            name='routing',
            field=models.ForeignKey(blank=True, help_text='Manufacturing Routing', null=True, on_delete=django.db.models.deletion.SET_NULL, to='mrp.mrprouting'),
        ),
        migrations.AddField(
            model_name='mrpbom',
            name='subcontractors',
            field=models.ManyToManyField(blank=True, help_text='Subcontractors', to='core.partner'),
        ),
        migrations.AddField(
            model_name='mrpbom',
            name='write_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='MrpWorkorder',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(help_text='Work Order Name', max_length=255)),
                ('state', models.CharField(choices=[('pending', 'Waiting for another WO'), ('waiting', 'Waiting for components'), ('ready', 'Ready'), ('progress', 'In Progress'), ('done', 'Finished'), ('cancel', 'Cancelled')], default='pending', max_length=20)),
                ('qty_production', models.DecimalField(decimal_places=4, default=1.0, help_text='Original Production Quantity', max_digits=20)),
                ('qty_produced', models.DecimalField(decimal_places=4, default=0.0, help_text='Quantity Produced', max_digits=20)),
                ('qty_producing', models.DecimalField(decimal_places=4, default=0.0, help_text='Currently Producing', max_digits=20)),
                ('qty_remaining', models.DecimalField(decimal_places=4, default=0.0, help_text='Quantity Remaining', max_digits=20)),
                ('date_planned_start', models.DateTimeField(blank=True, help_text='Planned Start', null=True)),
                ('date_planned_finished', models.DateTimeField(blank=True, help_text='Planned End', null=True)),
                ('date_start', models.DateTimeField(blank=True, help_text='Actual Start', null=True)),
                ('date_finished', models.DateTimeField(blank=True, help_text='Actual End', null=True)),
                ('duration_expected', models.DecimalField(decimal_places=2, default=0.0, help_text='Expected Duration (minutes)', max_digits=10)),
                ('duration', models.DecimalField(decimal_places=2, default=0.0, help_text='Real Duration (minutes)', max_digits=10)),
                ('duration_unit', models.DecimalField(decimal_places=2, default=0.0, help_text='Duration Per Unit (minutes)', max_digits=10)),
                ('duration_percent', models.DecimalField(decimal_places=2, default=0.0, help_text='Duration Deviation (%)', max_digits=5)),
                ('costs_hour', models.DecimalField(decimal_places=2, default=0.0, help_text='Cost per Hour', max_digits=20)),
                ('note', models.TextField(blank=True, help_text='Instructions')),
                ('worksheet_type', models.CharField(choices=[('pdf', 'PDF'), ('google_slide', 'Google Slide'), ('text', 'Text')], default='text', help_text='Worksheet Type', max_length=20)),
                ('worksheet', models.TextField(blank=True, help_text='Worksheet Content')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('operation', models.ForeignKey(help_text='Operation', on_delete=django.db.models.deletion.CASCADE, to='mrp.mrproutingworkcenter')),
                ('production', models.ForeignKey(help_text='Manufacturing Order', on_delete=django.db.models.deletion.CASCADE, related_name='workorders', to='mrp.mrpproduction')),
                ('workcenter', models.ForeignKey(help_text='Work Center', on_delete=django.db.models.deletion.CASCADE, to='mrp.mrpworkcenter')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'indexes': [models.Index(fields=['production', 'state'], name='mrp_mrpwork_product_e2ad6d_idx'), models.Index(fields=['workcenter', 'state'], name='mrp_mrpwork_workcen_d474c1_idx'), models.Index(fields=['date_planned_start'], name='mrp_mrpwork_date_pl_5348a0_idx')],
            },
        ),
        migrations.AddConstraint(
            model_name='mrpworkorder',
            constraint=models.CheckConstraint(check=models.Q(('qty_production__gt', 0)), name='mrp_workorder_qty_positive'),
        ),
        migrations.AddIndex(
            model_name='mrpworkcenter',
            index=models.Index(fields=['name'], name='mrp_mrpwork_name_51b81f_idx'),
        ),
        migrations.AddIndex(
            model_name='mrpworkcenter',
            index=models.Index(fields=['active'], name='mrp_mrpwork_active_f9c49d_idx'),
        ),
        migrations.AddConstraint(
            model_name='mrpworkcenter',
            constraint=models.CheckConstraint(check=models.Q(('capacity__gt', 0)), name='mrp_workcenter_capacity_positive'),
        ),
        migrations.AddConstraint(
            model_name='mrpworkcenter',
            constraint=models.CheckConstraint(check=models.Q(('time_efficiency__gt', 0)), name='mrp_workcenter_efficiency_positive'),
        ),
        migrations.AddIndex(
            model_name='mrpunbuild',
            index=models.Index(fields=['name'], name='mrp_mrpunbu_name_97422b_idx'),
        ),
        migrations.AddIndex(
            model_name='mrpunbuild',
            index=models.Index(fields=['state'], name='mrp_mrpunbu_state_fce341_idx'),
        ),
        migrations.AddIndex(
            model_name='mrpunbuild',
            index=models.Index(fields=['product'], name='mrp_mrpunbu_product_375089_idx'),
        ),
        migrations.AddIndex(
            model_name='mrproutingworkcenter',
            index=models.Index(fields=['routing', 'sequence'], name='mrp_mrprout_routing_2d3e66_idx'),
        ),
        migrations.AddIndex(
            model_name='mrproutingworkcenter',
            index=models.Index(fields=['workcenter'], name='mrp_mrprout_workcen_b778cc_idx'),
        ),
        migrations.AddConstraint(
            model_name='mrproutingworkcenter',
            constraint=models.CheckConstraint(check=models.Q(('time_cycle__gt', 0)), name='mrp_routing_workcenter_time_positive'),
        ),
        migrations.AddIndex(
            model_name='mrprouting',
            index=models.Index(fields=['name'], name='mrp_mrprout_name_faa691_idx'),
        ),
        migrations.AddIndex(
            model_name='mrprouting',
            index=models.Index(fields=['active'], name='mrp_mrprout_active_f3bc52_idx'),
        ),
        migrations.AddIndex(
            model_name='mrpproduction',
            index=models.Index(fields=['name'], name='mrp_mrpprod_name_c1f75f_idx'),
        ),
        migrations.AddIndex(
            model_name='mrpproduction',
            index=models.Index(fields=['state'], name='mrp_mrpprod_state_44d106_idx'),
        ),
        migrations.AddIndex(
            model_name='mrpproduction',
            index=models.Index(fields=['product'], name='mrp_mrpprod_product_d414ea_idx'),
        ),
        migrations.AddIndex(
            model_name='mrpproduction',
            index=models.Index(fields=['date_planned_start'], name='mrp_mrpprod_date_pl_25ee3c_idx'),
        ),
        migrations.AddIndex(
            model_name='mrpproduction',
            index=models.Index(fields=['priority', 'date_planned_start'], name='mrp_mrpprod_priorit_ab8601_idx'),
        ),
        migrations.AddConstraint(
            model_name='mrpproduction',
            constraint=models.CheckConstraint(check=models.Q(('product_qty__gt', 0)), name='mrp_production_qty_positive'),
        ),
        migrations.AddIndex(
            model_name='mrpbyproduct',
            index=models.Index(fields=['bom'], name='mrp_mrpbypr_bom_id_c6cf04_idx'),
        ),
        migrations.AddIndex(
            model_name='mrpbyproduct',
            index=models.Index(fields=['product'], name='mrp_mrpbypr_product_71c8cf_idx'),
        ),
        migrations.AddConstraint(
            model_name='mrpbyproduct',
            constraint=models.CheckConstraint(check=models.Q(('product_qty__gt', 0)), name='mrp_byproduct_qty_positive'),
        ),
        migrations.AddIndex(
            model_name='mrpbomline',
            index=models.Index(fields=['bom', 'sequence'], name='mrp_mrpboml_bom_id_6ea6df_idx'),
        ),
        migrations.AddIndex(
            model_name='mrpbomline',
            index=models.Index(fields=['product'], name='mrp_mrpboml_product_ba57f8_idx'),
        ),
        migrations.AddConstraint(
            model_name='mrpbomline',
            constraint=models.CheckConstraint(check=models.Q(('product_qty__gt', 0)), name='mrp_bom_line_qty_positive'),
        ),
        migrations.AddIndex(
            model_name='mrpbom',
            index=models.Index(fields=['product_tmpl', 'active'], name='mrp_mrpbom_product_d1a898_idx'),
        ),
        migrations.AddIndex(
            model_name='mrpbom',
            index=models.Index(fields=['product', 'active'], name='mrp_mrpbom_product_79805f_idx'),
        ),
        migrations.AddIndex(
            model_name='mrpbom',
            index=models.Index(fields=['type'], name='mrp_mrpbom_type_bd0d61_idx'),
        ),
        migrations.AddConstraint(
            model_name='mrpbom',
            constraint=models.CheckConstraint(check=models.Q(('product_qty__gt', 0)), name='mrp_bom_qty_positive'),
        ),
    ]
