from django.db import models
from django.contrib.auth.models import User, Group, Permission
from django.core.exceptions import ValidationError
from django.utils import timezone
from decimal import Decimal
import uuid

class BaseModel(models.Model):
    """Base model with common fields for all ERP models"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)
    create_uid = models.ForeignKey(User, on_delete=models.PROTECT, related_name='%(class)s_created')
    write_uid = models.ForeignKey(User, on_delete=models.PROTECT, related_name='%(class)s_modified')
    active = models.BooleanField(default=True)

    class Meta:
        abstract = True

class Company(BaseModel):
    """Company model - equivalent to res.company in Odoo"""
    name = models.CharField(max_length=255)
    code = models.CharField(max_length=10, unique=True)
    currency = models.ForeignKey('Currency', on_delete=models.PROTECT)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True)
    email = models.EmailField(blank=True)
    phone = models.CharField(max_length=50, blank=True)
    website = models.URLField(blank=True)
    vat = models.CharField(max_length=50, blank=True, help_text="Tax ID")

    # Address fields
    street = models.CharField(max_length=255, blank=True)
    street2 = models.CharField(max_length=255, blank=True)
    city = models.CharField(max_length=100, blank=True)
    state = models.ForeignKey('CountryState', on_delete=models.SET_NULL, null=True, blank=True)
    zip = models.CharField(max_length=20, blank=True)
    country = models.ForeignKey('Country', on_delete=models.PROTECT)

    class Meta:
        verbose_name_plural = "Companies"

    def __str__(self):
        return self.name

class Country(BaseModel):
    """Country model - equivalent to res.country in Odoo"""
    name = models.CharField(max_length=255)
    code = models.CharField(max_length=2, unique=True, help_text="ISO 2-letter code")
    phone_code = models.IntegerField(default=0, help_text="International dialing code")

    class Meta:
        verbose_name_plural = "Countries"

    def __str__(self):
        return self.name


class CountryGroup(BaseModel):
    """Country Group model - equivalent to res.country.group in Odoo"""
    name = models.CharField(max_length=255)
    countries = models.ManyToManyField(Country, blank=True)

    def __str__(self):
        return self.name


class CountryState(BaseModel):
    """State/Province model - equivalent to res.country.state in Odoo"""
    name = models.CharField(max_length=255)
    code = models.CharField(max_length=10)
    country = models.ForeignKey(Country, on_delete=models.CASCADE)

    class Meta:
        unique_together = [['code', 'country']]

    def __str__(self):
        return f"{self.name} ({self.country.name})"


# Keep State as an alias for backward compatibility
State = CountryState

class Currency(BaseModel):
    """Currency model - equivalent to res.currency in Odoo"""
    name = models.CharField(max_length=3, unique=True, help_text="ISO 4217 currency code")
    symbol = models.CharField(max_length=10)
    decimal_places = models.IntegerField(default=2)
    full_name = models.CharField(max_length=100, blank=True)
    position = models.CharField(max_length=10, choices=[('before', 'Before'), ('after', 'After')], default='before')
    currency_unit_label = models.CharField(max_length=50, blank=True)
    currency_subunit_label = models.CharField(max_length=50, blank=True)
    rounding = models.DecimalField(max_digits=12, decimal_places=6, default=0.01)
    active = models.BooleanField(default=True)

    class Meta:
        verbose_name_plural = "Currencies"

    def __str__(self):
        return f"{self.name} ({self.symbol})"

class Partner(BaseModel):
    """Partner model - equivalent to res.partner in Odoo"""
    PARTNER_TYPE_CHOICES = [
        ('contact', 'Contact'),
        ('invoice', 'Invoice Address'),
        ('delivery', 'Delivery Address'),
        ('other', 'Other Address'),
    ]

    name = models.CharField(max_length=255)
    display_name = models.CharField(max_length=255, blank=True)
    ref = models.CharField(max_length=50, blank=True, help_text="Internal Reference")

    # Contact information
    email = models.EmailField(blank=True)
    phone = models.CharField(max_length=50, blank=True)
    mobile = models.CharField(max_length=50, blank=True)
    website = models.URLField(blank=True)

    # Address fields
    street = models.CharField(max_length=255, blank=True)
    street2 = models.CharField(max_length=255, blank=True)
    city = models.CharField(max_length=100, blank=True)
    state = models.ForeignKey(CountryState, on_delete=models.SET_NULL, null=True, blank=True)
    zip = models.CharField(max_length=20, blank=True)
    country = models.ForeignKey(Country, on_delete=models.PROTECT, null=True, blank=True)

    # Business fields
    is_company = models.BooleanField(default=False)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='children')
    # Note: child_ids is handled by the reverse relation of parent_id

    # Customer/Vendor flags
    customer_rank = models.IntegerField(default=0)
    supplier_rank = models.IntegerField(default=0)

    # Financial fields
    credit_limit = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)

    # Tax information
    vat = models.CharField(max_length=50, blank=True, help_text="Tax ID")

    # Credit management
    credit_limit = models.DecimalField(max_digits=20, decimal_places=2, default=0.0,
                                     help_text="Credit Limit")

    # Type and category
    partner_type = models.CharField(max_length=20, choices=PARTNER_TYPE_CHOICES, default='contact')
    category = models.ManyToManyField('PartnerCategory', blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['email']),
            models.Index(fields=['customer_rank']),
            models.Index(fields=['supplier_rank']),
        ]

    def __str__(self):
        return self.name

    @property
    def is_customer(self):
        return self.customer_rank > 0

    @property
    def is_supplier(self):
        return self.supplier_rank > 0

class PartnerCategory(BaseModel):
    """Partner Category model - equivalent to res.partner.category in Odoo"""
    name = models.CharField(max_length=255)
    color = models.IntegerField(default=0)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True)

    class Meta:
        verbose_name_plural = "Partner Categories"

    def __str__(self):
        return self.name


# ===== USER ROLE AND PERMISSION MANAGEMENT =====

class UserProfile(BaseModel):
    """
    Extended user profile with additional ERP-specific fields
    """
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    employee_id = models.CharField(max_length=50, unique=True, null=True, blank=True)
    phone = models.CharField(max_length=20, blank=True)
    mobile = models.CharField(max_length=20, blank=True)

    # Company and department
    company = models.ForeignKey('Company', on_delete=models.PROTECT, null=True, blank=True)
    department = models.ForeignKey('hr.HrDepartment', on_delete=models.SET_NULL, null=True, blank=True)

    # Profile settings
    language = models.CharField(max_length=10, default='en_US')
    timezone = models.CharField(max_length=50, default='UTC')

    # Avatar
    avatar = models.ImageField(upload_to='avatars/', null=True, blank=True)

    # Status
    is_active_employee = models.BooleanField(default=True)
    hire_date = models.DateField(null=True, blank=True)

    class Meta:
        db_table = 'core_user_profile'
        verbose_name = 'User Profile'
        verbose_name_plural = 'User Profiles'

    def __str__(self):
        return f"{self.user.get_full_name() or self.user.username} Profile"


class UserRole(BaseModel):
    """
    Custom roles for ERP system with specific permissions
    """
    ROLE_TYPES = [
        ('admin', 'System Administrator'),
        ('manager', 'Manager'),
        ('accountant', 'Accountant'),
        ('sales_user', 'Sales User'),
        ('sales_manager', 'Sales Manager'),
        ('purchase_user', 'Purchase User'),
        ('purchase_manager', 'Purchase Manager'),
        ('inventory_user', 'Inventory User'),
        ('inventory_manager', 'Inventory Manager'),
        ('hr_user', 'HR User'),
        ('hr_manager', 'HR Manager'),
        ('project_user', 'Project User'),
        ('project_manager', 'Project Manager'),
        ('manufacturing_user', 'Manufacturing User'),
        ('manufacturing_manager', 'Manufacturing Manager'),
        ('readonly', 'Read Only User'),
    ]

    name = models.CharField(max_length=100, unique=True)
    code = models.CharField(max_length=50, unique=True, choices=ROLE_TYPES)
    description = models.TextField(blank=True)

    # Module access permissions
    can_access_accounting = models.BooleanField(default=False)
    can_access_sales = models.BooleanField(default=False)
    can_access_purchases = models.BooleanField(default=False)
    can_access_inventory = models.BooleanField(default=False)
    can_access_hr = models.BooleanField(default=False)
    can_access_project = models.BooleanField(default=False)
    can_access_manufacturing = models.BooleanField(default=False)
    can_access_crm = models.BooleanField(default=False)

    # CRUD permissions
    can_create = models.BooleanField(default=True)
    can_read = models.BooleanField(default=True)
    can_update = models.BooleanField(default=True)
    can_delete = models.BooleanField(default=False)

    # Special permissions
    can_approve_orders = models.BooleanField(default=False)
    can_manage_users = models.BooleanField(default=False)
    can_view_reports = models.BooleanField(default=True)
    can_export_data = models.BooleanField(default=False)

    # Company restrictions
    company_restricted = models.BooleanField(default=False, help_text="Restrict access to specific companies")
    allowed_companies = models.ManyToManyField('Company', blank=True)

    class Meta:
        db_table = 'core_user_role'
        verbose_name = 'User Role'
        verbose_name_plural = 'User Roles'

    def __str__(self):
        return self.name


class UserRoleAssignment(BaseModel):
    """
    Assignment of roles to users with optional company/date restrictions
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='role_assignments')
    role = models.ForeignKey(UserRole, on_delete=models.CASCADE)
    company = models.ForeignKey('Company', on_delete=models.CASCADE, null=True, blank=True)

    # Date restrictions
    valid_from = models.DateField(null=True, blank=True)
    valid_to = models.DateField(null=True, blank=True)

    # Status
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'core_user_role_assignment'
        verbose_name = 'User Role Assignment'
        verbose_name_plural = 'User Role Assignments'
        unique_together = ['user', 'role', 'company']

    def __str__(self):
        company_str = f" ({self.company.name})" if self.company else ""
        return f"{self.user.username} - {self.role.name}{company_str}"


class AccessLog(BaseModel):
    """
    Log user access and actions for audit purposes
    """
    ACTION_TYPES = [
        ('login', 'Login'),
        ('logout', 'Logout'),
        ('create', 'Create Record'),
        ('read', 'Read Record'),
        ('update', 'Update Record'),
        ('delete', 'Delete Record'),
        ('export', 'Export Data'),
        ('import', 'Import Data'),
        ('approve', 'Approve Record'),
        ('reject', 'Reject Record'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    action = models.CharField(max_length=20, choices=ACTION_TYPES)
    model_name = models.CharField(max_length=100, blank=True)
    object_id = models.CharField(max_length=100, blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    details = models.JSONField(default=dict, blank=True)

    class Meta:
        db_table = 'core_access_log'
        verbose_name = 'Access Log'
        verbose_name_plural = 'Access Logs'
        ordering = ['-create_date']

    def __str__(self):
        return f"{self.user.username} - {self.action} - {self.create_date}"
