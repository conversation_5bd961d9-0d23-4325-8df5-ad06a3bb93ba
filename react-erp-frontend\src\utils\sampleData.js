// Sample account data for testing
export const sampleAccounts = [
  {
    code: '1000',
    name: 'Cash',
    note: 'Cash on hand and petty cash funds',
    account_type: 'asset_cash',
    reconcile: true,
    active: true,
    balance: 15750.00
  },
  {
    code: '1100',
    name: 'Bank Account',
    note: 'Main business checking account',
    account_type: 'asset_cash',
    reconcile: true,
    active: true,
    balance: 45230.50
  },
  {
    code: '1200',
    name: 'Accounts Receivable',
    note: 'Money owed by customers for goods or services delivered',
    account_type: 'asset_receivable',
    reconcile: true,
    active: true,
    balance: 12500.00
  },
  {
    code: '1300',
    name: 'Inventory',
    account_type: 'asset_current',
    reconcile: false,
    active: true,
    balance: 8750.25
  },
  {
    code: '1500',
    name: 'Equipment',
    account_type: 'asset_fixed',
    reconcile: false,
    active: true,
    balance: 25000.00
  },
  {
    code: '2000',
    name: 'Accounts Payable',
    account_type: 'liability_payable',
    reconcile: true,
    active: true,
    balance: -8500.00
  },
  {
    code: '3000',
    name: 'Owner\'s Equity',
    account_type: 'equity',
    reconcile: false,
    active: true,
    balance: -50000.00
  },
  {
    code: '4000',
    name: 'Sales Revenue',
    account_type: 'income',
    reconcile: false,
    active: true,
    balance: -45750.75
  },
  {
    code: '5000',
    name: 'Cost of Goods Sold',
    account_type: 'expense_direct_cost',
    reconcile: false,
    active: true,
    balance: 18500.00
  },
  {
    code: '6000',
    name: 'Operating Expenses',
    account_type: 'expense',
    reconcile: false,
    active: true,
    balance: 12750.50
  }
];

export const createSampleAccounts = async (accountingAPI) => {
  console.log('🆕 Creating sample accounts...');
  const createdAccounts = [];
  
  for (const accountData of sampleAccounts) {
    try {
      console.log(`📝 Creating account: ${accountData.code} - ${accountData.name}`);
      const created = await accountingAPI.createAccount(accountData);
      createdAccounts.push(created);
      console.log(`✅ Created: ${created.code} - ${created.name}`);
    } catch (error) {
      console.error(`❌ Failed to create ${accountData.code}:`, error.response?.data || error.message);
    }
  }
  
  console.log(`🎉 Created ${createdAccounts.length} sample accounts`);
  return createdAccounts;
};
