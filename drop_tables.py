from django.db import connection

cursor = connection.cursor()
cursor.execute("SELECT tablename FROM pg_tables WHERE schemaname = 'public' AND tablename NOT LIKE 'pg_%'")
tables = [row[0] for row in cursor.fetchall()]

if tables:
    table_list = ', '.join(f'"{table}"' for table in tables)
    cursor.execute(f'DROP TABLE IF EXISTS {table_list} CASCADE')
    print(f'Dropped {len(tables)} tables')
else:
    print('No tables to drop')
