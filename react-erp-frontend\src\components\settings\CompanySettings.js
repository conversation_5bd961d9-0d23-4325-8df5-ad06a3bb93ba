import React, { useState, useEffect } from 'react';
import { settingsAPI } from '../../services/api';
import Button from '../common/Button/Button';
import './CompanySettings.css';

const CompanySettings = ({ onDataChange }) => {
  const [company, setCompany] = useState({
    name: '',
    email: '',
    phone: '',
    mobile: '',
    website: '',
    vat: '',
    company_registry: '',
    street: '',
    street2: '',
    city: '',
    state: '',
    zip: '',
    country: '',
    currency: '',
    logo: null,
    favicon: null
  });
  const [countries, setCountries] = useState([]);
  const [currencies, setCurrencies] = useState([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState({});

  useEffect(() => {
    loadCompanyData();
    loadCountries();
    loadCurrencies();
  }, []);

  const loadCompanyData = async () => {
    try {
      setLoading(true);
      console.log('Loading company data from API...');

      const response = await settingsAPI.getCompany();
      console.log('Company API response:', response);

      setCompany({
        id: response.id || '',
        name: response.name || '',
        email: response.email || '',
        phone: response.phone || '',
        mobile: response.mobile || '',
        website: response.website || '',
        vat: response.vat || '',
        company_registry: response.company_registry || '',
        street: response.street || '',
        street2: response.street2 || '',
        city: response.city || '',
        state: response.state || '',
        zip: response.zip || '',
        country: response.country || '',
        currency: response.currency || ''
      });
    } catch (error) {
      console.error('Error loading company data:', error);
      // Fallback to mock data if API fails
      setCompany({
        id: 'd7a75b6b-dea7-479f-995b-a0c194d79377',
        name: 'Demo ERP Company',
        email: '<EMAIL>',
        phone: '******-0123',
        mobile: '******-0124',
        website: 'https://democompany.com',
        vat: 'US123456789',
        company_registry: 'REG123456',
        street: '123 Business Street',
        street2: 'Suite 100',
        city: 'New York',
        state: 'NY',
        zip: '10001',
        country: 'US',
        currency: 'USD'
      });
    } finally {
      setLoading(false);
    }
  };

  const loadCountries = async () => {
    try {
      console.log('Loading countries from API...');
      const response = await settingsAPI.getCountries();
      console.log('Countries API response:', response);

      const countriesData = response.results || response;
      setCountries(countriesData);
    } catch (error) {
      console.error('Error loading countries:', error);
      // Fallback to mock data
      setCountries([
        { code: 'US', name: 'United States' },
        { code: 'CA', name: 'Canada' },
        { code: 'GB', name: 'United Kingdom' },
        { code: 'DE', name: 'Germany' },
        { code: 'FR', name: 'France' },
        { code: 'AU', name: 'Australia' },
        { code: 'IN', name: 'India' },
        { code: 'CN', name: 'China' },
        { code: 'JP', name: 'Japan' }
      ]);
    }
  };

  const loadCurrencies = async () => {
    try {
      console.log('Loading currencies from API...');
      const response = await settingsAPI.getCurrencies();
      console.log('Currencies API response:', response);

      const currenciesData = response.results || response;
      setCurrencies(currenciesData);
    } catch (error) {
      console.error('Error loading currencies:', error);
      // Fallback to mock data
      setCurrencies([
        { code: 'USD', name: 'US Dollar', symbol: '$' },
        { code: 'EUR', name: 'Euro', symbol: '€' },
        { code: 'GBP', name: 'British Pound', symbol: '£' },
        { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$' },
        { code: 'AUD', name: 'Australian Dollar', symbol: 'A$' },
        { code: 'INR', name: 'Indian Rupee', symbol: '₹' },
        { code: 'CNY', name: 'Chinese Yuan', symbol: '¥' },
        { code: 'JPY', name: 'Japanese Yen', symbol: '¥' }
      ]);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setCompany(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
    
    // Notify parent of changes
    onDataChange();
  };

  const handleFileChange = (e) => {
    const { name, files } = e.target;
    if (files && files[0]) {
      setCompany(prev => ({
        ...prev,
        [name]: files[0]
      }));
      onDataChange();
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!company.name.trim()) {
      newErrors.name = 'Company name is required';
    }
    
    if (!company.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(company.email)) {
      newErrors.email = 'Email is invalid';
    }
    
    if (!company.country) {
      newErrors.country = 'Country is required';
    }
    
    if (!company.currency) {
      newErrors.currency = 'Currency is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    setSaving(true);
    try {
      console.log('Saving company data:', company);

      const response = await settingsAPI.updateCompany(company);
      console.log('Company update response:', response);

      // Update local state with response data
      setCompany(prev => ({
        ...prev,
        ...response
      }));

      // Show success message
      alert('Company settings saved successfully!');

    } catch (error) {
      console.error('Error saving company data:', error);
      if (error.response?.data) {
        // Handle API validation errors
        const apiErrors = error.response.data;
        setErrors(apiErrors);
      } else {
        alert('Failed to save company settings. Please try again.');
      }
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="company-settings loading">
        <div className="loading-spinner">
          <i className="fas fa-spinner fa-spin"></i>
          <p>Loading company settings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="company-settings">
      <div className="settings-form">
        {/* Basic Information */}
        <div className="form-section">
          <h3>
            <i className="fas fa-info-circle"></i>
            Basic Information
          </h3>
          
          <div className="form-row">
            <div className="form-group">
              <label htmlFor="name" className="required">Company Name</label>
              <input
                type="text"
                id="name"
                name="name"
                value={company.name}
                onChange={handleInputChange}
                className={errors.name ? 'error' : ''}
                placeholder="Enter company name"
              />
              {errors.name && <span className="error-text">{errors.name}</span>}
            </div>
            
            <div className="form-group">
              <label htmlFor="email" className="required">Email</label>
              <input
                type="email"
                id="email"
                name="email"
                value={company.email}
                onChange={handleInputChange}
                className={errors.email ? 'error' : ''}
                placeholder="<EMAIL>"
              />
              {errors.email && <span className="error-text">{errors.email}</span>}
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="phone">Phone</label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={company.phone}
                onChange={handleInputChange}
                placeholder="******-0123"
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="mobile">Mobile</label>
              <input
                type="tel"
                id="mobile"
                name="mobile"
                value={company.mobile}
                onChange={handleInputChange}
                placeholder="******-0124"
              />
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="website">Website</label>
              <input
                type="url"
                id="website"
                name="website"
                value={company.website}
                onChange={handleInputChange}
                placeholder="https://company.com"
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="vat">VAT Number</label>
              <input
                type="text"
                id="vat"
                name="vat"
                value={company.vat}
                onChange={handleInputChange}
                placeholder="VAT123456789"
              />
            </div>
          </div>
        </div>

        {/* Address Information */}
        <div className="form-section">
          <h3>
            <i className="fas fa-map-marker-alt"></i>
            Address Information
          </h3>
          
          <div className="form-row">
            <div className="form-group full-width">
              <label htmlFor="street">Street</label>
              <input
                type="text"
                id="street"
                name="street"
                value={company.street}
                onChange={handleInputChange}
                placeholder="123 Business Street"
              />
            </div>
          </div>

          <div className="form-row">
            <div className="form-group full-width">
              <label htmlFor="street2">Street 2</label>
              <input
                type="text"
                id="street2"
                name="street2"
                value={company.street2}
                onChange={handleInputChange}
                placeholder="Suite 100"
              />
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="city">City</label>
              <input
                type="text"
                id="city"
                name="city"
                value={company.city}
                onChange={handleInputChange}
                placeholder="New York"
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="state">State/Province</label>
              <input
                type="text"
                id="state"
                name="state"
                value={company.state}
                onChange={handleInputChange}
                placeholder="NY"
              />
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="zip">ZIP/Postal Code</label>
              <input
                type="text"
                id="zip"
                name="zip"
                value={company.zip}
                onChange={handleInputChange}
                placeholder="10001"
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="country" className="required">Country</label>
              <select
                id="country"
                name="country"
                value={company.country}
                onChange={handleInputChange}
                className={errors.country ? 'error' : ''}
              >
                <option value="">Select a country</option>
                {countries.map(country => (
                  <option key={country.code} value={country.code}>
                    {country.name}
                  </option>
                ))}
              </select>
              {errors.country && <span className="error-text">{errors.country}</span>}
            </div>
          </div>
        </div>

        {/* Localization */}
        <div className="form-section">
          <h3>
            <i className="fas fa-globe"></i>
            Localization
          </h3>
          
          <div className="form-row">
            <div className="form-group">
              <label htmlFor="currency" className="required">Default Currency</label>
              <select
                id="currency"
                name="currency"
                value={company.currency}
                onChange={handleInputChange}
                className={errors.currency ? 'error' : ''}
              >
                <option value="">Select a currency</option>
                {currencies.map(currency => (
                  <option key={currency.code} value={currency.code}>
                    {currency.name} ({currency.symbol})
                  </option>
                ))}
              </select>
              {errors.currency && <span className="error-text">{errors.currency}</span>}
            </div>
            
            <div className="form-group">
              <label htmlFor="company_registry">Company Registry</label>
              <input
                type="text"
                id="company_registry"
                name="company_registry"
                value={company.company_registry}
                onChange={handleInputChange}
                placeholder="REG123456"
              />
            </div>
          </div>
        </div>

        {/* Save Button */}
        <div className="form-actions">
          <Button
            variant="primary"
            icon="fas fa-save"
            onClick={handleSave}
            loading={saving}
            disabled={saving}
          >
            {saving ? 'Saving...' : 'Save Company Settings'}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CompanySettings;
