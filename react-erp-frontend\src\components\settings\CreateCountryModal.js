import React, { useState } from 'react';
import { settingsAPI } from '../../services/api';
import Button from '../common/Button/Button';
import './CreateCurrencyModal.css'; // Reuse the same modal styles

const CreateCountryModal = ({ isOpen, onClose, onCountryCreated }) => {
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    phone_code: '',
    active: true
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Country name is required';
    }
    
    if (!formData.code.trim()) {
      newErrors.code = 'Country code is required';
    } else if (formData.code.length !== 2) {
      newErrors.code = 'Country code must be exactly 2 characters';
    } else if (!/^[A-Z]{2}$/.test(formData.code)) {
      newErrors.code = 'Country code must be 2 uppercase letters';
    }
    
    if (!formData.phone_code.trim()) {
      newErrors.phone_code = 'Phone code is required';
    } else if (!/^\d+$/.test(formData.phone_code)) {
      newErrors.phone_code = 'Phone code must be numeric';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setLoading(true);
    try {
      console.log('Creating country:', formData);
      
      const response = await settingsAPI.createCountry({
        ...formData,
        phone_code: parseInt(formData.phone_code)
      });
      
      console.log('Country created:', response);
      
      // Reset form
      setFormData({
        name: '',
        code: '',
        phone_code: '',
        active: true
      });
      
      // Notify parent component
      onCountryCreated(response);
      
      // Close modal
      onClose();
      
    } catch (error) {
      console.error('Error creating country:', error);
      if (error.response?.data) {
        setErrors(error.response.data);
      } else {
        alert('Failed to create country. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setFormData({
        name: '',
        code: '',
        phone_code: '',
        active: true
      });
      setErrors({});
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay" onClick={handleClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>
            <i className="fas fa-globe"></i>
            Create New Country
          </h2>
          <button className="modal-close" onClick={handleClose} disabled={loading}>
            <i className="fas fa-times"></i>
          </button>
        </div>

        <form onSubmit={handleSubmit} className="modal-form">
          <div className="form-row">
            <div className="form-group">
              <label htmlFor="name" className="required">Country Name</label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className={errors.name ? 'error' : ''}
                placeholder="United States"
                disabled={loading}
              />
              {errors.name && <span className="error-text">{errors.name}</span>}
            </div>
            
            <div className="form-group">
              <label htmlFor="code" className="required">Country Code</label>
              <input
                type="text"
                id="code"
                name="code"
                value={formData.code}
                onChange={handleInputChange}
                className={errors.code ? 'error' : ''}
                placeholder="US"
                maxLength="2"
                style={{ textTransform: 'uppercase' }}
                disabled={loading}
              />
              {errors.code && <span className="error-text">{errors.code}</span>}
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="phone_code" className="required">Phone Code</label>
              <input
                type="text"
                id="phone_code"
                name="phone_code"
                value={formData.phone_code}
                onChange={handleInputChange}
                className={errors.phone_code ? 'error' : ''}
                placeholder="1"
                disabled={loading}
              />
              {errors.phone_code && <span className="error-text">{errors.phone_code}</span>}
              <small style={{ color: 'var(--odoo-text-muted)', fontSize: 'var(--odoo-font-size-xs)', marginTop: '4px' }}>
                Enter without the + sign (e.g., 1 for +1)
              </small>
            </div>
            
            <div className="form-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  name="active"
                  checked={formData.active}
                  onChange={handleInputChange}
                  disabled={loading}
                />
                <span className="checkbox-text">Active</span>
              </label>
            </div>
          </div>

          <div className="modal-actions">
            <Button
              type="button"
              variant="secondary"
              onClick={handleClose}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={loading}
              disabled={loading}
            >
              {loading ? 'Creating...' : 'Create Country'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateCountryModal;
