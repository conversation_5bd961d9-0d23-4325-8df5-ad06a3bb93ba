import React, { useState, useEffect } from 'react';
import { settingsAPI } from '../../../services/api';
import './CompanySelector.css';

const CompanySelector = ({ currentCompany, onCompanyChange }) => {
  const [companies, setCompanies] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);

  useEffect(() => {
    loadCompanies();
  }, []);

  const loadCompanies = async () => {
    try {
      setLoading(true);
      // TODO: Create API endpoint to get all companies
      // For now, just show current company
      const response = await settingsAPI.getCompany();
      setCompanies([response]);
    } catch (error) {
      console.error('Error loading companies:', error);
      setCompanies([]);
    } finally {
      setLoading(false);
    }
  };

  const handleCompanySelect = (company) => {
    setShowDropdown(false);
    if (onCompanyChange) {
      onCompanyChange(company);
    }
  };

  if (loading) {
    return (
      <div className="company-selector loading">
        <i className="fas fa-spinner fa-spin"></i>
        <span>Loading...</span>
      </div>
    );
  }

  return (
    <div className="company-selector">
      <div 
        className="company-selector-trigger"
        onClick={() => setShowDropdown(!showDropdown)}
      >
        <div className="company-info">
          <div className="company-icon">
            <i className="fas fa-building"></i>
          </div>
          <div className="company-details">
            <div className="company-name">
              {currentCompany?.name || 'Select Company'}
            </div>
            <div className="company-code">
              {currentCompany?.code || 'No Company'}
            </div>
          </div>
        </div>
        <div className="dropdown-arrow">
          <i className={`fas fa-chevron-${showDropdown ? 'up' : 'down'}`}></i>
        </div>
      </div>

      {showDropdown && (
        <div className="company-dropdown">
          <div className="dropdown-header">
            <h4>Select Company</h4>
            <button 
              className="close-dropdown"
              onClick={() => setShowDropdown(false)}
            >
              <i className="fas fa-times"></i>
            </button>
          </div>
          
          <div className="company-list">
            {companies.map(company => (
              <div
                key={company.id}
                className={`company-item ${currentCompany?.id === company.id ? 'active' : ''}`}
                onClick={() => handleCompanySelect(company)}
              >
                <div className="company-icon">
                  <i className="fas fa-building"></i>
                </div>
                <div className="company-details">
                  <div className="company-name">{company.name}</div>
                  <div className="company-code">{company.code}</div>
                  <div className="company-meta">
                    {company.currency_name} • {company.country_name}
                  </div>
                </div>
                {currentCompany?.id === company.id && (
                  <div className="active-indicator">
                    <i className="fas fa-check"></i>
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="dropdown-footer">
            <button className="create-company-btn">
              <i className="fas fa-plus"></i>
              Create New Company
            </button>
          </div>
        </div>
      )}

      {showDropdown && (
        <div 
          className="dropdown-overlay"
          onClick={() => setShowDropdown(false)}
        ></div>
      )}
    </div>
  );
};

export default CompanySelector;
