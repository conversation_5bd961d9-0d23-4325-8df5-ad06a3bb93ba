import React, { useState } from 'react';
import { settingsAPI } from '../../services/api';
import Button from '../common/Button/Button';
import './CreateCurrencyModal.css';

const CreateCurrencyModal = ({ isOpen, onClose, onCurrencyCreated }) => {
  const [formData, setFormData] = useState({
    name: '',
    symbol: '',
    full_name: '',
    decimal_places: 2,
    position: 'before',
    active: true
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Currency code is required';
    } else if (formData.name.length !== 3) {
      newErrors.name = 'Currency code must be exactly 3 characters';
    } else if (!/^[A-Z]{3}$/.test(formData.name)) {
      newErrors.name = 'Currency code must be 3 uppercase letters';
    }
    
    if (!formData.symbol.trim()) {
      newErrors.symbol = 'Currency symbol is required';
    }
    
    if (!formData.full_name.trim()) {
      newErrors.full_name = 'Full name is required';
    }
    
    if (formData.decimal_places < 0 || formData.decimal_places > 6) {
      newErrors.decimal_places = 'Decimal places must be between 0 and 6';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setLoading(true);
    try {
      console.log('Creating currency:', formData);
      
      const response = await settingsAPI.createCurrency({
        ...formData,
        decimal_places: parseInt(formData.decimal_places)
      });
      
      console.log('Currency created:', response);
      
      // Reset form
      setFormData({
        name: '',
        symbol: '',
        full_name: '',
        decimal_places: 2,
        position: 'before',
        active: true
      });
      
      // Notify parent component
      onCurrencyCreated(response);
      
      // Close modal
      onClose();
      
    } catch (error) {
      console.error('Error creating currency:', error);
      if (error.response?.data) {
        setErrors(error.response.data);
      } else {
        alert('Failed to create currency. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setFormData({
        name: '',
        symbol: '',
        full_name: '',
        decimal_places: 2,
        position: 'before',
        active: true
      });
      setErrors({});
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay" onClick={handleClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>
            <i className="fas fa-coins"></i>
            Create New Currency
          </h2>
          <button className="modal-close" onClick={handleClose} disabled={loading}>
            <i className="fas fa-times"></i>
          </button>
        </div>

        <form onSubmit={handleSubmit} className="modal-form">
          <div className="form-row">
            <div className="form-group">
              <label htmlFor="name" className="required">Currency Code</label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className={errors.name ? 'error' : ''}
                placeholder="USD"
                maxLength="3"
                style={{ textTransform: 'uppercase' }}
                disabled={loading}
              />
              {errors.name && <span className="error-text">{errors.name}</span>}
            </div>
            
            <div className="form-group">
              <label htmlFor="symbol" className="required">Symbol</label>
              <input
                type="text"
                id="symbol"
                name="symbol"
                value={formData.symbol}
                onChange={handleInputChange}
                className={errors.symbol ? 'error' : ''}
                placeholder="$"
                maxLength="10"
                disabled={loading}
              />
              {errors.symbol && <span className="error-text">{errors.symbol}</span>}
            </div>
          </div>

          <div className="form-row">
            <div className="form-group full-width">
              <label htmlFor="full_name" className="required">Full Name</label>
              <input
                type="text"
                id="full_name"
                name="full_name"
                value={formData.full_name}
                onChange={handleInputChange}
                className={errors.full_name ? 'error' : ''}
                placeholder="US Dollar"
                disabled={loading}
              />
              {errors.full_name && <span className="error-text">{errors.full_name}</span>}
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="decimal_places">Decimal Places</label>
              <select
                id="decimal_places"
                name="decimal_places"
                value={formData.decimal_places}
                onChange={handleInputChange}
                className={errors.decimal_places ? 'error' : ''}
                disabled={loading}
              >
                <option value="0">0 (e.g., ¥100)</option>
                <option value="1">1 (e.g., $10.5)</option>
                <option value="2">2 (e.g., $10.50)</option>
                <option value="3">3 (e.g., $10.500)</option>
                <option value="4">4 (e.g., $10.5000)</option>
              </select>
              {errors.decimal_places && <span className="error-text">{errors.decimal_places}</span>}
            </div>
            
            <div className="form-group">
              <label htmlFor="position">Symbol Position</label>
              <select
                id="position"
                name="position"
                value={formData.position}
                onChange={handleInputChange}
                disabled={loading}
              >
                <option value="before">Before amount ($100)</option>
                <option value="after">After amount (100€)</option>
              </select>
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  name="active"
                  checked={formData.active}
                  onChange={handleInputChange}
                  disabled={loading}
                />
                <span className="checkbox-text">Active</span>
              </label>
            </div>
          </div>

          <div className="modal-actions">
            <Button
              type="button"
              variant="secondary"
              onClick={handleClose}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={loading}
              disabled={loading}
            >
              {loading ? 'Creating...' : 'Create Currency'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateCurrencyModal;
