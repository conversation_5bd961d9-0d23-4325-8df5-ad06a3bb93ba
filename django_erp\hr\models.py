from django.db import models
from django.core.exceptions import ValidationError
from django.utils import timezone
from decimal import Decimal
from datetime import timedelta, date
from core.models import BaseModel, Company, Partner, Currency, Country, CountryState
from django.contrib.auth.models import User


class HrDepartment(BaseModel):
    """Department model - equivalent to hr.department in Odoo"""
    
    name = models.CharField(max_length=255, help_text="Department Name")
    active = models.BooleanField(default=True)
    
    # Hierarchy
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True,
                                 help_text="Parent Department")
    childs = models.ManyToManyField('self', blank=True, symmetrical=False,
                                      related_name='parent_departments')
    
    # Management
    manager = models.ForeignKey('HrEmployee', on_delete=models.SET_NULL, null=True, blank=True,
                                  related_name='managed_departments', help_text="Department Manager")
    
    # Company
    company = models.ForeignKey(Company, on_delete=models.PROTECT)
    
    # Configuration
    note = models.TextField(blank=True, help_text="Additional Information")
    color = models.IntegerField(default=0, help_text="Color Index")
    
    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['name', 'company'],
                name='hr_department_name_company_uniq'
            )
        ]
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['company']),
            models.Index(fields=['parent']),
        ]
    
    def __str__(self):
        return self.name
    
    def clean(self):
        super().clean()
        
        # Prevent circular hierarchy
        if self.parent:
            parent = self.parent
            while parent:
                if parent == self:
                    raise ValidationError("Department cannot be its own parent.")
                parent = parent.parent


class HrJob(BaseModel):
    """Job Position model - equivalent to hr.job in Odoo"""
    
    STATE_CHOICES = [
        ('recruit', 'Recruitment in Progress'),
        ('open', 'Not Recruiting'),
    ]
    
    name = models.CharField(max_length=255, help_text="Job Title")
    active = models.BooleanField(default=True)
    
    # Department and company
    department = models.ForeignKey(HrDepartment, on_delete=models.PROTECT, null=True, blank=True)
    company = models.ForeignKey(Company, on_delete=models.PROTECT)
    
    # Recruitment
    state = models.CharField(max_length=20, choices=STATE_CHOICES, default='open')
    no_of_recruitment = models.IntegerField(default=1, help_text="Expected New Employees")
    no_of_hired_employee = models.IntegerField(default=0, help_text="Hired Employees")
    
    # Job details
    description = models.TextField(blank=True, help_text="Job Description")
    requirements = models.TextField(blank=True, help_text="Job Requirements")
    
    # Contract details
    contract_type = models.ForeignKey('HrContractType', on_delete=models.SET_NULL, 
                                        null=True, blank=True)
    
    class Meta:
        constraints = [
            models.CheckConstraint(
                check=models.Q(no_of_recruitment__gte=0),
                name='hr_job_positive_recruitment'
            ),
            models.CheckConstraint(
                check=models.Q(no_of_hired_employee__gte=0),
                name='hr_job_positive_hired'
            ),
        ]
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['department']),
            models.Index(fields=['company']),
            models.Index(fields=['state']),
        ]
    
    def __str__(self):
        return self.name


class HrContractType(BaseModel):
    """Contract Type model - equivalent to hr.contract.type in Odoo"""
    
    name = models.CharField(max_length=255, help_text="Contract Type")
    sequence = models.IntegerField(default=10)
    
    def __str__(self):
        return self.name


class HrEmployee(BaseModel):
    """Employee model - equivalent to hr.employee in Odoo"""
    
    GENDER_CHOICES = [
        ('male', 'Male'),
        ('female', 'Female'),
        ('other', 'Other'),
    ]
    
    MARITAL_CHOICES = [
        ('single', 'Single'),
        ('married', 'Married'),
        ('cohabitant', 'Legal Cohabitant'),
        ('widower', 'Widower'),
        ('divorced', 'Divorced'),
    ]
    
    # Basic information
    name = models.CharField(max_length=255, help_text="Employee Name")
    active = models.BooleanField(default=True)
    
    # User account
    user = models.OneToOneField(User, on_delete=models.CASCADE, null=True, blank=True,
                                  help_text="Related User")
    
    # Personal information
    private_name = models.CharField(max_length=255, blank=True, help_text="Private Name")
    gender = models.CharField(max_length=10, choices=GENDER_CHOICES, blank=True)
    marital = models.CharField(max_length=20, choices=MARITAL_CHOICES, blank=True)
    birthday = models.DateField(null=True, blank=True)
    
    # Contact information
    private_email = models.EmailField(blank=True, help_text="Private Email")
    phone = models.CharField(max_length=50, blank=True)
    mobile_phone = models.CharField(max_length=50, blank=True)
    
    # Work information
    company = models.ForeignKey(Company, on_delete=models.PROTECT)
    department = models.ForeignKey(HrDepartment, on_delete=models.SET_NULL, null=True, blank=True)
    job = models.ForeignKey(HrJob, on_delete=models.SET_NULL, null=True, blank=True)
    parent = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True,
                                 help_text="Manager")
    coach = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True,
                                related_name='coached_employees', help_text="Coach")
    
    # Work details
    employee_type = models.CharField(max_length=20, choices=[
        ('employee', 'Employee'),
        ('student', 'Student'),
        ('trainee', 'Trainee'),
        ('contractor', 'Contractor'),
        ('freelance', 'Freelancer'),
    ], default='employee')
    
    # Address information
    private_street = models.CharField(max_length=255, blank=True)
    private_street2 = models.CharField(max_length=255, blank=True)
    private_city = models.CharField(max_length=100, blank=True)
    private_state = models.ForeignKey(CountryState, on_delete=models.SET_NULL, 
                                        null=True, blank=True, related_name='employee_private_state')
    private_zip = models.CharField(max_length=20, blank=True)
    private_country = models.ForeignKey(Country, on_delete=models.SET_NULL, 
                                          null=True, blank=True, related_name='employee_private_country')
    
    # Work address
    work_location = models.CharField(max_length=255, blank=True)
    work_email = models.EmailField(blank=True)
    work_phone = models.CharField(max_length=50, blank=True)
    
    # Employment details
    employee_number = models.CharField(max_length=50, blank=True, help_text="Employee ID")
    passport = models.CharField(max_length=50, blank=True, help_text="Passport No")
    bank_account = models.CharField(max_length=100, blank=True, help_text="Bank Account")
    
    # Emergency contact
    emergency_contact = models.CharField(max_length=255, blank=True)
    emergency_phone = models.CharField(max_length=50, blank=True)
    
    # HR information
    identification = models.CharField(max_length=50, blank=True, help_text="Identification No")
    ssnid = models.CharField(max_length=50, blank=True, help_text="SSN")
    sinid = models.CharField(max_length=50, blank=True, help_text="SIN")
    
    # Additional information
    notes = models.TextField(blank=True)
    color = models.IntegerField(default=0, help_text="Color Index")
    
    # Computed fields
    is_address_home_a_company = models.BooleanField(default=False)
    
    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['employee_number', 'company'],
                name='hr_employee_number_company_uniq',
                condition=models.Q(employee_number__isnull=False) & ~models.Q(employee_number='')
            ),
        ]
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['employee_number']),
            models.Index(fields=['company']),
            models.Index(fields=['department']),
            models.Index(fields=['job']),
            models.Index(fields=['parent']),
            models.Index(fields=['user']),
        ]
    
    def __str__(self):
        return self.name
    
    def clean(self):
        super().clean()
        
        # Validate that employee cannot be their own manager
        if self.parent == self:
            raise ValidationError("Employee cannot be their own manager.")
        
        # Validate that employee cannot be their own coach
        if self.coach == self:
            raise ValidationError("Employee cannot be their own coach.")
    
    @property
    def display_name(self):
        """Display name for the employee"""
        return self.name
    
    def get_current_contract(self):
        """Get the current active contract"""
        today = timezone.now().date()
        return self.contract_ids.filter(
            date_start__lte=today,
            state='open'
        ).filter(
            models.Q(date_end__isnull=True) | models.Q(date_end__gte=today)
        ).first()


class HrContract(BaseModel):
    """Contract model - equivalent to hr.contract in Odoo"""

    STATE_CHOICES = [
        ('draft', 'New'),
        ('open', 'Running'),
        ('close', 'Expired'),
        ('cancel', 'Cancelled'),
    ]

    name = models.CharField(max_length=255, help_text="Contract Reference")
    active = models.BooleanField(default=True)

    # Employee and company
    employee = models.ForeignKey(HrEmployee, on_delete=models.CASCADE, related_name='contracts')
    company = models.ForeignKey(Company, on_delete=models.PROTECT)

    # Contract details
    type = models.ForeignKey(HrContractType, on_delete=models.PROTECT, null=True, blank=True)
    job = models.ForeignKey(HrJob, on_delete=models.SET_NULL, null=True, blank=True)
    department = models.ForeignKey(HrDepartment, on_delete=models.SET_NULL, null=True, blank=True)

    # Dates
    date_start = models.DateField(help_text="Start Date")
    date_end = models.DateField(null=True, blank=True, help_text="End Date")

    # State
    state = models.CharField(max_length=20, choices=STATE_CHOICES, default='draft')

    # Salary information
    wage = models.DecimalField(max_digits=20, decimal_places=2, default=0.0, help_text="Wage")
    currency = models.ForeignKey(Currency, on_delete=models.PROTECT, null=True, blank=True)

    # Working time
    resource_calendar = models.CharField(max_length=255, blank=True, help_text="Working Schedule")

    # Trial period
    trial_date_end = models.DateField(null=True, blank=True, help_text="End of Trial Period")

    # Notes
    notes = models.TextField(blank=True)

    class Meta:
        constraints = [
            models.CheckConstraint(
                check=models.Q(wage__gte=0),
                name='hr_contract_positive_wage'
            ),
            models.CheckConstraint(
                check=models.Q(date_end__isnull=True) | models.Q(date_end__gte=models.F('date_start')),
                name='hr_contract_valid_dates'
            ),
        ]
        indexes = [
            models.Index(fields=['employee']),
            models.Index(fields=['company']),
            models.Index(fields=['state']),
            models.Index(fields=['date_start']),
            models.Index(fields=['date_end']),
        ]

    def __str__(self):
        return f"{self.name} - {self.employee.name}"

    def clean(self):
        super().clean()

        # Validate dates
        if self.date_end and self.date_start and self.date_end < self.date_start:
            raise ValidationError("End date cannot be before start date.")

        # Validate trial period
        if self.trial_date_end and self.trial_date_end < self.date_start:
            raise ValidationError("Trial period end cannot be before contract start.")

    def save(self, *args, **kwargs):
        # Set currency from company if not set
        if not self.currency and self.company:
            self.currency = self.company.currency

        # Auto-update employee's job and department from contract
        if self.state == 'open':
            if self.job and self.employee.job != self.job:
                self.employee.job = self.job
                self.employee.save()

            if self.department and self.employee.department != self.department:
                self.employee.department = self.department
                self.employee.save()

        super().save(*args, **kwargs)

    def action_start(self):
        """Start the contract"""
        if self.state != 'draft':
            raise ValidationError("Only draft contracts can be started.")

        self.state = 'open'
        self.save()

    def action_close(self):
        """Close the contract"""
        if self.state != 'open':
            raise ValidationError("Only running contracts can be closed.")

        self.state = 'close'
        if not self.date_end:
            self.date_end = timezone.now().date()
        self.save()

    def action_cancel(self):
        """Cancel the contract"""
        if self.state in ['close', 'cancel']:
            raise ValidationError("Cannot cancel a closed or already cancelled contract.")

        self.state = 'cancel'
        self.save()


class HrAttendance(BaseModel):
    """Attendance model - equivalent to hr.attendance in Odoo"""

    employee = models.ForeignKey(HrEmployee, on_delete=models.CASCADE, related_name='attendances')

    # Check in/out times
    check_in = models.DateTimeField(help_text="Check In")
    check_out = models.DateTimeField(null=True, blank=True, help_text="Check Out")

    # Computed fields
    worked_hours = models.FloatField(default=0.0, help_text="Worked Hours")

    class Meta:
        constraints = [
            models.CheckConstraint(
                check=models.Q(check_out__isnull=True) | models.Q(check_out__gt=models.F('check_in')),
                name='hr_attendance_valid_times'
            ),
        ]
        indexes = [
            models.Index(fields=['employee']),
            models.Index(fields=['check_in']),
            models.Index(fields=['check_out']),
        ]

    def __str__(self):
        return f"{self.employee.name} - {self.check_in.date()}"

    def clean(self):
        super().clean()

        # Validate check out time
        if self.check_out and self.check_out <= self.check_in:
            raise ValidationError("Check out time must be after check in time.")

    def save(self, *args, **kwargs):
        # Calculate worked hours
        if self.check_in and self.check_out:
            delta = self.check_out - self.check_in
            self.worked_hours = delta.total_seconds() / 3600.0  # Convert to hours
        else:
            self.worked_hours = 0.0

        super().save(*args, **kwargs)

    def action_check_out(self):
        """Check out the employee"""
        if self.check_out:
            raise ValidationError("Employee is already checked out.")

        self.check_out = timezone.now()
        self.save()


class HrLeaveType(BaseModel):
    """Leave Type model - equivalent to hr.leave.type in Odoo"""

    ALLOCATION_TYPE_CHOICES = [
        ('no', 'No Allocation'),
        ('fixed', 'Fixed Allocation'),
        ('fixed_allocation', 'Fixed by HR'),
    ]

    REQUEST_UNIT_CHOICES = [
        ('day', 'Day'),
        ('half_day', 'Half Day'),
        ('hour', 'Hour'),
    ]

    name = models.CharField(max_length=255, help_text="Leave Type")
    active = models.BooleanField(default=True)
    sequence = models.IntegerField(default=100)

    # Configuration
    allocation_type = models.CharField(max_length=20, choices=ALLOCATION_TYPE_CHOICES, default='no')
    request_unit = models.CharField(max_length=20, choices=REQUEST_UNIT_CHOICES, default='day')

    # Validation
    validation_type = models.CharField(max_length=20, choices=[
        ('no_validation', 'No Validation'),
        ('hr', 'By HR Officer'),
        ('manager', 'By Employee Manager'),
        ('both', 'By Employee Manager then HR Officer'),
    ], default='hr')

    # Limits
    max_leaves = models.FloatField(default=0.0, help_text="Maximum Allowed")
    leaves_taken = models.FloatField(default=0.0, help_text="Leaves Already Taken")

    # Company
    company = models.ForeignKey(Company, on_delete=models.PROTECT)

    # Color for display
    color_name = models.CharField(max_length=50, blank=True)
    color = models.IntegerField(default=0)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['name', 'company'],
                name='hr_leave_type_name_company_uniq'
            ),
        ]
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['company']),
            models.Index(fields=['sequence']),
        ]

    def __str__(self):
        return self.name


class HrLeave(BaseModel):
    """Leave Request model - equivalent to hr.leave in Odoo"""

    STATE_CHOICES = [
        ('draft', 'To Submit'),
        ('confirm', 'To Approve'),
        ('refuse', 'Refused'),
        ('validate1', 'Second Approval'),
        ('validate', 'Approved'),
        ('cancel', 'Cancelled'),
    ]

    REQUEST_UNIT_CHOICES = [
        ('day', 'Day'),
        ('half_day', 'Half Day'),
        ('hour', 'Hour'),
    ]

    # Basic information
    name = models.CharField(max_length=255, help_text="Description")
    active = models.BooleanField(default=True)

    # Employee and leave type
    employee = models.ForeignKey(HrEmployee, on_delete=models.CASCADE, related_name='leaves')
    holiday_status = models.ForeignKey(HrLeaveType, on_delete=models.PROTECT,
                                         help_text="Leave Type")

    # Dates and duration
    request_date_from = models.DateField(help_text="Request Start Date")
    request_date_to = models.DateField(help_text="Request End Date")
    request_hour_from = models.CharField(max_length=10, blank=True, help_text="Request Start Hour")
    request_hour_to = models.CharField(max_length=10, blank=True, help_text="Request End Hour")

    # Duration
    request_unit_hours = models.BooleanField(default=False, help_text="Custom Hours")
    request_unit_half = models.BooleanField(default=False, help_text="Half Day")
    request_unit_custom = models.BooleanField(default=False, help_text="Custom")

    # Computed duration
    number_of_days = models.FloatField(default=0.0, help_text="Number of Days")
    duration_display = models.CharField(max_length=255, blank=True)

    # State and approval
    state = models.CharField(max_length=20, choices=STATE_CHOICES, default='draft')

    # Managers
    manager = models.ForeignKey(HrEmployee, on_delete=models.SET_NULL, null=True, blank=True,
                                  related_name='managed_leaves', help_text="Manager")

    # Company
    company = models.ForeignKey(Company, on_delete=models.PROTECT)

    # Notes
    notes = models.TextField(blank=True, help_text="Add a reason...")

    # Dates for tracking
    date_from = models.DateTimeField(null=True, blank=True, help_text="Start Date")
    date_to = models.DateTimeField(null=True, blank=True, help_text="End Date")

    class Meta:
        constraints = [
            models.CheckConstraint(
                check=models.Q(request_date_to__gte=models.F('request_date_from')),
                name='hr_leave_valid_dates'
            ),
            models.CheckConstraint(
                check=models.Q(number_of_days__gte=0),
                name='hr_leave_positive_days'
            ),
        ]
        indexes = [
            models.Index(fields=['employee']),
            models.Index(fields=['holiday_status']),
            models.Index(fields=['state']),
            models.Index(fields=['request_date_from']),
            models.Index(fields=['request_date_to']),
            models.Index(fields=['company']),
        ]

    def __str__(self):
        return f"{self.employee.name} - {self.holiday_status.name}"

    def clean(self):
        super().clean()

        # Validate dates
        if self.request_date_to < self.request_date_from:
            raise ValidationError("End date cannot be before start date.")

    def save(self, *args, **kwargs):
        # Calculate number of days
        if self.request_date_from and self.request_date_to:
            delta = self.request_date_to - self.request_date_from
            self.number_of_days = delta.days + 1  # Include both start and end dates

            # Adjust for half days
            if self.request_unit_half:
                self.number_of_days = 0.5

        # Set manager from employee's manager
        if not self.manager and self.employee and self.employee.parent:
            self.manager = self.employee.parent

        super().save(*args, **kwargs)

    def action_approve(self):
        """Approve the leave request"""
        if self.state not in ['confirm', 'validate1']:
            raise ValidationError("Only confirmed leave requests can be approved.")

        # Check if two-step approval is needed
        if self.holiday_status.validation_type == 'both' and self.state == 'confirm':
            self.state = 'validate1'
        else:
            self.state = 'validate'

        self.save()

    def action_refuse(self):
        """Refuse the leave request"""
        if self.state in ['refuse', 'cancel']:
            raise ValidationError("Cannot refuse an already refused or cancelled leave.")

        self.state = 'refuse'
        self.save()

    def action_draft(self):
        """Reset to draft"""
        self.state = 'draft'
        self.save()
