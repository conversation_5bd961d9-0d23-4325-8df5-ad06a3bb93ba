#!/usr/bin/env python3
"""
Script to fix ForeignKey field names in Django models.
Removes '_id' suffix from ForeignKey field names to prevent double '_id' in database.
"""

import os
import re
import sys

def fix_foreign_key_names(file_path):
    """Fix ForeignKey field names in a Python file."""
    print(f"Processing {file_path}...")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # Pattern to match ForeignKey field definitions
    # Matches: field_name_id = models.ForeignKey(...)
    fk_pattern = r'(\s+)(\w+)_id(\s*=\s*models\.ForeignKey\([^)]+\))'
    
    def replace_fk(match):
        indent = match.group(1)
        field_name = match.group(2)
        rest = match.group(3)
        return f"{indent}{field_name}{rest}"
    
    content = re.sub(fk_pattern, replace_fk, content)
    
    # Pattern to match ManyToManyField with _ids suffix
    # Matches: field_name_ids = models.ManyToManyField(...)
    m2m_pattern = r'(\s+)(\w+)_ids(\s*=\s*models\.ManyToManyField\([^)]+\))'
    
    def replace_m2m(match):
        indent = match.group(1)
        field_name = match.group(2)
        rest = match.group(3)
        # Convert plural _ids to singular or appropriate plural form
        if field_name.endswith('ies'):
            # companies -> company, categories -> category
            singular = field_name[:-3] + 'y'
        elif field_name.endswith('s'):
            # accounts -> account, taxes -> tax
            singular = field_name[:-1]
        else:
            singular = field_name
        
        # For many-to-many, we usually want plural form
        if singular.endswith('y'):
            plural = singular[:-1] + 'ies'
        elif singular.endswith(('s', 'sh', 'ch', 'x', 'z')):
            plural = singular + 'es'
        else:
            plural = singular + 's'
            
        return f"{indent}{plural}{rest}"
    
    content = re.sub(m2m_pattern, replace_m2m, content)
    
    # Fix references in Meta classes and other places
    # unique_together, indexes, etc.
    meta_patterns = [
        (r"'(\w+)_id'", r"'\1'"),  # 'field_id' -> 'field'
        (r'"(\w+)_id"', r'"\1"'),  # "field_id" -> "field"
        (r'\[(\w+)_id\]', r'[\1]'),  # [field_id] -> [field]
        (r"'(\w+)_ids'", r"'\1s'"),  # 'field_ids' -> 'fields'
        (r'"(\w+)_ids"', r'"\1s"'),  # "field_ids" -> "fields"
    ]

    for pattern, replacement in meta_patterns:
        content = re.sub(pattern, replacement, content)

    # Fix __str__ methods and other references
    # self.field_id.name -> self.field.name
    attr_pattern = r'self\.(\w+)_id\.'
    content = re.sub(attr_pattern, r'self.\1.', content)

    # Fix other attribute references
    # line.field_id -> line.field
    obj_attr_pattern = r'(\w+)\.(\w+)_id(?=\s*[^a-zA-Z0-9_])'
    content = re.sub(obj_attr_pattern, r'\1.\2', content)

    # Fix filter/exclude patterns
    # filter(field_id=...) -> filter(field=...)
    filter_pattern = r'(\w+)_id(\s*=)'
    content = re.sub(filter_pattern, r'\1\2', content)

    # Fix assignment patterns
    # obj.field_id = value -> obj.field = value
    assignment_pattern = r'(\w+)\.(\w+)_id(\s*=\s*)'
    content = re.sub(assignment_pattern, r'\1.\2\3', content)
    
    # Write back if changed
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  ✓ Fixed {file_path}")
        return True
    else:
        print(f"  - No changes needed in {file_path}")
        return False

def main():
    """Main function to process all model files."""
    django_erp_path = "django_erp"
    
    if not os.path.exists(django_erp_path):
        print(f"Error: {django_erp_path} directory not found!")
        sys.exit(1)
    
    model_files = []
    
    # Find all models.py and admin.py files
    for root, dirs, files in os.walk(django_erp_path):
        for file in files:
            if file in ['models.py', 'admin.py']:
                model_files.append(os.path.join(root, file))
    
    if not model_files:
        print("No models.py or admin.py files found!")
        sys.exit(1)

    print(f"Found {len(model_files)} model/admin files:")
    for file in model_files:
        print(f"  - {file}")
    
    print("\nProcessing files...")
    changed_files = 0
    
    for file_path in model_files:
        if fix_foreign_key_names(file_path):
            changed_files += 1
    
    print(f"\nCompleted! Fixed {changed_files} out of {len(model_files)} files.")
    
    if changed_files > 0:
        print("\nNext steps:")
        print("1. Review the changes")
        print("2. Create and run migrations: python manage.py makemigrations")
        print("3. Apply migrations: python manage.py migrate")

if __name__ == "__main__":
    main()
