from django.contrib import admin
from .models import (
    SaleOrder, SaleOrderLine, ProductPricelist, SalesTeam
)


@admin.register(SaleOrder)
class SaleOrderAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'partner_id', 'date_order', 'state', 'amount_total',
        'delivery_status', 'approval_status'
    ]
    list_filter = [
        'state', 'delivery_status', 'approval_status', 'company_id',
        'date_order', 'pricelist_id'
    ]
    search_fields = ['name', 'client_order_ref', 'partner_id__name']
    date_hierarchy = 'date_order'
    readonly_fields = ['amount_untaxed', 'amount_tax', 'amount_total']

    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'origin', 'client_order_ref', 'date_order']
        }),
        ('Customer', {
            'fields': ['partner_id', 'partner_invoice_id', 'partner_shipping_id']
        }),
        ('Sales Information', {
            'fields': ['user_id', 'team_id', 'company_id']
        }),
        ('Pricing', {
            'fields': ['pricelist_id', 'currency_id']
        }),
        ('Amounts', {
            'fields': ['amount_untaxed', 'amount_tax', 'amount_total']
        }),
        ('Status', {
            'fields': ['state', 'delivery_status', 'approval_status']
        }),
        ('Approval', {
            'fields': ['approved_by', 'approval_date']
        }),
        ('Delivery', {
            'fields': ['commitment_date', 'expected_date']
        }),
        ('Terms', {
            'fields': ['payment_term_id', 'fiscal_position_id', 'incoterm']
        }),
        ('Additional', {
            'fields': ['note']
        }),
    ]

    actions = ['action_confirm', 'action_approve', 'action_cancel']

    def action_confirm(self, request, queryset):
        for order in queryset.filter(state='draft'):
            order.action_confirm()
        self.message_user(request, f"Confirmed {queryset.count()} sale orders.")
    action_confirm.short_description = "Confirm selected sale orders"

    def action_approve(self, request, queryset):
        for order in queryset.filter(approval_status='pending'):
            order.action_approve()
        self.message_user(request, f"Approved {queryset.count()} sale orders.")
    action_approve.short_description = "Approve selected sale orders"

    def action_cancel(self, request, queryset):
        for order in queryset.filter(state__in=['draft', 'sent']):
            order.action_cancel()
        self.message_user(request, f"Cancelled {queryset.count()} sale orders.")
    action_cancel.short_description = "Cancel selected sale orders"


@admin.register(SaleOrderLine)
class SaleOrderLineAdmin(admin.ModelAdmin):
    list_display = [
        'order_id', 'product_id', 'name', 'product_uom_qty',
        'price_unit', 'price_subtotal'
    ]
    list_filter = ['order_id__state', 'company_id', 'product_uom']
    search_fields = ['name', 'product_id__name', 'order_id__name']
    readonly_fields = ['price_subtotal', 'price_tax', 'price_total']

    fieldsets = [
        ('Order Information', {
            'fields': ['order_id', 'sequence']
        }),
        ('Product', {
            'fields': ['product_id', 'name']
        }),
        ('Quantity & UoM', {
            'fields': ['product_uom_qty', 'product_uom', 'qty_delivered', 'qty_invoiced']
        }),
        ('Pricing', {
            'fields': ['price_unit', 'discount']
        }),
        ('Amounts', {
            'fields': ['price_subtotal', 'price_tax', 'price_total']
        }),
        ('Taxes', {
            'fields': ['tax_id']
        }),
        ('Additional', {
            'fields': ['company_id', 'currency_id']
        }),
    ]


@admin.register(ProductPricelist)
class ProductPricelistAdmin(admin.ModelAdmin):
    list_display = ['name', 'currency_id', 'company_id', 'active']
    list_filter = ['active', 'currency_id', 'company_id']
    search_fields = ['name']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'active', 'currency_id', 'company_id']
        }),
        ('Configuration', {
            'fields': ['discount_policy']
        }),
    ]


@admin.register(SalesTeam)
class SalesTeamAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'active', 'company_id']
    list_filter = ['active', 'company_id']
    search_fields = ['name', 'code']
    filter_horizontal = ['member_ids']

    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'code', 'active', 'company_id']
        }),
        ('Team Members', {
            'fields': ['user_id', 'member_ids']
        }),
    ]
