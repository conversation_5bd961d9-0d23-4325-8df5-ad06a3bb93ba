from django.contrib import admin
from .models import (
    SaleOrder, SaleOrderLine, ProductPricelist, SalesTeam
)


@admin.register(SaleOrder)
class SaleOrderAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'partner', 'date_order', 'state', 'amount_total',
        'delivery_status', 'approval_status'
    ]
    list_filter = [
        'state', 'delivery_status', 'approval_status', 'company',
        'date_order', 'pricelist'
    ]
    search_fields = ['name', 'client_order_ref', 'partner_id__name']
    date_hierarchy = 'date_order'
    readonly_fields = ['amount_untaxed', 'amount_tax', 'amount_total']

    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'origin', 'client_order_ref', 'date_order']
        }),
        ('Customer', {
            'fields': ['partner', 'partner_invoice', 'partner_shipping']
        }),
        ('Sales Information', {
            'fields': ['user', 'team', 'company']
        }),
        ('Pricing', {
            'fields': ['pricelist', 'currency']
        }),
        ('Amounts', {
            'fields': ['amount_untaxed', 'amount_tax', 'amount_total']
        }),
        ('Status', {
            'fields': ['state', 'delivery_status', 'approval_status']
        }),
        ('Approval', {
            'fields': ['approved_by', 'approval_date']
        }),
        ('Delivery', {
            'fields': ['commitment_date', 'expected_date']
        }),
        ('Terms', {
            'fields': ['payment_term', 'fiscal_position', 'incoterm']
        }),
        ('Additional', {
            'fields': ['note']
        }),
    ]

    actions = ['action_confirm', 'action_approve', 'action_cancel']

    def action_confirm(self, request, queryset):
        for order in queryset.filter(state='draft'):
            order.action_confirm()
        self.message_user(request, f"Confirmed {queryset.count()} sale orders.")
    action_confirm.short_description = "Confirm selected sale orders"

    def action_approve(self, request, queryset):
        for order in queryset.filter(approval_status='pending'):
            order.action_approve()
        self.message_user(request, f"Approved {queryset.count()} sale orders.")
    action_approve.short_description = "Approve selected sale orders"

    def action_cancel(self, request, queryset):
        for order in queryset.filter(state__in=['draft', 'sent']):
            order.action_cancel()
        self.message_user(request, f"Cancelled {queryset.count()} sale orders.")
    action_cancel.short_description = "Cancel selected sale orders"


@admin.register(SaleOrderLine)
class SaleOrderLineAdmin(admin.ModelAdmin):
    list_display = [
        'order', 'product', 'name', 'product_uom_qty',
        'price_unit', 'price_subtotal'
    ]
    list_filter = ['order_id__state', 'company', 'product_uom']
    search_fields = ['name', 'product_id__name', 'order_id__name']
    readonly_fields = ['price_subtotal', 'price_tax', 'price_total']

    fieldsets = [
        ('Order Information', {
            'fields': ['order', 'sequence']
        }),
        ('Product', {
            'fields': ['product', 'name']
        }),
        ('Quantity & UoM', {
            'fields': ['product_uom_qty', 'product_uom', 'qty_delivered', 'qty_invoiced']
        }),
        ('Pricing', {
            'fields': ['price_unit', 'discount']
        }),
        ('Amounts', {
            'fields': ['price_subtotal', 'price_tax', 'price_total']
        }),
        ('Taxes', {
            'fields': ['tax']
        }),
        ('Additional', {
            'fields': ['company', 'currency']
        }),
    ]


@admin.register(ProductPricelist)
class ProductPricelistAdmin(admin.ModelAdmin):
    list_display = ['name', 'currency', 'company', 'active']
    list_filter = ['active', 'currency', 'company']
    search_fields = ['name']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'active', 'currency', 'company']
        }),
        ('Configuration', {
            'fields': ['discount_policy']
        }),
    ]


@admin.register(SalesTeam)
class SalesTeamAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'active', 'company']
    list_filter = ['active', 'company']
    search_fields = ['name', 'code']
    filter_horizontal = ['members']

    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'code', 'active', 'company']
        }),
        ('Team Members', {
            'fields': ['user', 'members']
        }),
    ]
