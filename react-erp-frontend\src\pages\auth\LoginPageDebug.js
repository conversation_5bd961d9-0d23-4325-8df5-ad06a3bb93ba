import React, { useState } from 'react';
import './LoginPage.css';

const LoginPageDebug = () => {
  const [formData, setFormData] = useState({
    username: 'admin',
    password: 'admin'
  });
  const [loading, setLoading] = useState(false);
  const [response, setResponse] = useState(null);
  const [error, setError] = useState(null);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const testDirectAPI = async () => {
    setLoading(true);
    setError(null);
    setResponse(null);

    try {
      console.log('Testing direct API call...');
      
      const response = await fetch('http://localhost:8000/api/v1/auth/login/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('Response data:', data);
      
      setResponse(data);
      
      // Store tokens in localStorage
      if (data.access) {
        localStorage.setItem('access_token', data.access);
        localStorage.setItem('refresh_token', data.refresh);
        localStorage.setItem('user', JSON.stringify(data.user));
        console.log('Tokens stored successfully');
      }

    } catch (err) {
      console.error('API Error:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const checkStoredTokens = () => {
    const accessToken = localStorage.getItem('access_token');
    const refreshToken = localStorage.getItem('refresh_token');
    const user = localStorage.getItem('user');
    
    console.log('Stored tokens:');
    console.log('Access token:', accessToken ? 'Present' : 'Not found');
    console.log('Refresh token:', refreshToken ? 'Present' : 'Not found');
    console.log('User data:', user ? JSON.parse(user) : 'Not found');
  };

  const clearTokens = () => {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user');
    console.log('Tokens cleared');
    setResponse(null);
  };

  return (
    <div className="login-page">
      <div className="login-container">
        <div className="login-card">
          <div className="login-header">
            <h1>🔧 Login Debug Mode</h1>
            <p>Testing authentication integration</p>
          </div>

          <div className="login-form">
            <div style={{ marginBottom: '1rem' }}>
              <label>Username:</label>
              <input
                type="text"
                name="username"
                value={formData.username}
                onChange={handleInputChange}
                style={{
                  width: '100%',
                  padding: '0.5rem',
                  margin: '0.25rem 0',
                  border: '1px solid #ccc',
                  borderRadius: '4px'
                }}
              />
            </div>

            <div style={{ marginBottom: '1rem' }}>
              <label>Password:</label>
              <input
                type="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                style={{
                  width: '100%',
                  padding: '0.5rem',
                  margin: '0.25rem 0',
                  border: '1px solid #ccc',
                  borderRadius: '4px'
                }}
              />
            </div>

            <div style={{ display: 'flex', gap: '0.5rem', flexWrap: 'wrap' }}>
              <button
                onClick={testDirectAPI}
                disabled={loading}
                style={{
                  padding: '0.5rem 1rem',
                  backgroundColor: '#714B67',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: loading ? 'wait' : 'pointer'
                }}
              >
                {loading ? 'Testing...' : 'Test Login API'}
              </button>

              <button
                onClick={checkStoredTokens}
                style={{
                  padding: '0.5rem 1rem',
                  backgroundColor: '#17A2B8',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                Check Tokens
              </button>

              <button
                onClick={clearTokens}
                style={{
                  padding: '0.5rem 1rem',
                  backgroundColor: '#DC3545',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                Clear Tokens
              </button>
            </div>

            {error && (
              <div style={{
                marginTop: '1rem',
                padding: '0.5rem',
                backgroundColor: '#f8d7da',
                border: '1px solid #f5c6cb',
                borderRadius: '4px',
                color: '#721c24'
              }}>
                <strong>Error:</strong> {error}
              </div>
            )}

            {response && (
              <div style={{
                marginTop: '1rem',
                padding: '0.5rem',
                backgroundColor: '#d4edda',
                border: '1px solid #c3e6cb',
                borderRadius: '4px',
                color: '#155724'
              }}>
                <strong>Success!</strong>
                <pre style={{ fontSize: '0.8rem', marginTop: '0.5rem' }}>
                  {JSON.stringify(response, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPageDebug;
