from django.contrib.auth.models import User
from core.models import Company, Partner, Currency, Country
from accounting.models import AccountGroup, AccountAccount, AccountJournal

# Get admin user
admin_user = User.objects.get(username='admin')

# Create Currency
usd, created = Currency.objects.get_or_create(
    name='USD',
    defaults={
        'symbol': '$',
        'decimal_places': 2,
        'full_name': 'US Dollar',
        'position': 'before',
        'create_uid': admin_user,
        'write_uid': admin_user
    }
)
print(f"Currency: {usd.name} ({'created' if created else 'exists'})")

# Create Country
usa, created = Country.objects.get_or_create(
    code='US',
    defaults={
        'name': 'United States',
        'phone_code': 1,
        'create_uid': admin_user,
        'write_uid': admin_user
    }
)
print(f"Country: {usa.name} ({'created' if created else 'exists'})")

# Create Company
company, created = Company.objects.get_or_create(
    code='DEMO',
    defaults={
        'name': 'Demo ERP Company',
        'currency': usd,
        'email': '<EMAIL>',
        'phone': '******-0123',
        'website': 'https://demo-erp.com',
        'vat': 'US123456789',
        'street': '123 Business Ave',
        'city': 'New York',
        'zip': '10001',
        'country': usa,
        'create_uid': admin_user,
        'write_uid': admin_user
    }
)
print(f"Company: {company.name} ({'created' if created else 'exists'})")

# Create Account Groups
asset_group, created = AccountGroup.objects.get_or_create(
    name='Assets',
    company=company,
    defaults={
        'code_prefix_start': '1000',
        'code_prefix_end': '1999',
        'create_uid': admin_user,
        'write_uid': admin_user
    }
)
print(f"Account Group: {asset_group.name} ({'created' if created else 'exists'})")

liability_group, created = AccountGroup.objects.get_or_create(
    name='Liabilities',
    company=company,
    defaults={
        'code_prefix_start': '2000',
        'code_prefix_end': '2999',
        'create_uid': admin_user,
        'write_uid': admin_user
    }
)
print(f"Account Group: {liability_group.name} ({'created' if created else 'exists'})")

# Create Main Accounts
accounts_data = [
    ('1000', 'Cash', 'asset_cash', asset_group),
    ('1100', 'Bank Account', 'asset_cash', asset_group),
    ('1200', 'Accounts Receivable', 'asset_receivable', asset_group, True),
    ('2000', 'Accounts Payable', 'liability_payable', liability_group, True),
    ('4000', 'Sales Revenue', 'income', None),
    ('5000', 'Cost of Goods Sold', 'expense_direct_cost', None),
]

for account_data in accounts_data:
    code, name, account_type, group = account_data[:4]
    reconcile = account_data[4] if len(account_data) > 4 else False
    
    account, created = AccountAccount.objects.get_or_create(
        code=code,
        company=company,
        defaults={
            'name': name,
            'account_type': account_type,
            'group': group,
            'reconcile': reconcile,
            'currency': usd,
            'create_uid': admin_user,
            'write_uid': admin_user
        }
    )
    print(f"Account: {code} - {name} ({'created' if created else 'exists'})")

# Create Customers
customers_data = [
    ('ABC Corp', '<EMAIL>', '******-1001'),
    ('XYZ Ltd', '<EMAIL>', '******-1002'),
    ('Tech Solutions Inc', '<EMAIL>', '******-1003'),
]

for name, email, phone in customers_data:
    partner, created = Partner.objects.get_or_create(
        name=name,
        defaults={
            'is_company': True,
            'customer_rank': 1,
            'supplier_rank': 0,
            'email': email,
            'phone': phone,
            'street': f'{name} Street',
            'city': 'New York',
            'zip': '10001',
            'country': usa,
            'create_uid': admin_user,
            'write_uid': admin_user
        }
    )
    print(f"Customer: {name} ({'created' if created else 'exists'})")

# Create Vendors
vendors_data = [
    ('Office Supplies Co', '<EMAIL>', '******-2001'),
    ('Equipment Rental LLC', '<EMAIL>', '******-2002'),
]

for name, email, phone in vendors_data:
    partner, created = Partner.objects.get_or_create(
        name=name,
        defaults={
            'is_company': True,
            'customer_rank': 0,
            'supplier_rank': 1,
            'email': email,
            'phone': phone,
            'street': f'{name} Street',
            'city': 'New York',
            'zip': '10001',
            'country': usa,
            'create_uid': admin_user,
            'write_uid': admin_user
        }
    )
    print(f"Vendor: {name} ({'created' if created else 'exists'})")

print("\n✅ Basic test data created successfully!")
print("🌐 Access admin at: http://localhost:8000/admin/")
print("   Username: admin | Password: admin")
