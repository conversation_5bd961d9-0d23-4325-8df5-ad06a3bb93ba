import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { accountingAPI } from '../../services/api';
import Button from '../../components/common/Button/Button';
import CreateAccountModal from '../../components/accounting/CreateAccountModal';
import { createSampleAccounts } from '../../utils/sampleData';
import './ChartOfAccounts.css';

const ChartOfAccounts = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [accounts, setAccounts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [showCreateForm, setShowCreateForm] = useState(false);

  useEffect(() => {
    if (user) {
      loadAccounts();
    } else {
      navigate('/login');
    }
  }, [user, navigate]);

  const loadAccounts = async () => {
    try {
      setLoading(true);
      const response = await accountingAPI.getAllAccounts();

      // Handle paginated responses - fetch all pages if needed
      let allAccounts = [];

      if (response.results) {
        // Paginated response
        allAccounts = [...response.results];
        console.log(`📋 First page: ${response.results.length} accounts`);

        // If there are more pages, fetch them all
        let nextUrl = response.next;
        while (nextUrl) {
          console.log('� Fetching next page:', nextUrl);
          try {
            const nextResponse = await fetch(nextUrl, {
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
                'Content-Type': 'application/json'
              }
            });
            const nextData = await nextResponse.json();
            allAccounts = [...allAccounts, ...nextData.results];
            nextUrl = nextData.next;
            console.log(`📋 Added ${nextData.results.length} more accounts. Total: ${allAccounts.length}`);
          } catch (pageError) {
            console.error('❌ Error fetching next page:', pageError);
            break;
          }
        }
      } else {
        // Non-paginated response
        allAccounts = Array.isArray(response) ? response : [];
      }

      console.log('📋 Final accounts data:', allAccounts);
      console.log('📋 Total accounts loaded:', allAccounts.length);

      if (allAccounts.length > 0) {
        setAccounts(allAccounts);
        console.log(`✅ Loaded ${allAccounts.length} accounts from API`);
        console.log('📋 First account:', allAccounts[0]);
        console.log('📋 Last account:', allAccounts[allAccounts.length - 1]);

        // Show sample of account codes for verification
        const codes = allAccounts.slice(0, 10).map(acc => acc.code).join(', ');
        console.log('📋 Sample account codes:', codes);
      } else {
        console.log('⚠️ No accounts found in API response');
        console.log('🔍 Creating sample accounts for testing...');

        // Create sample accounts if none exist
        try {
          await createSampleAccounts(accountingAPI);
          console.log('✅ Sample accounts created, reloading...');

          // Reload accounts after creating samples
          const newResponse = await accountingAPI.getAccounts();
          const newAccountsData = newResponse.results || newResponse;
          setAccounts(Array.isArray(newAccountsData) ? newAccountsData : []);

        } catch (createError) {
          console.error('❌ Error creating sample accounts:', createError);
          setAccounts([]);
        }
      }

    } catch (error) {
      console.error('❌ Error loading accounts:', error);
      console.error('❌ Error details:', error.response?.data);
      console.error('❌ Error status:', error.response?.status);
      setAccounts([]);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateAccount = () => {
    setShowCreateForm(true);
  };

  const handleAccountCreated = (newAccount) => {
    console.log('New account created:', newAccount);
    // Add the new account to the list
    setAccounts(prevAccounts => [newAccount, ...prevAccounts]);
    // Optionally reload all accounts to ensure consistency
    loadAccounts();
    setShowCreateForm(false);
  };

  const handleCancelCreate = () => {
    setShowCreateForm(false);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(Math.abs(amount));
  };

  const getAccountTypeLabel = (type) => {
    const types = {
      'asset_cash': 'Cash & Bank',
      'asset_receivable': 'Receivable',
      'asset_current': 'Current Asset',
      'asset_fixed': 'Fixed Asset',
      'liability_payable': 'Payable',
      'liability_current': 'Current Liability',
      'liability_non_current': 'Non-current Liability',
      'equity': 'Equity',
      'income': 'Income',
      'expense': 'Expense',
      'expense_direct_cost': 'Cost of Revenue'
    };
    return types[type] || type;
  };

  const getAccountTypeColor = (type) => {
    const colors = {
      'asset_cash': '#28a745',
      'asset_receivable': '#17a2b8',
      'asset_current': '#20c997',
      'asset_fixed': '#6f42c1',
      'liability_payable': '#dc3545',
      'liability_current': '#fd7e14',
      'liability_non_current': '#e83e8c',
      'equity': '#6610f2',
      'income': '#28a745',
      'expense': '#ffc107',
      'expense_direct_cost': '#fd7e14'
    };
    return colors[type] || '#6c757d';
  };

  const filteredAccounts = accounts.filter(account => {
    // Search filter - check name and code
    const matchesSearch = searchTerm === '' ||
                         account.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         account.code.toLowerCase().includes(searchTerm.toLowerCase());

    // Type filter - check account type
    const matchesFilter = filterType === 'all' ||
                         account.account_type.toLowerCase().includes(filterType.toLowerCase());

    // Debug logging (remove in production)
    if (process.env.NODE_ENV === 'development' && searchTerm && !matchesSearch) {
      console.log(`Search miss: "${searchTerm}" not in "${account.name}" or "${account.code}"`);
    }
    if (process.env.NODE_ENV === 'development' && filterType !== 'all' && !matchesFilter) {
      console.log(`Filter miss: "${filterType}" not in "${account.account_type}"`);
    }

    return matchesSearch && matchesFilter;
  });

  const accountTypes = [
    { value: 'all', label: 'All Types' },
    { value: 'asset', label: 'Assets' },
    { value: 'liability', label: 'Liabilities' },
    { value: 'equity', label: 'Equity' },
    { value: 'income', label: 'Income' },
    { value: 'expense', label: 'Expenses' }
  ];

  if (loading) {
    return (
      <div className="chart-of-accounts loading">
        <div className="loading-spinner">
          <i className="fas fa-spinner fa-spin"></i>
          <p>Loading chart of accounts...</p>
        </div>
      </div>
    );
  }

  // Show create form if requested
  if (showCreateForm) {
    return (
      <CreateAccountModal
        isOpen={true}
        onClose={handleCancelCreate}
        onAccountCreated={handleAccountCreated}
      />
    );
  }

  return (
    <div className="chart-of-accounts">


      {/* Header */}
      <div className="page-header">
        <div className="header-content">
          <h1>
            <i className="fas fa-list-alt"></i>
            Chart of Accounts
          </h1>
          <p>Manage your accounting structure</p>
        </div>
        <div className="header-actions">
          <Button
            variant="secondary"
            icon="fas fa-arrow-left"
            onClick={() => navigate('/accounting')}
          >
            Back to Accounting
          </Button>

          <Button
            variant="outline"
            icon={`fas fa-sync-alt ${loading ? 'fa-spin' : ''}`}
            onClick={loadAccounts}
            disabled={loading}
          >
            {loading ? 'Loading...' : 'Refresh'}
          </Button>

          {process.env.NODE_ENV === 'development' && (
            <Button
              variant="outline"
              icon="fas fa-bug"
              onClick={() => {
                console.log('🔍 Debug Info:');
                console.log('Total accounts:', accounts.length);
                console.log('Filtered accounts:', filteredAccounts.length);
                console.log('Search term:', searchTerm);
                console.log('Filter type:', filterType);
                console.log('Sample accounts:', accounts.slice(0, 3));
                console.log('Account types found:', [...new Set(accounts.map(a => a.account_type))]);
              }}
            >
              Debug
            </Button>
          )}

          <Button
            variant="primary"
            icon="fas fa-plus"
            onClick={handleCreateAccount}
          >
            New Account
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="filters-section">
        {/* Filter Status Indicator */}
        {(searchTerm || filterType !== 'all') && (
          <div style={{
            padding: '0.5rem',
            background: '#e3f2fd',
            borderRadius: '4px',
            marginBottom: '1rem',
            fontSize: '0.875rem'
          }}>
            <strong>Active Filters:</strong>
            {searchTerm && <span> Search: "{searchTerm}"</span>}
            {filterType !== 'all' && <span> Type: {accountTypes.find(t => t.value === filterType)?.label}</span>}
            <button
              onClick={() => { setSearchTerm(''); setFilterType('all'); }}
              style={{ marginLeft: '1rem', padding: '0.25rem 0.5rem', fontSize: '0.75rem' }}
            >
              Clear All
            </button>
          </div>
        )}

        <div className="search-box">
          <i className="fas fa-search"></i>
          <input
            type="text"
            placeholder="Search accounts by name or code..."
            value={searchTerm}
            onChange={(e) => {
              console.log('🔍 Search changed:', e.target.value);
              setSearchTerm(e.target.value);
            }}
          />
        </div>

        <div className="filter-dropdown">
          <select
            value={filterType}
            onChange={(e) => {
              console.log('🔽 Filter changed:', e.target.value);
              setFilterType(e.target.value);
            }}
          >
            {accountTypes.map(type => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Accounts Table */}
      <div className="accounts-table-container">
        <table className="accounts-table">
          <thead>
            <tr>
              <th>Code</th>
              <th>Account Name</th>
              <th>Type</th>
              <th>Balance</th>
              <th>Reconcile</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody>
            {filteredAccounts.map(account => (
              <tr key={account.id} className={!account.active ? 'inactive' : ''}>
                <td className="account-code">{account.code}</td>
                <td className="account-name">
                  <div className="name-cell">
                    <span className="name">{account.name}</span>
                  </div>
                </td>
                <td>
                  <span 
                    className="account-type-badge"
                    style={{ backgroundColor: getAccountTypeColor(account.account_type) }}
                  >
                    {getAccountTypeLabel(account.account_type)}
                  </span>
                </td>
                <td className={`balance ${account.balance < 0 ? 'credit' : 'debit'}`}>
                  {formatCurrency(account.balance)}
                  <span className="balance-type">
                    {account.balance < 0 ? 'Cr' : 'Dr'}
                  </span>
                </td>
                <td>
                  {account.reconcile ? (
                    <span className="reconcile-badge active">
                      <i className="fas fa-check"></i>
                      Yes
                    </span>
                  ) : (
                    <span className="reconcile-badge inactive">
                      <i className="fas fa-times"></i>
                      No
                    </span>
                  )}
                </td>
                <td>
                  <span className={`status-badge ${account.active ? 'active' : 'inactive'}`}>
                    {account.active ? 'Active' : 'Inactive'}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {filteredAccounts.length === 0 && (
          <div className="empty-state">
            <i className="fas fa-search"></i>
            <h3>No accounts found</h3>
            <p>Try adjusting your search criteria or create a new account.</p>
          </div>
        )}
      </div>

      {/* Summary */}
      <div className="accounts-summary">
        <div className="summary-item">
          <span className="label">Total Accounts:</span>
          <span className="value">{filteredAccounts.length}</span>
        </div>
        <div className="summary-item">
          <span className="label">Active:</span>
          <span className="value">{filteredAccounts.filter(a => a.active).length}</span>
        </div>
        <div className="summary-item">
          <span className="label">Reconcilable:</span>
          <span className="value">{filteredAccounts.filter(a => a.reconcile).length}</span>
        </div>
      </div>

    </div>
  );
};

export default ChartOfAccounts;
