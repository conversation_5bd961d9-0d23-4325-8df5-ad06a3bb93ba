import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { accountingAPI } from '../../services/api';
import Button from '../../components/common/Button/Button';
import CreateAccountModal from '../../components/accounting/CreateAccountModal';
import { createSampleAccounts } from '../../utils/sampleData';
import './ChartOfAccounts.css';

const ChartOfAccounts = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [accounts, setAccounts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [showCreateForm, setShowCreateForm] = useState(false);

  useEffect(() => {
    console.log('🔐 Auth state:', { user, isAuthenticated: !!user });
    console.log('🔑 Access token:', localStorage.getItem('access_token') ? 'Present' : 'Missing');
    console.log('🔄 Refresh token:', localStorage.getItem('refresh_token') ? 'Present' : 'Missing');

    if (user) {
      loadAccounts();
    } else {
      console.log('⚠️ User not authenticated, redirecting to login...');
      navigate('/login');
    }
  }, [user, navigate]);

  const loadAccounts = async () => {
    try {
      setLoading(true);
      console.log('🔄 Loading accounts from API...');
      console.log('🌐 API Base URL:', process.env.REACT_APP_API_URL || 'http://localhost:8000');

      const response = await accountingAPI.getAllAccounts(); // Get ALL accounts with pagination handling
      console.log('📊 Accounts API response:', response);
      console.log('📊 Response type:', typeof response);
      console.log('📊 Response keys:', Object.keys(response || {}));
      console.log('📊 Total count:', response.count);
      console.log('📊 Results length:', response.results?.length);
      console.log('📊 Has next page:', response.next);
      console.log('📊 Has previous page:', response.previous);

      // Handle paginated responses - fetch all pages if needed
      let allAccounts = [];

      if (response.results) {
        // Paginated response
        allAccounts = [...response.results];
        console.log(`📋 First page: ${response.results.length} accounts`);

        // If there are more pages, fetch them all
        let nextUrl = response.next;
        while (nextUrl) {
          console.log('� Fetching next page:', nextUrl);
          try {
            const nextResponse = await fetch(nextUrl, {
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
                'Content-Type': 'application/json'
              }
            });
            const nextData = await nextResponse.json();
            allAccounts = [...allAccounts, ...nextData.results];
            nextUrl = nextData.next;
            console.log(`📋 Added ${nextData.results.length} more accounts. Total: ${allAccounts.length}`);
          } catch (pageError) {
            console.error('❌ Error fetching next page:', pageError);
            break;
          }
        }
      } else {
        // Non-paginated response
        allAccounts = Array.isArray(response) ? response : [];
      }

      console.log('📋 Final accounts data:', allAccounts);
      console.log('📋 Total accounts loaded:', allAccounts.length);

      if (allAccounts.length > 0) {
        setAccounts(allAccounts);
        console.log(`✅ Loaded ${allAccounts.length} accounts from API`);
        console.log('📋 First account:', allAccounts[0]);
        console.log('📋 Last account:', allAccounts[allAccounts.length - 1]);

        // Show sample of account codes for verification
        const codes = allAccounts.slice(0, 10).map(acc => acc.code).join(', ');
        console.log('📋 Sample account codes:', codes);
      } else {
        console.log('⚠️ No accounts found in API response');
        console.log('🔍 Creating sample accounts for testing...');

        // Create sample accounts if none exist
        try {
          await createSampleAccounts(accountingAPI);
          console.log('✅ Sample accounts created, reloading...');

          // Reload accounts after creating samples
          const newResponse = await accountingAPI.getAccounts();
          const newAccountsData = newResponse.results || newResponse;
          setAccounts(Array.isArray(newAccountsData) ? newAccountsData : []);

        } catch (createError) {
          console.error('❌ Error creating sample accounts:', createError);
          setAccounts([]);
        }
      }

    } catch (error) {
      console.error('❌ Error loading accounts:', error);
      console.error('❌ Error details:', error.response?.data);
      console.error('❌ Error status:', error.response?.status);
      setAccounts([]);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateAccount = () => {
    setShowCreateForm(true);
  };

  const handleAccountCreated = (newAccount) => {
    console.log('New account created:', newAccount);
    // Add the new account to the list
    setAccounts(prevAccounts => [newAccount, ...prevAccounts]);
    // Optionally reload all accounts to ensure consistency
    loadAccounts();
    setShowCreateForm(false);
  };

  const handleCancelCreate = () => {
    setShowCreateForm(false);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(Math.abs(amount));
  };

  const getAccountTypeLabel = (type) => {
    const types = {
      'asset_cash': 'Cash & Bank',
      'asset_receivable': 'Receivable',
      'asset_current': 'Current Asset',
      'asset_fixed': 'Fixed Asset',
      'liability_payable': 'Payable',
      'liability_current': 'Current Liability',
      'liability_non_current': 'Non-current Liability',
      'equity': 'Equity',
      'income': 'Income',
      'expense': 'Expense',
      'expense_direct_cost': 'Cost of Revenue'
    };
    return types[type] || type;
  };

  const getAccountTypeColor = (type) => {
    const colors = {
      'asset_cash': '#28a745',
      'asset_receivable': '#17a2b8',
      'asset_current': '#20c997',
      'asset_fixed': '#6f42c1',
      'liability_payable': '#dc3545',
      'liability_current': '#fd7e14',
      'liability_non_current': '#e83e8c',
      'equity': '#6610f2',
      'income': '#28a745',
      'expense': '#ffc107',
      'expense_direct_cost': '#fd7e14'
    };
    return colors[type] || '#6c757d';
  };

  const filteredAccounts = accounts.filter(account => {
    const matchesSearch = account.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         account.code.includes(searchTerm);
    const matchesFilter = filterType === 'all' || account.account_type.includes(filterType);
    return matchesSearch && matchesFilter;
  });

  const accountTypes = [
    { value: 'all', label: 'All Types' },
    { value: 'asset', label: 'Assets' },
    { value: 'liability', label: 'Liabilities' },
    { value: 'equity', label: 'Equity' },
    { value: 'income', label: 'Income' },
    { value: 'expense', label: 'Expenses' }
  ];

  if (loading) {
    return (
      <div className="chart-of-accounts loading">
        <div className="loading-spinner">
          <i className="fas fa-spinner fa-spin"></i>
          <p>Loading chart of accounts...</p>
        </div>
      </div>
    );
  }

  // Show create form if requested
  if (showCreateForm) {
    return (
      <CreateAccountModal
        isOpen={true}
        onClose={handleCancelCreate}
        onAccountCreated={handleAccountCreated}
      />
    );
  }

  return (
    <div className="chart-of-accounts">
      {/* Debug Panel - Remove in production */}
      {process.env.NODE_ENV === 'development' && (
        <div style={{
          background: '#f8f9fa',
          padding: '1rem',
          margin: '1rem 0',
          borderRadius: '4px',
          fontSize: '0.875rem',
          fontFamily: 'monospace'
        }}>
          <strong>🔧 Debug Info:</strong><br/>
          User: {user ? `${user.username} (${user.email})` : 'Not logged in'}<br/>
          Access Token: {localStorage.getItem('access_token') ? '✅ Present' : '❌ Missing'}<br/>
          <strong>Accounts Count: {accounts.length}</strong> (Expected: 40+)<br/>
          Loading: {loading ? 'Yes' : 'No'}<br/>
          API URL: {process.env.REACT_APP_API_URL || 'http://localhost:8000'}<br/>
          <button
            onClick={() => console.log('Current accounts:', accounts)}
            style={{marginTop: '0.5rem', padding: '0.25rem 0.5rem', fontSize: '0.75rem'}}
          >
            Log Accounts to Console
          </button>
        </div>
      )}

      {/* Header */}
      <div className="page-header">
        <div className="header-content">
          <h1>
            <i className="fas fa-list-alt"></i>
            Chart of Accounts
          </h1>
          <p>Manage your accounting structure</p>
        </div>
        <div className="header-actions">
          <Button
            variant="secondary"
            icon="fas fa-arrow-left"
            onClick={() => navigate('/accounting')}
          >
            Back to Accounting
          </Button>

          <Button
            variant="outline"
            icon={`fas fa-sync-alt ${loading ? 'fa-spin' : ''}`}
            onClick={loadAccounts}
            disabled={loading}
          >
            {loading ? 'Loading...' : 'Refresh'}
          </Button>

          {user?.permissions?.can_create !== false && (
            <Button
              variant="primary"
              icon="fas fa-plus"
              onClick={handleCreateAccount}
            >
              New Account
            </Button>
          )}
        </div>
      </div>

      {/* Filters */}
      <div className="filters-section">
        <div className="search-box">
          <i className="fas fa-search"></i>
          <input
            type="text"
            placeholder="Search accounts by name or code..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        <div className="filter-dropdown">
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
          >
            {accountTypes.map(type => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Accounts Table */}
      <div className="accounts-table-container">
        <table className="accounts-table">
          <thead>
            <tr>
              <th>Code</th>
              <th>Account Name</th>
              <th>Type</th>
              <th>Balance</th>
              <th>Reconcile</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredAccounts.map(account => (
              <tr key={account.id} className={!account.active ? 'inactive' : ''}>
                <td className="account-code">{account.code}</td>
                <td className="account-name">
                  <div className="name-cell">
                    <span className="name">{account.name}</span>
                  </div>
                </td>
                <td>
                  <span 
                    className="account-type-badge"
                    style={{ backgroundColor: getAccountTypeColor(account.account_type) }}
                  >
                    {getAccountTypeLabel(account.account_type)}
                  </span>
                </td>
                <td className={`balance ${account.balance < 0 ? 'credit' : 'debit'}`}>
                  {formatCurrency(account.balance)}
                  <span className="balance-type">
                    {account.balance < 0 ? 'Cr' : 'Dr'}
                  </span>
                </td>
                <td>
                  {account.reconcile ? (
                    <span className="reconcile-badge active">
                      <i className="fas fa-check"></i>
                      Yes
                    </span>
                  ) : (
                    <span className="reconcile-badge inactive">
                      <i className="fas fa-times"></i>
                      No
                    </span>
                  )}
                </td>
                <td>
                  <span className={`status-badge ${account.active ? 'active' : 'inactive'}`}>
                    {account.active ? 'Active' : 'Inactive'}
                  </span>
                </td>
                <td className="actions">
                  <div className="action-buttons">
                    <button className="btn-icon" title="View Ledger">
                      <i className="fas fa-book"></i>
                    </button>
                    {user?.permissions?.can_update && (
                      <button className="btn-icon" title="Edit Account">
                        <i className="fas fa-edit"></i>
                      </button>
                    )}
                    {user?.permissions?.can_delete && (
                      <button className="btn-icon danger" title="Delete Account">
                        <i className="fas fa-trash"></i>
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {filteredAccounts.length === 0 && (
          <div className="empty-state">
            <i className="fas fa-search"></i>
            <h3>No accounts found</h3>
            <p>Try adjusting your search criteria or create a new account.</p>
          </div>
        )}
      </div>

      {/* Summary */}
      <div className="accounts-summary">
        <div className="summary-item">
          <span className="label">Total Accounts:</span>
          <span className="value">{filteredAccounts.length}</span>
        </div>
        <div className="summary-item">
          <span className="label">Active:</span>
          <span className="value">{filteredAccounts.filter(a => a.active).length}</span>
        </div>
        <div className="summary-item">
          <span className="label">Reconcilable:</span>
          <span className="value">{filteredAccounts.filter(a => a.reconcile).length}</span>
        </div>
      </div>

    </div>
  );
};

export default ChartOfAccounts;
