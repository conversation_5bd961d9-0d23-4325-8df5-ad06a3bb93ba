import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { accountingAPI } from '../../services/api';
import Button from '../../components/common/Button/Button';
import CreateAccountModal from '../../components/accounting/CreateAccountModal';
import './ChartOfAccounts.css';

const ChartOfAccounts = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [accounts, setAccounts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [showCreateModal, setShowCreateModal] = useState(false);

  useEffect(() => {
    loadAccounts();
  }, []);

  const loadAccounts = async () => {
    try {
      setLoading(true);
      console.log('Loading accounts from API...');

      const response = await accountingAPI.getAccounts();
      console.log('Accounts API response:', response);

      // Handle both paginated and non-paginated responses
      const accountsData = response.results || response;
      setAccounts(accountsData);

    } catch (error) {
      console.error('Error loading accounts:', error);
      // Mock data for demonstration if API fails
      setAccounts([
        {
          id: 1,
          code: '1000',
          name: 'Cash',
          account_type: 'asset_cash',
          balance: 25000,
          reconcile: false,
          active: true
        },
        {
          id: 2,
          code: '1100',
          name: 'Bank Account',
          account_type: 'asset_cash',
          balance: 150000,
          reconcile: true,
          active: true
        },
        {
          id: 3,
          code: '1200',
          name: 'Accounts Receivable',
          account_type: 'asset_receivable',
          balance: 75000,
          reconcile: true,
          active: true
        },
        {
          id: 4,
          code: '2000',
          name: 'Accounts Payable',
          account_type: 'liability_payable',
          balance: -45000,
          reconcile: true,
          active: true
        },
        {
          id: 5,
          code: '4000',
          name: 'Sales Revenue',
          account_type: 'income',
          balance: -180000,
          reconcile: false,
          active: true
        },
        {
          id: 6,
          code: '5000',
          name: 'Cost of Goods Sold',
          account_type: 'expense_direct_cost',
          balance: 95000,
          reconcile: false,
          active: true
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const handleAccountCreated = (newAccount) => {
    console.log('New account created:', newAccount);
    // Add the new account to the list
    setAccounts(prevAccounts => [newAccount, ...prevAccounts]);
    // Optionally reload all accounts to ensure consistency
    loadAccounts();
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(Math.abs(amount));
  };

  const getAccountTypeLabel = (type) => {
    const types = {
      'asset_cash': 'Cash & Bank',
      'asset_receivable': 'Receivable',
      'asset_current': 'Current Asset',
      'asset_fixed': 'Fixed Asset',
      'liability_payable': 'Payable',
      'liability_current': 'Current Liability',
      'liability_non_current': 'Non-current Liability',
      'equity': 'Equity',
      'income': 'Income',
      'expense': 'Expense',
      'expense_direct_cost': 'Cost of Revenue'
    };
    return types[type] || type;
  };

  const getAccountTypeColor = (type) => {
    const colors = {
      'asset_cash': '#28a745',
      'asset_receivable': '#17a2b8',
      'asset_current': '#20c997',
      'asset_fixed': '#6f42c1',
      'liability_payable': '#dc3545',
      'liability_current': '#fd7e14',
      'liability_non_current': '#e83e8c',
      'equity': '#6610f2',
      'income': '#28a745',
      'expense': '#ffc107',
      'expense_direct_cost': '#fd7e14'
    };
    return colors[type] || '#6c757d';
  };

  const filteredAccounts = accounts.filter(account => {
    const matchesSearch = account.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         account.code.includes(searchTerm);
    const matchesFilter = filterType === 'all' || account.account_type.includes(filterType);
    return matchesSearch && matchesFilter;
  });

  const accountTypes = [
    { value: 'all', label: 'All Types' },
    { value: 'asset', label: 'Assets' },
    { value: 'liability', label: 'Liabilities' },
    { value: 'equity', label: 'Equity' },
    { value: 'income', label: 'Income' },
    { value: 'expense', label: 'Expenses' }
  ];

  if (loading) {
    return (
      <div className="chart-of-accounts loading">
        <div className="loading-spinner">
          <i className="fas fa-spinner fa-spin"></i>
          <p>Loading chart of accounts...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="chart-of-accounts">
      {/* Header */}
      <div className="page-header">
        <div className="header-content">
          <h1>
            <i className="fas fa-list-alt"></i>
            Chart of Accounts
          </h1>
          <p>Manage your accounting structure</p>
        </div>
        <div className="header-actions">
          <Button
            variant="secondary"
            icon="fas fa-arrow-left"
            onClick={() => navigate('/accounting')}
          >
            Back to Accounting
          </Button>
          {user?.permissions?.can_create && (
            <Button
              variant="primary"
              icon="fas fa-plus"
              onClick={() => setShowCreateModal(true)}
            >
              New Account
            </Button>
          )}
        </div>
      </div>

      {/* Filters */}
      <div className="filters-section">
        <div className="search-box">
          <i className="fas fa-search"></i>
          <input
            type="text"
            placeholder="Search accounts by name or code..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        <div className="filter-dropdown">
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
          >
            {accountTypes.map(type => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Accounts Table */}
      <div className="accounts-table-container">
        <table className="accounts-table">
          <thead>
            <tr>
              <th>Code</th>
              <th>Account Name</th>
              <th>Type</th>
              <th>Balance</th>
              <th>Reconcile</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredAccounts.map(account => (
              <tr key={account.id} className={!account.active ? 'inactive' : ''}>
                <td className="account-code">{account.code}</td>
                <td className="account-name">
                  <div className="name-cell">
                    <span className="name">{account.name}</span>
                  </div>
                </td>
                <td>
                  <span 
                    className="account-type-badge"
                    style={{ backgroundColor: getAccountTypeColor(account.account_type) }}
                  >
                    {getAccountTypeLabel(account.account_type)}
                  </span>
                </td>
                <td className={`balance ${account.balance < 0 ? 'credit' : 'debit'}`}>
                  {formatCurrency(account.balance)}
                  <span className="balance-type">
                    {account.balance < 0 ? 'Cr' : 'Dr'}
                  </span>
                </td>
                <td>
                  {account.reconcile ? (
                    <span className="reconcile-badge active">
                      <i className="fas fa-check"></i>
                      Yes
                    </span>
                  ) : (
                    <span className="reconcile-badge inactive">
                      <i className="fas fa-times"></i>
                      No
                    </span>
                  )}
                </td>
                <td>
                  <span className={`status-badge ${account.active ? 'active' : 'inactive'}`}>
                    {account.active ? 'Active' : 'Inactive'}
                  </span>
                </td>
                <td className="actions">
                  <div className="action-buttons">
                    <button className="btn-icon" title="View Ledger">
                      <i className="fas fa-book"></i>
                    </button>
                    {user?.permissions?.can_update && (
                      <button className="btn-icon" title="Edit Account">
                        <i className="fas fa-edit"></i>
                      </button>
                    )}
                    {user?.permissions?.can_delete && (
                      <button className="btn-icon danger" title="Delete Account">
                        <i className="fas fa-trash"></i>
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {filteredAccounts.length === 0 && (
          <div className="empty-state">
            <i className="fas fa-search"></i>
            <h3>No accounts found</h3>
            <p>Try adjusting your search criteria or create a new account.</p>
          </div>
        )}
      </div>

      {/* Summary */}
      <div className="accounts-summary">
        <div className="summary-item">
          <span className="label">Total Accounts:</span>
          <span className="value">{filteredAccounts.length}</span>
        </div>
        <div className="summary-item">
          <span className="label">Active:</span>
          <span className="value">{filteredAccounts.filter(a => a.active).length}</span>
        </div>
        <div className="summary-item">
          <span className="label">Reconcilable:</span>
          <span className="value">{filteredAccounts.filter(a => a.reconcile).length}</span>
        </div>
      </div>

      {/* Create Account Modal */}
      <CreateAccountModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onAccountCreated={handleAccountCreated}
      />
    </div>
  );
};

export default ChartOfAccounts;
