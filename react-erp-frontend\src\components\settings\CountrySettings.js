import React, { useState, useEffect } from 'react';
import Button from '../common/Button/Button';

const CountrySettings = ({ onDataChange }) => {
  const [countries, setCountries] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadCountries();
  }, []);

  const loadCountries = async () => {
    try {
      setLoading(true);
      // Mock countries data
      const mockCountries = [
        { id: 1, name: 'United States', code: 'US', active: true, currency: 'USD' },
        { id: 2, name: 'Canada', code: 'CA', active: true, currency: 'CAD' },
        { id: 3, name: 'United Kingdom', code: 'GB', active: true, currency: 'GBP' },
        { id: 4, name: 'Germany', code: 'DE', active: true, currency: 'EUR' },
        { id: 5, name: 'France', code: 'FR', active: true, currency: 'EUR' }
      ];
      setCountries(mockCountries);
    } catch (error) {
      console.error('Error loading countries:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="loading-spinner">
        <i className="fas fa-spinner fa-spin"></i>
        <p>Loading countries...</p>
      </div>
    );
  }

  return (
    <div className="country-settings">
      <div className="settings-section">
        <h3>
          <i className="fas fa-globe"></i>
          Country Management
        </h3>
        <p>Manage countries and their localization settings.</p>
        
        <div className="country-list">
          {countries.map(country => (
            <div key={country.id} className="country-item">
              <div className="country-info">
                <h4>{country.name}</h4>
                <span className="country-code">{country.code}</span>
              </div>
              <div className="country-actions">
                <span className={`status ${country.active ? 'active' : 'inactive'}`}>
                  {country.active ? 'Active' : 'Inactive'}
                </span>
              </div>
            </div>
          ))}
        </div>
        
        <div className="section-actions">
          <Button variant="primary" icon="fas fa-plus">
            Add Country
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CountrySettings;
