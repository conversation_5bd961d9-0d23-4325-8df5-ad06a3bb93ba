import React, { useState, useEffect } from 'react';
import { settingsAPI } from '../../services/api';
import Button from '../common/Button/Button';
import './CountrySettings.css';

const CountrySettings = ({ onDataChange }) => {
  const [countries, setCountries] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    loadCountries();
  }, []);

  const loadCountries = async () => {
    try {
      setLoading(true);
      console.log('Loading countries from API...');

      const response = await settingsAPI.getCountries();
      console.log('Countries API response:', response);

      const countriesData = response.results || response;
      setCountries(countriesData);
    } catch (error) {
      console.error('Error loading countries:', error);
      // Fallback to mock data
      setCountries([
        { id: 1, name: 'United States', code: 'US', active: true, phone_code: 1 },
        { id: 2, name: 'Canada', code: 'CA', active: true, phone_code: 1 },
        { id: 3, name: 'United Kingdom', code: 'GB', active: true, phone_code: 44 },
        { id: 4, name: 'Germany', code: 'DE', active: true, phone_code: 49 },
        { id: 5, name: 'France', code: 'FR', active: true, phone_code: 33 }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleActive = async (countryId) => {
    try {
      const country = countries.find(c => c.id === countryId);
      const newActiveState = !country.active;

      console.log('Toggling country active state:', countryId, newActiveState);

      // Update via API
      await settingsAPI.updateCountry(countryId, { active: newActiveState });

      // Update local state
      setCountries(prev => prev.map(country =>
        country.id === countryId
          ? { ...country, active: newActiveState }
          : country
      ));
      onDataChange();
    } catch (error) {
      console.error('Error toggling country active state:', error);
      alert('Failed to update country status. Please try again.');
    }
  };

  const filteredCountries = countries.filter(country =>
    country.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    country.code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="country-settings loading">
        <div className="loading-spinner">
          <i className="fas fa-spinner fa-spin"></i>
          <p>Loading countries...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="country-settings">
      {/* Header Actions */}
      <div className="country-header">
        <div className="search-section">
          <div className="search-box">
            <i className="fas fa-search"></i>
            <input
              type="text"
              placeholder="Search countries..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        <div className="header-actions">
          <Button
            variant="primary"
            icon="fas fa-plus"
            onClick={() => console.log('Add country')}
          >
            Add Country
          </Button>
        </div>
      </div>

      {/* Country List */}
      <div className="country-list">
        <div className="list-header">
          <h3>
            <i className="fas fa-globe"></i>
            Countries ({filteredCountries.length})
          </h3>
          <p>Manage countries and their localization settings</p>
        </div>

        <div className="country-table">
          <div className="table-header">
            <div className="col-country">Country</div>
            <div className="col-code">Code</div>
            <div className="col-phone">Phone Code</div>
            <div className="col-status">Status</div>
            <div className="col-actions">Actions</div>
          </div>

          <div className="table-body">
            {filteredCountries.map(country => (
              <div key={country.id} className={`country-row ${!country.active ? 'inactive' : ''}`}>
                <div className="col-country">
                  <div className="country-info">
                    <div className="country-flag">
                      <i className="fas fa-flag"></i>
                    </div>
                    <div className="country-details">
                      <h4>{country.name}</h4>
                      <span className="country-code">{country.code}</span>
                    </div>
                  </div>
                </div>

                <div className="col-code">
                  <span className="code-badge">{country.code}</span>
                </div>

                <div className="col-phone">
                  <span className="phone-code">+{country.phone_code}</span>
                </div>

                <div className="col-status">
                  <div className="status-controls">
                    <label className="toggle-switch">
                      <input
                        type="checkbox"
                        checked={country.active}
                        onChange={() => handleToggleActive(country.id)}
                      />
                      <span className="toggle-slider"></span>
                    </label>
                    <span className={`status-label ${country.active ? 'active' : 'inactive'}`}>
                      {country.active ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>

                <div className="col-actions">
                  <div className="action-buttons">
                    <button
                      className="btn-action"
                      onClick={() => console.log('Edit country', country.id)}
                      title="Edit country"
                    >
                      <i className="fas fa-edit"></i>
                    </button>
                    <button
                      className="btn-action danger"
                      onClick={() => console.log('Delete country', country.id)}
                      title="Delete country"
                    >
                      <i className="fas fa-trash"></i>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {filteredCountries.length === 0 && (
          <div className="empty-state">
            <i className="fas fa-globe"></i>
            <h3>No countries found</h3>
            <p>Try adjusting your search criteria or add a new country.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default CountrySettings;
