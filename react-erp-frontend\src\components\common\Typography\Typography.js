import React from 'react';
import './Typography.css';

// Heading Component
export const Heading = ({ 
  level = 1, 
  children, 
  className = '', 
  variant = 'default',
  ...props 
}) => {
  const Tag = `h${level}`;
  const headingClass = [
    `odoo-heading-${level}`,
    `odoo-h${level}`,
    variant !== 'default' ? `odoo-heading--${variant}` : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <Tag className={headingClass} {...props}>
      {children}
    </Tag>
  );
};

// Text Component
export const Text = ({ 
  variant = 'body', 
  size = 'base',
  weight = 'normal',
  color = 'primary',
  children, 
  className = '', 
  as = 'span',
  ...props 
}) => {
  const Tag = as;
  const textClass = [
    `odoo-text-${variant}`,
    size !== 'base' ? `odoo-text-${size}` : '',
    weight !== 'normal' ? `odoo-font-${weight}` : '',
    color !== 'primary' ? `odoo-text-${color}` : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <Tag className={textClass} {...props}>
      {children}
    </Tag>
  );
};

// Number/Digits Component
export const Number = ({ 
  value, 
  size = 'medium',
  variant = 'default',
  children, 
  className = '', 
  ...props 
}) => {
  const numberClass = [
    'odoo-number',
    `odoo-number-${size}`,
    variant !== 'default' ? `odoo-number--${variant}` : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <span className={numberClass} {...props}>
      {children || value}
    </span>
  );
};

// Code Component (for account codes, etc.)
export const Code = ({ 
  children, 
  size = 'medium',
  className = '', 
  ...props 
}) => {
  const codeClass = [
    'odoo-code',
    size !== 'medium' ? `odoo-code-${size}` : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <span className={codeClass} {...props}>
      {children}
    </span>
  );
};

// Label Component
export const Label = ({ 
  children, 
  size = 'normal',
  required = false,
  className = '', 
  htmlFor,
  ...props 
}) => {
  const labelClass = [
    size === 'small' ? 'odoo-label-small' : 'odoo-label',
    className
  ].filter(Boolean).join(' ');

  return (
    <label className={labelClass} htmlFor={htmlFor} {...props}>
      {children}
      {required && <span className="odoo-required-indicator">*</span>}
    </label>
  );
};

// Focus Area Component (for main content areas)
export const FocusArea = ({ 
  level = 'primary',
  children, 
  className = '', 
  as = 'div',
  ...props 
}) => {
  const Tag = as;
  const focusClass = [
    `odoo-focus-${level}`,
    className
  ].filter(Boolean).join(' ');

  return (
    <Tag className={focusClass} {...props}>
      {children}
    </Tag>
  );
};

// Link Component
export const Link = ({ 
  children, 
  href, 
  onClick,
  className = '', 
  ...props 
}) => {
  const linkClass = [
    'odoo-link',
    className
  ].filter(Boolean).join(' ');

  if (href) {
    return (
      <a href={href} className={linkClass} {...props}>
        {children}
      </a>
    );
  }

  return (
    <button type="button" className={linkClass} onClick={onClick} {...props}>
      {children}
    </button>
  );
};

// Badge Text Component
export const Badge = ({ 
  children, 
  variant = 'default',
  className = '', 
  ...props 
}) => {
  const badgeClass = [
    'odoo-badge-text',
    variant !== 'default' ? `odoo-badge--${variant}` : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <span className={badgeClass} {...props}>
      {children}
    </span>
  );
};

// Export all components
export default {
  Heading,
  Text,
  Number,
  Code,
  Label,
  FocusArea,
  Link,
  Badge
};
