import axios from 'axios';

// Base API configuration
const API_BASE_URL = 'http://localhost:8000/api/v1';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = localStorage.getItem('refresh_token');
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/auth/token/refresh/`, {
            refresh: refreshToken,
          });

          const { access } = response.data;
          localStorage.setItem('access_token', access);

          // Retry the original request with new token
          originalRequest.headers.Authorization = `Bearer ${access}`;
          return api(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('user');
        window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  }
);

// Auth API endpoints
export const authAPI = {
  // Login user
  login: async (credentials) => {
    try {
      const response = await api.post('/auth/login/', credentials);
      const { access, refresh, user } = response.data;
      
      // Store tokens and user info
      localStorage.setItem('access_token', access);
      localStorage.setItem('refresh_token', refresh);
      localStorage.setItem('user', JSON.stringify(user));
      
      return response.data;
    } catch (error) {
      throw error.response?.data || { message: 'Login failed' };
    }
  },

  // Logout user
  logout: async () => {
    try {
      const refreshToken = localStorage.getItem('refresh_token');
      if (refreshToken) {
        await api.post('/auth/logout/', { refresh: refreshToken });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear local storage regardless of API call success
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('user');
    }
  },

  // Get current user profile
  getProfile: async () => {
    const response = await api.get('/auth/profile/');
    return response.data;
  },

  // Update user profile
  updateProfile: async (profileData) => {
    const response = await api.patch('/auth/profile/', profileData);
    return response.data;
  },

  // Change password
  changePassword: async (passwordData) => {
    const response = await api.post('/auth/change-password/', passwordData);
    return response.data;
  },
};

// Core API endpoints
export const coreAPI = {
  // Companies
  getCompanies: async () => {
    const response = await api.get('/core/companies/');
    return response.data;
  },

  // Partners (Customers/Vendors)
  getPartners: async (params = {}) => {
    const response = await api.get('/core/partners/', { params });
    return response.data;
  },

  createPartner: async (partnerData) => {
    const response = await api.post('/core/partners/', partnerData);
    return response.data;
  },

  updatePartner: async (id, partnerData) => {
    const response = await api.patch(`/core/partners/${id}/`, partnerData);
    return response.data;
  },

  deletePartner: async (id) => {
    const response = await api.delete(`/core/partners/${id}/`);
    return response.data;
  },
};

// Accounting API endpoints
export const accountingAPI = {
  // Chart of Accounts
  getAccounts: async (params = {}) => {
    const response = await api.get('/accounting/accounts/', { params });
    return response.data;
  },

  // Journal Entries
  getJournalEntries: async (params = {}) => {
    const response = await api.get('/accounting/moves/', { params });
    return response.data;
  },

  createJournalEntry: async (entryData) => {
    const response = await api.post('/accounting/moves/', entryData);
    return response.data;
  },
};

// Sales API endpoints
export const salesAPI = {
  // Sales Orders
  getSalesOrders: async (params = {}) => {
    const response = await api.get('/sales/orders/', { params });
    return response.data;
  },

  createSalesOrder: async (orderData) => {
    const response = await api.post('/sales/orders/', orderData);
    return response.data;
  },
};

// Inventory API endpoints
export const inventoryAPI = {
  // Products
  getProducts: async (params = {}) => {
    const response = await api.get('/inventory/products/', { params });
    return response.data;
  },

  createProduct: async (productData) => {
    const response = await api.post('/inventory/products/', productData);
    return response.data;
  },
};

// HR API endpoints
export const hrAPI = {
  // Employees
  getEmployees: async (params = {}) => {
    const response = await api.get('/hr/employees/', { params });
    return response.data;
  },

  createEmployee: async (employeeData) => {
    const response = await api.post('/hr/employees/', employeeData);
    return response.data;
  },
};

// Utility functions
export const apiUtils = {
  // Check if user is authenticated
  isAuthenticated: () => {
    const token = localStorage.getItem('access_token');
    const user = localStorage.getItem('user');
    return !!(token && user);
  },

  // Get current user from localStorage
  getCurrentUser: () => {
    const user = localStorage.getItem('user');
    return user ? JSON.parse(user) : null;
  },

  // Format API errors for display
  formatError: (error) => {
    if (error.response?.data) {
      const errorData = error.response.data;
      
      // Handle validation errors
      if (errorData.errors) {
        return Object.values(errorData.errors).flat().join(', ');
      }
      
      // Handle single error message
      if (errorData.message) {
        return errorData.message;
      }
      
      // Handle Django field errors
      if (typeof errorData === 'object') {
        const messages = [];
        Object.keys(errorData).forEach(key => {
          if (Array.isArray(errorData[key])) {
            messages.push(...errorData[key]);
          } else {
            messages.push(errorData[key]);
          }
        });
        return messages.join(', ');
      }
    }
    
    return error.message || 'An unexpected error occurred';
  },
};

export default api;
