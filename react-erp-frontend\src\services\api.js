import axios from 'axios';

// Base API configuration
const API_BASE_URL = 'http://localhost:8000/api/v1';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = localStorage.getItem('refresh_token');
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/auth/token/refresh/`, {
            refresh: refreshToken,
          });

          const { access } = response.data;
          localStorage.setItem('access_token', access);

          // Retry the original request with new token
          originalRequest.headers.Authorization = `Bearer ${access}`;
          return api(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('user');
        window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  }
);

// Auth API endpoints
export const authAPI = {
  // Login user
  login: async (credentials) => {
    try {
      const response = await api.post('/auth/login/', credentials);
      const { access, refresh, user } = response.data;
      
      // Store tokens and user info
      localStorage.setItem('access_token', access);
      localStorage.setItem('refresh_token', refresh);
      localStorage.setItem('user', JSON.stringify(user));
      
      return response.data;
    } catch (error) {
      throw error.response?.data || { message: 'Login failed' };
    }
  },

  // Logout user
  logout: async () => {
    try {
      const refreshToken = localStorage.getItem('refresh_token');
      if (refreshToken) {
        await api.post('/auth/logout/', { refresh: refreshToken });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear local storage regardless of API call success
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('user');
    }
  },

  // Get current user profile
  getProfile: async () => {
    const response = await api.get('/auth/profile/');
    return response.data;
  },

  // Update user profile
  updateProfile: async (profileData) => {
    const response = await api.patch('/auth/profile/', profileData);
    return response.data;
  },

  // Change password
  changePassword: async (passwordData) => {
    const response = await api.post('/auth/change-password/', passwordData);
    return response.data;
  },
};

// Core API endpoints
export const coreAPI = {
  // Companies
  getCompanies: async () => {
    const response = await api.get('/core/companies/');
    return response.data;
  },

  // Partners (Customers/Vendors)
  getPartners: async (params = {}) => {
    const response = await api.get('/core/partners/', { params });
    return response.data;
  },

  createPartner: async (partnerData) => {
    const response = await api.post('/core/partners/', partnerData);
    return response.data;
  },

  updatePartner: async (id, partnerData) => {
    const response = await api.patch(`/core/partners/${id}/`, partnerData);
    return response.data;
  },

  deletePartner: async (id) => {
    const response = await api.delete(`/core/partners/${id}/`);
    return response.data;
  },
};

// Accounting API endpoints
export const accountingAPI = {
  // Dashboard Statistics
  getDashboardStats: async () => {
    const response = await api.get('/accounting/accounts/dashboard_stats/');
    return response.data;
  },

  // Chart of Accounts - Get all accounts (handle pagination)
  getAccounts: async (params = {}) => {
    // Set a large page size to get all accounts in one request
    const queryParams = { page_size: 1000, ...params };
    const response = await api.get('/accounting/accounts/', { params: queryParams });
    return response.data;
  },

  // Get all accounts with automatic pagination handling
  getAllAccounts: async (params = {}) => {
    let allAccounts = [];
    let nextUrl = '/accounting/accounts/';
    let queryParams = { page_size: 100, ...params }; // Changed const to let

    while (nextUrl) {
      const response = await api.get(nextUrl, { params: queryParams });
      const data = response.data;

      if (data.results) {
        allAccounts = [...allAccounts, ...data.results];
        nextUrl = data.next ? data.next.replace(api.defaults.baseURL, '') : null;
      } else {
        // Non-paginated response
        allAccounts = Array.isArray(data) ? data : [];
        break;
      }

      // Clear params for subsequent requests (they're in the nextUrl)
      queryParams = {};
    }

    return {
      results: allAccounts,
      count: allAccounts.length,
      next: null,
      previous: null
    };
  },

  createAccount: async (accountData) => {
    const response = await api.post('/accounting/accounts/', accountData);
    return response.data;
  },

  updateAccount: async (id, accountData) => {
    const response = await api.patch(`/accounting/accounts/${id}/`, accountData);
    return response.data;
  },

  deleteAccount: async (id) => {
    const response = await api.delete(`/accounting/accounts/${id}/`);
    return response.data;
  },

  // Account Groups
  getAccountGroups: async (params = {}) => {
    const response = await api.get('/accounting/groups/', { params });
    return response.data;
  },

  // Journal Entries
  getJournalEntries: async (params = {}) => {
    const response = await api.get('/accounting/moves/', { params });
    return response.data;
  },

  createJournalEntry: async (entryData) => {
    const response = await api.post('/accounting/moves/', entryData);
    return response.data;
  },

  // Journals
  getJournals: async (params = {}) => {
    const response = await api.get('/accounting/journals/', { params });
    return response.data;
  },
};

// Settings API endpoints
export const settingsAPI = {
  // Dashboard
  getDashboard: async () => {
    const response = await api.get('/settings/dashboard/');
    return response.data;
  },

  // Company Settings
  getCompany: async () => {
    const response = await api.get('/settings/companies/current/');
    return response.data;
  },

  updateCompany: async (companyData) => {
    const response = await api.patch('/settings/companies/update_current/', companyData);
    return response.data;
  },

  // Currency Settings
  getCurrencies: async (params = {}) => {
    const response = await api.get('/settings/currencies/', { params });
    return response.data;
  },

  createCurrency: async (currencyData) => {
    const response = await api.post('/settings/currencies/', currencyData);
    return response.data;
  },

  updateCurrency: async (id, currencyData) => {
    const response = await api.patch(`/settings/currencies/${id}/`, currencyData);
    return response.data;
  },

  deleteCurrency: async (id) => {
    const response = await api.delete(`/settings/currencies/${id}/`);
    return response.data;
  },

  setCurrencyAsBase: async (id) => {
    const response = await api.post(`/settings/currencies/${id}/set_base/`);
    return response.data;
  },

  updateCurrencyRate: async (id, rate) => {
    const response = await api.patch(`/settings/currencies/${id}/update_rate/`, { rate });
    return response.data;
  },

  // Country Settings
  getCountries: async (params = {}) => {
    const response = await api.get('/settings/countries/', { params });
    return response.data;
  },

  createCountry: async (countryData) => {
    const response = await api.post('/settings/countries/', countryData);
    return response.data;
  },

  updateCountry: async (id, countryData) => {
    const response = await api.patch(`/settings/countries/${id}/`, countryData);
    return response.data;
  },

  getCountryStates: async (countryId) => {
    const response = await api.get(`/settings/countries/${countryId}/states/`);
    return response.data;
  },

  // State Settings
  getStates: async (params = {}) => {
    const response = await api.get('/settings/states/', { params });
    return response.data;
  },

  createState: async (stateData) => {
    const response = await api.post('/settings/states/', stateData);
    return response.data;
  },

  updateState: async (id, stateData) => {
    const response = await api.patch(`/settings/states/${id}/`, stateData);
    return response.data;
  },

  // System Settings
  exportSettings: async () => {
    const response = await api.post('/settings/export_settings/');
    return response.data;
  },

  importSettings: async (settingsData) => {
    const response = await api.post('/settings/import_settings/', settingsData);
    return response.data;
  },

  resetDefaults: async () => {
    const response = await api.post('/settings/reset_defaults/');
    return response.data;
  },

  // Company Setup
  createCompanyWithSetup: async (setupData) => {
    const response = await api.post('/settings/companies/setup/', setupData);
    return response.data;
  },

  // Fiscal Year Settings
  getFiscalYears: async (params = {}) => {
    const response = await api.get('/settings/fiscal-years/', { params });
    return response.data;
  },

  createFiscalYear: async (fiscalYearData) => {
    const response = await api.post('/settings/fiscal-years/', fiscalYearData);
    return response.data;
  },

  updateFiscalYear: async (id, fiscalYearData) => {
    const response = await api.patch(`/settings/fiscal-years/${id}/`, fiscalYearData);
    return response.data;
  },

  deleteFiscalYear: async (id) => {
    const response = await api.delete(`/settings/fiscal-years/${id}/`);
    return response.data;
  },

  openFiscalYear: async (id) => {
    const response = await api.post(`/settings/fiscal-years/${id}/open_year/`);
    return response.data;
  },

  closeFiscalYear: async (id) => {
    const response = await api.post(`/settings/fiscal-years/${id}/close_year/`);
    return response.data;
  },
};

// Sales API endpoints
export const salesAPI = {
  // Sales Orders
  getSalesOrders: async (params = {}) => {
    const response = await api.get('/sales/orders/', { params });
    return response.data;
  },

  createSalesOrder: async (orderData) => {
    const response = await api.post('/sales/orders/', orderData);
    return response.data;
  },
};

// Inventory API endpoints
export const inventoryAPI = {
  // Products
  getProducts: async (params = {}) => {
    const response = await api.get('/inventory/products/', { params });
    return response.data;
  },

  createProduct: async (productData) => {
    const response = await api.post('/inventory/products/', productData);
    return response.data;
  },
};

// HR API endpoints
export const hrAPI = {
  // Employees
  getEmployees: async (params = {}) => {
    const response = await api.get('/hr/employees/', { params });
    return response.data;
  },

  createEmployee: async (employeeData) => {
    const response = await api.post('/hr/employees/', employeeData);
    return response.data;
  },
};

// Utility functions
export const apiUtils = {
  // Check if user is authenticated
  isAuthenticated: () => {
    const token = localStorage.getItem('access_token');
    const user = localStorage.getItem('user');
    return !!(token && user);
  },

  // Get current user from localStorage
  getCurrentUser: () => {
    const user = localStorage.getItem('user');
    return user ? JSON.parse(user) : null;
  },

  // Format API errors for display
  formatError: (error) => {
    if (error.response?.data) {
      const errorData = error.response.data;
      
      // Handle validation errors
      if (errorData.errors) {
        return Object.values(errorData.errors).flat().join(', ');
      }
      
      // Handle single error message
      if (errorData.message) {
        return errorData.message;
      }
      
      // Handle Django field errors
      if (typeof errorData === 'object') {
        const messages = [];
        Object.keys(errorData).forEach(key => {
          if (Array.isArray(errorData[key])) {
            messages.push(...errorData[key]);
          } else {
            messages.push(errorData[key]);
          }
        });
        return messages.join(', ');
      }
    }
    
    return error.message || 'An unexpected error occurred';
  },
};

export default api;
