import React from 'react';
import './Button.css';

const Button = ({ 
  children, 
  variant = 'primary', 
  size = 'medium', 
  type = 'button',
  disabled = false,
  loading = false,
  fullWidth = false,
  icon = null,
  onClick,
  className = '',
  ...props 
}) => {
  const buttonClass = [
    'odoo-btn',
    `odoo-btn--${variant}`,
    `odoo-btn--${size}`,
    fullWidth ? 'odoo-btn--full-width' : '',
    loading ? 'odoo-btn--loading' : '',
    disabled ? 'odoo-btn--disabled' : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <button
      type={type}
      className={buttonClass}
      disabled={disabled || loading}
      onClick={onClick}
      {...props}
    >
      {loading && (
        <i className="fas fa-spinner fa-spin odoo-btn__spinner"></i>
      )}
      {icon && !loading && (
        <i className={`${icon} odoo-btn__icon`}></i>
      )}
      <span className="odoo-btn__text">{children}</span>
    </button>
  );
};

export default Button;
