/* General Settings - Odoo Style */
.general-settings {
  min-height: 100vh;
  background-color: var(--odoo-bg-secondary);
  padding: var(--odoo-spacing-lg);
}

/* Settings Header */
.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--odoo-spacing-xl);
  background: var(--odoo-white);
  padding: var(--odoo-spacing-lg);
  border-radius: var(--odoo-border-radius-lg);
  box-shadow: var(--odoo-shadow-sm);
}

.header-content h1 {
  font-size: var(--odoo-font-size-2xl);
  font-weight: var(--odoo-font-weight-bold);
  color: var(--odoo-text-primary);
  margin-bottom: var(--odoo-spacing-xs);
  display: flex;
  align-items: center;
  gap: var(--odoo-spacing-sm);
}

.header-content h1 i {
  color: var(--odoo-primary);
}

.header-content p {
  color: var(--odoo-text-secondary);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: var(--odoo-spacing-sm);
  align-items: center;
}

/* Settings Container */
.settings-container {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: var(--odoo-spacing-lg);
  min-height: 600px;
}

/* Settings Sidebar */
.settings-sidebar {
  background: var(--odoo-white);
  border-radius: var(--odoo-border-radius-lg);
  box-shadow: var(--odoo-shadow-sm);
  overflow: hidden;
  height: fit-content;
}

.settings-nav {
  padding: var(--odoo-spacing-md);
}

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--odoo-spacing-md);
  padding: var(--odoo-spacing-md);
  border-radius: var(--odoo-border-radius);
  cursor: pointer;
  transition: all var(--odoo-transition-fast);
  margin-bottom: var(--odoo-spacing-xs);
  position: relative;
}

.nav-item:hover {
  background: var(--odoo-bg-light);
}

.nav-item.active {
  background: linear-gradient(135deg, var(--odoo-primary), var(--odoo-primary-light));
  color: var(--odoo-white);
}

.nav-item.active .nav-content h4,
.nav-item.active .nav-content p {
  color: var(--odoo-white);
}

.nav-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--odoo-border-radius);
  background: var(--odoo-bg-light);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--odoo-font-size-lg);
  color: var(--odoo-primary);
  flex-shrink: 0;
}

.nav-item.active .nav-icon {
  background: rgba(255, 255, 255, 0.2);
  color: var(--odoo-white);
}

.nav-content {
  flex: 1;
}

.nav-content h4 {
  font-size: var(--odoo-font-size-base);
  font-weight: var(--odoo-font-weight-semibold);
  color: var(--odoo-text-primary);
  margin-bottom: var(--odoo-spacing-xs);
}

.nav-content p {
  font-size: var(--odoo-font-size-xs);
  color: var(--odoo-text-muted);
  margin: 0;
  line-height: 1.3;
}

.nav-indicator {
  color: var(--odoo-white);
  font-size: var(--odoo-font-size-sm);
}

/* Quick Actions */
.quick-actions {
  border-top: 1px solid var(--odoo-border-light);
  padding: var(--odoo-spacing-md);
}

.quick-actions h3 {
  font-size: var(--odoo-font-size-sm);
  font-weight: var(--odoo-font-weight-semibold);
  color: var(--odoo-text-primary);
  margin-bottom: var(--odoo-spacing-md);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.action-list {
  display: flex;
  flex-direction: column;
  gap: var(--odoo-spacing-xs);
}

.action-item {
  display: flex;
  align-items: center;
  gap: var(--odoo-spacing-sm);
  padding: var(--odoo-spacing-sm);
  background: none;
  border: none;
  border-radius: var(--odoo-border-radius);
  cursor: pointer;
  transition: all var(--odoo-transition-fast);
  color: var(--odoo-text-secondary);
  font-size: var(--odoo-font-size-sm);
}

.action-item:hover {
  background: var(--odoo-bg-light);
  color: var(--odoo-primary);
}

.action-item i {
  width: 16px;
  text-align: center;
}

/* Settings Content */
.settings-content {
  background: var(--odoo-white);
  border-radius: var(--odoo-border-radius-lg);
  box-shadow: var(--odoo-shadow-sm);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--odoo-spacing-lg);
  border-bottom: 1px solid var(--odoo-border-light);
  background: var(--odoo-bg-light);
}

.tab-info h2 {
  font-size: var(--odoo-font-size-xl);
  font-weight: var(--odoo-font-weight-semibold);
  color: var(--odoo-text-primary);
  margin-bottom: var(--odoo-spacing-xs);
  display: flex;
  align-items: center;
  gap: var(--odoo-spacing-sm);
}

.tab-info h2 i {
  color: var(--odoo-primary);
}

.tab-info p {
  color: var(--odoo-text-secondary);
  margin: 0;
  font-size: var(--odoo-font-size-sm);
}

.unsaved-indicator {
  display: flex;
  align-items: center;
  gap: var(--odoo-spacing-xs);
  padding: var(--odoo-spacing-xs) var(--odoo-spacing-sm);
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: var(--odoo-border-radius);
  color: #856404;
  font-size: var(--odoo-font-size-xs);
  font-weight: var(--odoo-font-weight-medium);
}

.unsaved-indicator i {
  color: #ffc107;
}

.content-body {
  flex: 1;
  padding: var(--odoo-spacing-lg);
  overflow-y: auto;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .settings-container {
    grid-template-columns: 250px 1fr;
  }
  
  .nav-content p {
    display: none;
  }
}

@media (max-width: 768px) {
  .general-settings {
    padding: var(--odoo-spacing-md);
  }
  
  .settings-header {
    flex-direction: column;
    gap: var(--odoo-spacing-md);
    text-align: center;
  }
  
  .settings-container {
    grid-template-columns: 1fr;
    gap: var(--odoo-spacing-md);
  }
  
  .settings-sidebar {
    order: 2;
  }
  
  .settings-content {
    order: 1;
  }
  
  .settings-nav {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--odoo-spacing-xs);
  }
  
  .nav-item {
    flex-direction: column;
    text-align: center;
    padding: var(--odoo-spacing-sm);
  }
  
  .nav-content h4 {
    font-size: var(--odoo-font-size-xs);
  }
  
  .nav-indicator {
    display: none;
  }
  
  .quick-actions {
    display: none;
  }
}

/* Animation */
.content-body {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
