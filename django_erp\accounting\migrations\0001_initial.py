# Generated by Django 4.2.21 on 2025-07-21 16:30

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('core', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('crm', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AccountAccount',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(db_index=True, max_length=255)),
                ('code', models.CharField(db_index=True, max_length=64)),
                ('account_type', models.CharField(choices=[('asset_receivable', 'Receivable'), ('asset_cash', 'Bank and Cash'), ('asset_current', 'Current Assets'), ('asset_non_current', 'Non-current Assets'), ('asset_prepayments', 'Prepayments'), ('asset_fixed', 'Fixed Assets'), ('liability_payable', 'Payable'), ('liability_credit_card', 'Credit Card'), ('liability_current', 'Current Liabilities'), ('liability_non_current', 'Non-current Liabilities'), ('equity', 'Equity'), ('equity_unaffected', 'Current Year Earnings'), ('income', 'Income'), ('income_other', 'Other Income'), ('expense', 'Expenses'), ('expense_depreciation', 'Depreciation'), ('expense_direct_cost', 'Cost of Revenue'), ('off_balance', 'Off-Balance Sheet')], max_length=50)),
                ('reconcile', models.BooleanField(default=False, help_text='Allow reconciliation of journal items')),
                ('deprecated', models.BooleanField(default=False)),
                ('note', models.TextField(blank=True, help_text='Internal notes')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('currency', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.currency')),
            ],
        ),
        migrations.CreateModel(
            name='AccountFiscalPosition',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(help_text='Fiscal Position Name', max_length=255)),
                ('active', models.BooleanField(default=True)),
                ('sequence', models.IntegerField(default=1)),
                ('vat_required', models.BooleanField(default=False, help_text='Apply only if partner has VAT')),
                ('auto_apply', models.BooleanField(default=False, help_text='Detect automatically')),
                ('note', models.TextField(blank=True, help_text='Notes')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('country', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.country')),
                ('country_group', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.countrygroup')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('states', models.ManyToManyField(blank=True, to='core.countrystate')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='AccountFullReconcile',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=255)),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='AccountJournal',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=255)),
                ('code', models.CharField(max_length=10)),
                ('type', models.CharField(choices=[('sale', 'Sales'), ('purchase', 'Purchase'), ('cash', 'Cash'), ('bank', 'Bank'), ('general', 'Miscellaneous')], max_length=20)),
                ('sequence', models.IntegerField(default=10, help_text='Used to order journals')),
                ('restrict_mode_hash_table', models.BooleanField(default=True, help_text='Lock posted entries with hash')),
                ('account_control', models.ManyToManyField(blank=True, help_text='Allowed accounts for this journal', to='accounting.accountaccount')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('currency', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.currency')),
                ('default_account', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='journal_default_account', to='accounting.accountaccount')),
                ('suspense_account', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='journal_suspense_account', to='accounting.accountaccount')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='AccountMove',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(blank=True, help_text='Journal Entry Number', max_length=255)),
                ('ref', models.CharField(blank=True, help_text='Reference', max_length=255)),
                ('date', models.DateField(default=django.utils.timezone.now)),
                ('move_type', models.CharField(choices=[('entry', 'Journal Entry'), ('out_invoice', 'Customer Invoice'), ('out_refund', 'Customer Credit Note'), ('in_invoice', 'Vendor Bill'), ('in_refund', 'Vendor Credit Note'), ('out_receipt', 'Sales Receipt'), ('in_receipt', 'Purchase Receipt')], default='entry', max_length=20)),
                ('state', models.CharField(choices=[('draft', 'Unposted'), ('posted', 'Posted'), ('cancel', 'Cancelled')], default='draft', max_length=20)),
                ('invoice_date', models.DateField(blank=True, null=True)),
                ('invoice_date_due', models.DateField(blank=True, null=True)),
                ('payment_reference', models.CharField(blank=True, max_length=255)),
                ('payment_state', models.CharField(choices=[('not_paid', 'Not Paid'), ('in_payment', 'In Payment'), ('paid', 'Paid'), ('partial', 'Partially Paid'), ('reversed', 'Reversed'), ('invoicing_legacy', 'Invoicing App Legacy')], default='not_paid', max_length=20)),
                ('amount_untaxed', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('amount_tax', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('amount_total', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('amount_residual', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('posted_before', models.BooleanField(default=False)),
                ('to_check', models.BooleanField(default=False)),
                ('inalterable_hash', models.CharField(blank=True, max_length=255)),
                ('secure_sequence_number', models.IntegerField(blank=True, null=True)),
                ('invoice_origin', models.CharField(blank=True, help_text='Source Document', max_length=255)),
                ('invoice_source_email', models.EmailField(blank=True, help_text='Source Email', max_length=254)),
                ('invoice_partner_display_name', models.CharField(blank=True, help_text='Partner Display Name', max_length=255)),
                ('approval_status', models.CharField(choices=[('draft', 'Draft'), ('pending', 'Pending Approval'), ('approved', 'Approved'), ('rejected', 'Rejected')], default='draft', help_text='Approval Status', max_length=20)),
                ('approved_date', models.DateTimeField(blank=True, help_text='Approval Date', null=True)),
                ('invoice_sent', models.BooleanField(default=False, help_text='Invoice Sent')),
                ('invoice_sent_date', models.DateTimeField(blank=True, help_text='Invoice Sent Date', null=True)),
                ('is_recurring', models.BooleanField(default=False, help_text='Is Recurring Invoice')),
                ('auto_post', models.BooleanField(default=False, help_text='Auto Post')),
                ('auto_post_until', models.DateField(blank=True, help_text='Auto Post Until', null=True)),
                ('approved_by', models.ForeignKey(blank=True, help_text='Approved By', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_invoices', to=settings.AUTH_USER_MODEL)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.currency')),
                ('fiscal_position', models.ForeignKey(blank=True, help_text='Fiscal Position', null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountfiscalposition')),
            ],
        ),
        migrations.CreateModel(
            name='AccountMoveLine',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('debit', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('credit', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('balance', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('amount_currency', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('name', models.CharField(help_text='Label', max_length=255)),
                ('ref', models.CharField(blank=True, help_text='Reference', max_length=255)),
                ('date', models.DateField()),
                ('date_maturity', models.DateField(blank=True, help_text='Due date', null=True)),
                ('tax_base_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('reconciled', models.BooleanField(default=False)),
                ('matching_number', models.CharField(blank=True, max_length=255)),
                ('quantity', models.DecimalField(decimal_places=4, default=1.0, max_digits=16)),
                ('price_unit', models.DecimalField(decimal_places=4, default=0.0, max_digits=20)),
                ('discount', models.DecimalField(decimal_places=2, default=0.0, max_digits=16)),
                ('sequence', models.IntegerField(default=10)),
                ('display_type', models.CharField(blank=True, choices=[('line_section', 'Section'), ('line_note', 'Note')], max_length=20, null=True)),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='accounting.accountaccount')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('currency', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.currency')),
                ('full_reconcile', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountfullreconcile')),
                ('move', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lines', to='accounting.accountmove')),
                ('partner', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.partner')),
            ],
        ),
        migrations.CreateModel(
            name='AccountPayment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(default='/', max_length=255)),
                ('payment_type', models.CharField(choices=[('outbound', 'Send Money'), ('inbound', 'Receive Money'), ('transfer', 'Internal Transfer')], max_length=20)),
                ('partner_type', models.CharField(blank=True, choices=[('customer', 'Customer'), ('supplier', 'Vendor')], max_length=20, null=True)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=16)),
                ('date', models.DateField(default=django.utils.timezone.now)),
                ('ref', models.CharField(blank=True, help_text='Payment reference', max_length=255)),
                ('state', models.CharField(choices=[('draft', 'Draft'), ('posted', 'Posted'), ('sent', 'Sent'), ('reconciled', 'Reconciled'), ('cancelled', 'Cancelled')], default='draft', max_length=20)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.currency')),
                ('destination_account', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='payment_destination_account', to='accounting.accountaccount')),
                ('journal', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='accounting.accountjournal')),
                ('move', models.ForeignKey(blank=True, help_text='Generated journal entry', null=True, on_delete=django.db.models.deletion.PROTECT, to='accounting.accountmove')),
                ('partner', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.partner')),
                ('reconciled_invoices', models.ManyToManyField(blank=True, help_text='Reconciled invoices', related_name='payment_reconciled_invoices', to='accounting.accountmove')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name_plural': 'Payments',
            },
        ),
        migrations.CreateModel(
            name='PaymentProvider',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(help_text='Provider Name', max_length=255)),
                ('code', models.CharField(choices=[('paypal', 'PayPal'), ('stripe', 'Stripe'), ('razorpay', 'Razorpay'), ('manual', 'Manual')], help_text='Provider Code', max_length=50)),
                ('state', models.CharField(choices=[('disabled', 'Disabled'), ('enabled', 'Enabled'), ('test', 'Test Mode')], default='disabled', max_length=20)),
                ('is_published', models.BooleanField(default=False, help_text='Published on Website')),
                ('maximum_amount', models.DecimalField(decimal_places=2, default=0.0, help_text='Maximum Payment Amount', max_digits=20)),
                ('api_key', models.CharField(blank=True, help_text='API Key', max_length=255)),
                ('api_secret', models.CharField(blank=True, help_text='API Secret', max_length=255)),
                ('webhook_secret', models.CharField(blank=True, help_text='Webhook Secret', max_length=255)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='PaymentTransaction',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('reference', models.CharField(help_text='Transaction Reference', max_length=255, unique=True)),
                ('provider_reference', models.CharField(blank=True, help_text='Provider Reference', max_length=255)),
                ('amount', models.DecimalField(decimal_places=2, help_text='Transaction Amount', max_digits=20)),
                ('state', models.CharField(choices=[('draft', 'Draft'), ('pending', 'Pending'), ('authorized', 'Authorized'), ('done', 'Done'), ('cancel', 'Cancelled'), ('error', 'Error')], default='draft', max_length=20)),
                ('operation', models.CharField(blank=True, choices=[('online_redirect', 'Online payment with redirection'), ('online_direct', 'Online direct payment'), ('online_token', 'Online payment by token'), ('validation', 'Validation of the payment method'), ('offline', 'Offline payment by token'), ('refund', 'Refund')], max_length=50)),
                ('partner_name', models.CharField(blank=True, max_length=255)),
                ('partner_email', models.EmailField(blank=True, max_length=254)),
                ('partner_phone', models.CharField(blank=True, max_length=50)),
                ('provider_data', models.TextField(blank=True, help_text='Provider-specific data (JSON)')),
                ('last_state_change', models.DateTimeField(auto_now=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.currency')),
                ('invoices', models.ManyToManyField(blank=True, help_text='Related Invoices', to='accounting.accountmove')),
                ('partner', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.partner')),
                ('payment', models.ForeignKey(blank=True, help_text='Generated Payment', null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountpayment')),
                ('provider', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='accounting.paymentprovider')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='AccountTaxRepartitionLine',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('factor_percent', models.DecimalField(decimal_places=4, default=100.0, max_digits=16)),
                ('repartition_type', models.CharField(choices=[('base', 'Base'), ('tax', 'Tax')], default='tax', max_length=10)),
                ('sequence', models.IntegerField(default=1)),
                ('account', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='accounting.accountaccount')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='AccountTax',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=255)),
                ('type_tax_use', models.CharField(choices=[('sale', 'Sales'), ('purchase', 'Purchase'), ('none', 'None')], default='sale', max_length=20)),
                ('tax_scope', models.CharField(choices=[('service', 'Services'), ('consu', 'Goods')], default='consu', max_length=20)),
                ('amount_type', models.CharField(choices=[('group', 'Group of Taxes'), ('fixed', 'Fixed'), ('percent', 'Percentage of Price'), ('division', 'Percentage of Price Tax Included')], default='percent', max_length=20)),
                ('amount', models.DecimalField(decimal_places=4, default=0.0, max_digits=16)),
                ('sequence', models.IntegerField(default=1)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('invoice_repartition_lines', models.ManyToManyField(blank=True, related_name='invoice_taxs', to='accounting.accounttaxrepartitionline')),
                ('refund_repartition_lines', models.ManyToManyField(blank=True, related_name='refund_taxs', to='accounting.accounttaxrepartitionline')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('name', 'type_tax_use', 'tax_scope', 'company')},
            },
        ),
        migrations.CreateModel(
            name='AccountPaymentTerm',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(help_text='Payment Terms Name', max_length=255)),
                ('active', models.BooleanField(default=True)),
                ('sequence', models.IntegerField(default=10)),
                ('note', models.TextField(blank=True, help_text='Description on invoices')),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='AccountPartialReconcile',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=20)),
                ('amount_currency', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('credit_move', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='matched_credits', to='accounting.accountmoveline')),
                ('currency', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.currency')),
                ('debit_move', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='matched_debits', to='accounting.accountmoveline')),
                ('full_reconcile', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='accounting.accountfullreconcile')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='accountmoveline',
            name='tax_line',
            field=models.ForeignKey(blank=True, help_text='Tax this line is part of', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='tax_move_lines', to='accounting.accounttax'),
        ),
        migrations.AddField(
            model_name='accountmoveline',
            name='taxes',
            field=models.ManyToManyField(blank=True, related_name='move_line_taxs', to='accounting.accounttax'),
        ),
        migrations.AddField(
            model_name='accountmoveline',
            name='write_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='accountmove',
            name='invoice_payment_term',
            field=models.ForeignKey(blank=True, help_text='Payment Terms', null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountpaymentterm'),
        ),
        migrations.AddField(
            model_name='accountmove',
            name='journal',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='accounting.accountjournal'),
        ),
        migrations.AddField(
            model_name='accountmove',
            name='partner',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.partner'),
        ),
        migrations.AddField(
            model_name='accountmove',
            name='partner_shipping',
            field=models.ForeignKey(blank=True, help_text='Delivery Address', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='shipping_invoices', to='core.partner'),
        ),
        migrations.AddField(
            model_name='accountmove',
            name='payment_transactions',
            field=models.ManyToManyField(blank=True, help_text='Payment Transactions', to='accounting.paymenttransaction'),
        ),
        migrations.AddField(
            model_name='accountmove',
            name='recurring_source',
            field=models.ForeignKey(blank=True, help_text='Source Recurring Invoice', null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountmove'),
        ),
        migrations.AddField(
            model_name='accountmove',
            name='team',
            field=models.ForeignKey(blank=True, help_text='Sales Team', null=True, on_delete=django.db.models.deletion.SET_NULL, to='crm.crmteam'),
        ),
        migrations.AddField(
            model_name='accountmove',
            name='user',
            field=models.ForeignKey(blank=True, help_text='Salesperson', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='accountmove',
            name='write_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='AccountIncoterms',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(help_text='Incoterms Name', max_length=255)),
                ('code', models.CharField(help_text='Incoterms Code', max_length=10, unique=True)),
                ('active', models.BooleanField(default=True)),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='AccountGroup',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=255)),
                ('code_prefix_start', models.CharField(blank=True, max_length=20)),
                ('code_prefix_end', models.CharField(blank=True, max_length=20)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='accounting.accountgroup')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddField(
            model_name='accountfullreconcile',
            name='reconciled_lines',
            field=models.ManyToManyField(related_name='full_reconciles', to='accounting.accountmoveline'),
        ),
        migrations.AddField(
            model_name='accountfullreconcile',
            name='write_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='accountaccount',
            name='group',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountgroup'),
        ),
        migrations.AddField(
            model_name='accountaccount',
            name='taxes',
            field=models.ManyToManyField(blank=True, help_text='Default taxes for this account', to='accounting.accounttax'),
        ),
        migrations.AddField(
            model_name='accountaccount',
            name='write_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='accountpaymentterm',
            index=models.Index(fields=['company'], name='accounting__company_e5dbaf_idx'),
        ),
        migrations.AddIndex(
            model_name='accountmoveline',
            index=models.Index(fields=['move'], name='accounting__move_id_1a18b5_idx'),
        ),
        migrations.AddIndex(
            model_name='accountmoveline',
            index=models.Index(fields=['account'], name='accounting__account_84f83b_idx'),
        ),
        migrations.AddIndex(
            model_name='accountmoveline',
            index=models.Index(fields=['partner'], name='accounting__partner_8990cb_idx'),
        ),
        migrations.AddIndex(
            model_name='accountmoveline',
            index=models.Index(fields=['date'], name='accounting__date_927817_idx'),
        ),
        migrations.AddIndex(
            model_name='accountmoveline',
            index=models.Index(fields=['reconciled'], name='accounting__reconci_bf53cc_idx'),
        ),
        migrations.AddConstraint(
            model_name='accountmoveline',
            constraint=models.CheckConstraint(check=models.Q(('debit__gte', 0)), name='check_debit_positive'),
        ),
        migrations.AddConstraint(
            model_name='accountmoveline',
            constraint=models.CheckConstraint(check=models.Q(('credit__gte', 0)), name='check_credit_positive'),
        ),
        migrations.AddIndex(
            model_name='accountmove',
            index=models.Index(fields=['date', 'name'], name='accounting__date_1f0f45_idx'),
        ),
        migrations.AddIndex(
            model_name='accountmove',
            index=models.Index(fields=['state'], name='accounting__state_dd2545_idx'),
        ),
        migrations.AddIndex(
            model_name='accountmove',
            index=models.Index(fields=['move_type'], name='accounting__move_ty_44114b_idx'),
        ),
        migrations.AddIndex(
            model_name='accountmove',
            index=models.Index(fields=['partner'], name='accounting__partner_9c27ae_idx'),
        ),
        migrations.AddIndex(
            model_name='accountmove',
            index=models.Index(fields=['journal'], name='accounting__journal_05733d_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='accountjournal',
            unique_together={('code', 'company')},
        ),
        migrations.AddIndex(
            model_name='accountincoterms',
            index=models.Index(fields=['code'], name='accounting__code_ac75b8_idx'),
        ),
        migrations.AddIndex(
            model_name='accountfiscalposition',
            index=models.Index(fields=['company'], name='accounting__company_bde0b5_idx'),
        ),
        migrations.AddIndex(
            model_name='accountfiscalposition',
            index=models.Index(fields=['country'], name='accounting__country_874c2b_idx'),
        ),
        migrations.AddIndex(
            model_name='accountaccount',
            index=models.Index(fields=['code', 'company'], name='accounting__code_f9bbdc_idx'),
        ),
        migrations.AddIndex(
            model_name='accountaccount',
            index=models.Index(fields=['account_type'], name='accounting__account_12f259_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='accountaccount',
            unique_together={('code', 'company')},
        ),
    ]
