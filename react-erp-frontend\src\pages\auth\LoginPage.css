/* Odoo-Style Login Page */
.login-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #714B67 0%, #8B5A7F 100%);
  padding: var(--odoo-spacing-md);
  position: relative;
  overflow: hidden;
}

.login-container {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 400px;
}

.login-card {
  background: var(--odoo-white);
  border-radius: var(--odoo-border-radius-xl);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  padding: var(--odoo-spacing-xl);
  position: relative;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Header */
.login-header {
  text-align: center;
  margin-bottom: var(--odoo-spacing-xl);
}

.login-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--odoo-spacing-sm);
  margin-bottom: var(--odoo-spacing-lg);
  font-size: var(--odoo-font-size-xl);
  font-weight: var(--odoo-font-weight-bold);
  color: var(--odoo-primary);
}

.login-logo i {
  font-size: 2rem;
  background: linear-gradient(135deg, var(--odoo-primary), var(--odoo-primary-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.login-title {
  font-size: var(--odoo-font-size-2xl);
  font-weight: var(--odoo-font-weight-bold);
  color: var(--odoo-text-primary);
  margin-bottom: var(--odoo-spacing-sm);
  line-height: 1.2;
}

.login-subtitle {
  font-size: var(--odoo-font-size-base);
  color: var(--odoo-text-secondary);
  margin-bottom: 0;
  line-height: 1.4;
}

/* Form */
.login-form {
  margin-bottom: var(--odoo-spacing-lg);
}

.login-error-banner {
  display: flex;
  align-items: center;
  gap: var(--odoo-spacing-sm);
  padding: var(--odoo-spacing-sm) var(--odoo-spacing-md);
  background-color: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.2);
  border-radius: var(--odoo-border-radius);
  color: var(--odoo-danger);
  font-size: var(--odoo-font-size-sm);
  margin-bottom: var(--odoo-spacing-md);
}

.login-options {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--odoo-spacing-lg);
  flex-wrap: wrap;
  gap: var(--odoo-spacing-sm);
}

/* Custom Checkbox */
.login-checkbox {
  display: flex;
  align-items: center;
  gap: var(--odoo-spacing-sm);
  cursor: pointer;
  font-size: var(--odoo-font-size-sm);
  color: var(--odoo-text-secondary);
  user-select: none;
}

.login-checkbox input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid var(--odoo-border-medium);
  border-radius: var(--odoo-border-radius-sm);
  position: relative;
  transition: all var(--odoo-transition-fast);
  background: var(--odoo-white);
}

.login-checkbox input[type="checkbox"]:checked + .checkmark {
  background: var(--odoo-primary);
  border-color: var(--odoo-primary);
}

.login-checkbox input[type="checkbox"]:checked + .checkmark::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 2px;
  width: 4px;
  height: 8px;
  border: solid var(--odoo-white);
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.checkbox-text {
  font-size: var(--odoo-font-size-sm);
  color: var(--odoo-text-secondary);
}

.login-forgot-link {
  font-size: var(--odoo-font-size-sm);
  color: var(--odoo-primary);
  text-decoration: none;
  font-weight: var(--odoo-font-weight-medium);
  transition: color var(--odoo-transition-fast);
}

.login-forgot-link:hover {
  color: var(--odoo-primary-hover);
  text-decoration: underline;
}

/* Footer */
.login-footer {
  text-align: center;
  padding-top: var(--odoo-spacing-lg);
  border-top: 1px solid var(--odoo-border-light);
}

.login-footer p {
  font-size: var(--odoo-font-size-sm);
  color: var(--odoo-text-secondary);
  margin: 0;
}

.login-signup-link {
  color: var(--odoo-primary);
  text-decoration: none;
  font-weight: var(--odoo-font-weight-medium);
  transition: color var(--odoo-transition-fast);
}

.login-signup-link:hover {
  color: var(--odoo-primary-hover);
  text-decoration: underline;
}

/* Background Pattern */
.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  overflow: hidden;
}

.login-pattern {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  33% { transform: translate(30px, -30px) rotate(120deg); }
  66% { transform: translate(-20px, 20px) rotate(240deg); }
}

/* Responsive Design */
@media (max-width: 767px) {
  .login-page {
    padding: var(--odoo-spacing-sm);
  }
  
  .login-card {
    padding: var(--odoo-spacing-lg);
  }
  
  .login-title {
    font-size: var(--odoo-font-size-xl);
  }
  
  .login-options {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--odoo-spacing-md);
  }
  
  .login-forgot-link {
    align-self: flex-end;
  }
}

@media (max-width: 480px) {
  .login-container {
    max-width: 100%;
  }
  
  .login-card {
    padding: var(--odoo-spacing-md);
  }
  
  .login-header {
    margin-bottom: var(--odoo-spacing-lg);
  }
}
