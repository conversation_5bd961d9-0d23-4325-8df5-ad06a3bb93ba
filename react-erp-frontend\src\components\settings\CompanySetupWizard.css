/* Company Setup Wizard - Odoo Style */
.wizard-modal {
  background: var(--odoo-white);
  border-radius: var(--odoo-border-radius-lg);
  box-shadow: var(--odoo-shadow-lg);
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.wizard-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--odoo-spacing-lg);
  border-bottom: 1px solid var(--odoo-border-light);
  background: linear-gradient(135deg, var(--odoo-primary), var(--odoo-primary-light));
  color: var(--odoo-white);
}

.wizard-header h2 {
  font-size: var(--odoo-font-size-title);
  font-weight: var(--odoo-font-weight-semibold);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--odoo-spacing-sm);
}

.wizard-header .modal-close {
  background: rgba(255, 255, 255, 0.2);
  color: var(--odoo-white);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.wizard-header .modal-close:hover {
  background: rgba(255, 255, 255, 0.3);
  color: var(--odoo-white);
}

/* Step Progress */
.step-progress {
  display: flex;
  padding: var(--odoo-spacing-lg);
  background: var(--odoo-bg-light);
  border-bottom: 1px solid var(--odoo-border-light);
  gap: var(--odoo-spacing-md);
}

.step-item {
  flex: 1;
  display: flex;
  align-items: center;
  gap: var(--odoo-spacing-sm);
  padding: var(--odoo-spacing-sm);
  border-radius: var(--odoo-border-radius);
  transition: all var(--odoo-transition-fast);
  position: relative;
}

.step-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: calc(-1 * var(--odoo-spacing-md) / 2);
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid var(--odoo-border-medium);
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  z-index: 1;
}

.step-item.active {
  background: var(--odoo-white);
  box-shadow: var(--odoo-shadow-sm);
}

.step-item.active::after {
  border-left-color: var(--odoo-primary);
}

.step-item.current {
  background: var(--odoo-primary);
  color: var(--odoo-white);
}

.step-item.current::after {
  border-left-color: var(--odoo-primary);
}

.step-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--odoo-border-medium);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--odoo-font-size-lg);
  color: var(--odoo-text-muted);
  transition: all var(--odoo-transition-fast);
  flex-shrink: 0;
}

.step-item.active .step-icon {
  background: var(--odoo-primary);
  color: var(--odoo-white);
}

.step-item.current .step-icon {
  background: var(--odoo-white);
  color: var(--odoo-primary);
}

.step-info {
  flex: 1;
  min-width: 0;
}

.step-number {
  font-size: var(--odoo-font-size-xs);
  font-weight: var(--odoo-font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  opacity: 0.8;
}

.step-title {
  font-size: var(--odoo-font-size-sm);
  font-weight: var(--odoo-font-weight-semibold);
  margin-top: 2px;
}

/* Wizard Content */
.wizard-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--odoo-spacing-xl);
}

.step-content h3 {
  font-size: var(--odoo-font-size-title);
  font-weight: var(--odoo-font-weight-semibold);
  color: var(--odoo-text-primary);
  margin-bottom: var(--odoo-spacing-xs);
}

.step-content p {
  color: var(--odoo-text-secondary);
  margin-bottom: var(--odoo-spacing-xl);
  font-size: var(--odoo-font-size-base);
}

/* Form Styles */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--odoo-spacing-lg);
  margin-bottom: var(--odoo-spacing-lg);
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-size: var(--odoo-font-size-sm);
  font-weight: var(--odoo-font-weight-medium);
  color: var(--odoo-text-primary);
  margin-bottom: var(--odoo-spacing-xs);
}

.form-group label.required::after {
  content: '*';
  color: var(--odoo-danger);
  margin-left: 4px;
}

.form-group input,
.form-group select {
  padding: var(--odoo-spacing-sm);
  border: 1px solid var(--odoo-border-medium);
  border-radius: var(--odoo-border-radius);
  font-size: var(--odoo-font-size-base);
  transition: border-color var(--odoo-transition-fast), box-shadow var(--odoo-transition-fast);
  background: var(--odoo-white);
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--odoo-primary);
  box-shadow: 0 0 0 0.2rem rgba(113, 75, 103, 0.25);
}

.form-group input.error,
.form-group select.error {
  border-color: var(--odoo-danger);
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.error-text {
  color: var(--odoo-danger);
  font-size: var(--odoo-font-size-xs);
  margin-top: var(--odoo-spacing-xs);
}

/* Checkbox */
.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--odoo-spacing-sm);
  cursor: pointer;
  font-size: var(--odoo-font-size-base);
  color: var(--odoo-text-primary);
  margin-top: var(--odoo-spacing-sm);
}

.checkbox-label input[type="checkbox"] {
  width: 18px;
  height: 18px;
  margin: 0;
  cursor: pointer;
}

/* Chart Templates */
.chart-templates {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--odoo-spacing-md);
  margin-bottom: var(--odoo-spacing-lg);
}

.chart-template {
  border: 2px solid var(--odoo-border-medium);
  border-radius: var(--odoo-border-radius-lg);
  padding: var(--odoo-spacing-lg);
  cursor: pointer;
  transition: all var(--odoo-transition-fast);
  background: var(--odoo-white);
}

.chart-template:hover {
  border-color: var(--odoo-primary);
  box-shadow: var(--odoo-shadow-sm);
}

.chart-template.selected {
  border-color: var(--odoo-primary);
  background: rgba(113, 75, 103, 0.05);
  box-shadow: var(--odoo-shadow-sm);
}

/* Info Box */
.info-box {
  display: flex;
  align-items: flex-start;
  gap: var(--odoo-spacing-sm);
  padding: var(--odoo-spacing-md);
  background: var(--odoo-bg-light);
  border: 1px solid var(--odoo-border-light);
  border-radius: var(--odoo-border-radius);
  margin-top: var(--odoo-spacing-sm);
}

.info-box i {
  color: var(--odoo-info);
  font-size: var(--odoo-font-size-lg);
  margin-top: 2px;
  flex-shrink: 0;
}

.info-box strong {
  font-size: var(--odoo-font-size-sm);
  font-weight: var(--odoo-font-weight-semibold);
  color: var(--odoo-text-primary);
  display: block;
  margin-bottom: 2px;
}

.info-box p {
  font-size: var(--odoo-font-size-xs);
  color: var(--odoo-text-secondary);
  margin: 0;
  line-height: 1.4;
}

.template-header {
  display: flex;
  align-items: center;
  gap: var(--odoo-spacing-sm);
  margin-bottom: var(--odoo-spacing-sm);
}

.template-header input[type="radio"] {
  width: 18px;
  height: 18px;
  margin: 0;
}

.template-header h4 {
  font-size: var(--odoo-font-size-name);
  font-weight: var(--odoo-font-weight-semibold);
  color: var(--odoo-text-primary);
  margin: 0;
}

.coa-template p {
  color: var(--odoo-text-secondary);
  font-size: var(--odoo-font-size-sm);
  margin: 0;
}

/* Wizard Actions */
.wizard-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--odoo-spacing-lg);
  border-top: 1px solid var(--odoo-border-light);
  background: var(--odoo-bg-light);
}

.left-actions,
.right-actions {
  display: flex;
  gap: var(--odoo-spacing-sm);
}

/* Responsive Design */
@media (max-width: 768px) {
  .wizard-modal {
    max-width: 95vw;
    max-height: 95vh;
  }
  
  .step-progress {
    flex-direction: column;
    gap: var(--odoo-spacing-sm);
  }
  
  .step-item::after {
    display: none;
  }
  
  .wizard-content {
    padding: var(--odoo-spacing-lg);
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: var(--odoo-spacing-md);
  }
  
  .coa-templates {
    grid-template-columns: 1fr;
  }
  
  .wizard-actions {
    flex-direction: column;
    gap: var(--odoo-spacing-sm);
  }
  
  .left-actions,
  .right-actions {
    width: 100%;
    justify-content: center;
  }
}
