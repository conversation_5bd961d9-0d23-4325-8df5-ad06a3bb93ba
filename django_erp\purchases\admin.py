from django.contrib import admin
from .models import PurchaseOrder, PurchaseOrderLine


@admin.register(PurchaseOrder)
class PurchaseOrderAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'partner_id', 'date_order', 'state', 'amount_total',
        'date_planned', 'user_id'
    ]
    list_filter = [
        'state', 'company_id', 'date_order', 'date_planned', 'user_id'
    ]
    search_fields = ['name', 'partner_ref', 'partner_id__name']
    date_hierarchy = 'date_order'
    readonly_fields = ['amount_untaxed', 'amount_tax', 'amount_total']

    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'origin', 'partner_ref', 'date_order']
        }),
        ('Vendor', {
            'fields': ['partner_id']
        }),
        ('Purchase Information', {
            'fields': ['user_id', 'company_id']
        }),
        ('Pricing', {
            'fields': ['currency_id']
        }),
        ('Amounts', {
            'fields': ['amount_untaxed', 'amount_tax', 'amount_total']
        }),
        ('Status', {
            'fields': ['state']
        }),
        ('Delivery', {
            'fields': ['date_planned', 'picking_type_id']
        }),
        ('Terms', {
            'fields': ['payment_term_id', 'fiscal_position_id', 'incoterm_id']
        }),
        ('Additional', {
            'fields': ['notes']
        }),
    ]

    actions = ['action_confirm', 'action_cancel']

    def action_confirm(self, request, queryset):
        for order in queryset.filter(state='draft'):
            order.button_confirm()
        self.message_user(request, f"Confirmed {queryset.count()} purchase orders.")
    action_confirm.short_description = "Confirm selected purchase orders"

    def action_cancel(self, request, queryset):
        for order in queryset.filter(state__in=['draft', 'sent']):
            order.button_cancel()
        self.message_user(request, f"Cancelled {queryset.count()} purchase orders.")
    action_cancel.short_description = "Cancel selected purchase orders"


@admin.register(PurchaseOrderLine)
class PurchaseOrderLineAdmin(admin.ModelAdmin):
    list_display = [
        'order_id', 'product_id', 'name', 'product_qty',
        'price_unit', 'price_subtotal', 'date_planned'
    ]
    list_filter = ['order_id__state', 'company_id', 'product_uom', 'date_planned']
    search_fields = ['name', 'product_id__name', 'order_id__name']
    readonly_fields = ['price_subtotal', 'price_tax', 'price_total']
    date_hierarchy = 'date_planned'

    fieldsets = [
        ('Order Information', {
            'fields': ['order_id', 'sequence']
        }),
        ('Product', {
            'fields': ['product_id', 'name']
        }),
        ('Quantity & UoM', {
            'fields': ['product_qty', 'product_uom', 'qty_received', 'qty_invoiced']
        }),
        ('Pricing', {
            'fields': ['price_unit']
        }),
        ('Amounts', {
            'fields': ['price_subtotal', 'price_tax', 'price_total']
        }),
        ('Taxes', {
            'fields': ['taxes_id']
        }),
        ('Delivery', {
            'fields': ['date_planned']
        }),
        ('Additional', {
            'fields': ['company_id', 'currency_id']
        }),
    ]
