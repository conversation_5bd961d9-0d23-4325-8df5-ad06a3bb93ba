import React, { useState, useEffect } from 'react';
import { settingsAPI } from '../../services/api';
import Button from '../common/Button/Button';
import './CurrencySettings.css';

const CurrencySettings = ({ onDataChange }) => {
  const [currencies, setCurrencies] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingCurrency, setEditingCurrency] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    loadCurrencies();
  }, []);

  const loadCurrencies = async () => {
    try {
      setLoading(true);
      console.log('Loading currencies from API...');

      const response = await settingsAPI.getCurrencies();
      console.log('Currencies API response:', response);

      const currenciesData = response.results || response;
      setCurrencies(currenciesData);
    } catch (error) {
      console.error('Error loading currencies:', error);
      // Fallback to mock data if API fails
      setCurrencies([
        {
          id: 1,
          name: 'US Dollar',
          code: 'USD',
          symbol: '$',
          position: 'before',
          decimal_places: 2,
          rounding: 0.01,
          active: true,
          rate: 1.0,
          is_base: true
        },
        {
          id: 2,
          name: 'Euro',
          code: 'EUR',
          symbol: '€',
          position: 'after',
          decimal_places: 2,
          rounding: 0.01,
          active: true,
          rate: 0.85,
          is_base: false
        },
        {
          id: 3,
          name: 'British Pound',
          code: 'GBP',
          symbol: '£',
          position: 'before',
          decimal_places: 2,
          rounding: 0.01,
          active: true,
          rate: 0.73,
          is_base: false
        },
        {
          id: 4,
          name: 'Canadian Dollar',
          code: 'CAD',
          symbol: 'C$',
          position: 'before',
          decimal_places: 2,
          rounding: 0.01,
          active: true,
          rate: 1.35,
          is_base: false
        },
        {
          id: 5,
          name: 'Japanese Yen',
          code: 'JPY',
          symbol: '¥',
          position: 'before',
          decimal_places: 0,
          rounding: 1,
          active: false,
          rate: 110.0,
          is_base: false
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleActive = (currencyId) => {
    setCurrencies(prev => prev.map(currency => 
      currency.id === currencyId 
        ? { ...currency, active: !currency.active }
        : currency
    ));
    onDataChange();
  };

  const handleSetBase = async (currencyId) => {
    try {
      console.log('Setting base currency:', currencyId);
      await settingsAPI.setCurrencyAsBase(currencyId);

      // Update local state
      setCurrencies(prev => prev.map(currency => ({
        ...currency,
        is_base: currency.id === currencyId,
        rate: currency.id === currencyId ? 1.0 : currency.rate
      })));
      onDataChange();
    } catch (error) {
      console.error('Error setting base currency:', error);
      alert('Failed to set base currency. Please try again.');
    }
  };

  const handleUpdateRate = async (currencyId, newRate) => {
    try {
      const rate = parseFloat(newRate) || 0;
      console.log('Updating currency rate:', currencyId, rate);

      await settingsAPI.updateCurrencyRate(currencyId, rate);

      // Update local state
      setCurrencies(prev => prev.map(currency =>
        currency.id === currencyId
          ? { ...currency, rate: rate }
          : currency
      ));
      onDataChange();
    } catch (error) {
      console.error('Error updating currency rate:', error);
      // Revert the change in UI
      loadCurrencies();
    }
  };

  const formatCurrency = (amount, currency) => {
    const formattedAmount = amount.toFixed(currency.decimal_places);
    return currency.position === 'before' 
      ? `${currency.symbol}${formattedAmount}`
      : `${formattedAmount}${currency.symbol}`;
  };

  const filteredCurrencies = currencies.filter(currency =>
    currency.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    currency.code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="currency-settings loading">
        <div className="loading-spinner">
          <i className="fas fa-spinner fa-spin"></i>
          <p>Loading currencies...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="currency-settings">
      {/* Header Actions */}
      <div className="currency-header">
        <div className="search-section">
          <div className="search-box">
            <i className="fas fa-search"></i>
            <input
              type="text"
              placeholder="Search currencies..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
        
        <div className="header-actions">
          <Button
            variant="secondary"
            icon="fas fa-sync"
            onClick={() => console.log('Update exchange rates')}
          >
            Update Rates
          </Button>
          <Button
            variant="primary"
            icon="fas fa-plus"
            onClick={() => setShowCreateModal(true)}
          >
            Add Currency
          </Button>
        </div>
      </div>

      {/* Currency List */}
      <div className="currency-list">
        <div className="list-header">
          <h3>
            <i className="fas fa-coins"></i>
            Currencies ({filteredCurrencies.length})
          </h3>
          <p>Manage your system currencies and exchange rates</p>
        </div>

        <div className="currency-table">
          <div className="table-header">
            <div className="col-currency">Currency</div>
            <div className="col-rate">Exchange Rate</div>
            <div className="col-format">Format</div>
            <div className="col-status">Status</div>
            <div className="col-actions">Actions</div>
          </div>

          <div className="table-body">
            {filteredCurrencies.map(currency => (
              <div key={currency.id} className={`currency-row ${!currency.active ? 'inactive' : ''}`}>
                <div className="col-currency">
                  <div className="currency-info">
                    <div className="currency-symbol">{currency.symbol}</div>
                    <div className="currency-details">
                      <h4>{currency.name}</h4>
                      <span className="currency-code">{currency.code}</span>
                    </div>
                    {currency.is_base && (
                      <span className="base-badge">
                        <i className="fas fa-star"></i>
                        Base
                      </span>
                    )}
                  </div>
                </div>

                <div className="col-rate">
                  <div className="rate-input">
                    <input
                      type="number"
                      step="0.000001"
                      value={currency.rate}
                      onChange={(e) => handleUpdateRate(currency.id, e.target.value)}
                      disabled={currency.is_base}
                      className={currency.is_base ? 'disabled' : ''}
                    />
                    <span className="rate-label">
                      {currency.is_base ? 'Base Currency' : `per 1 ${currencies.find(c => c.is_base)?.code || 'USD'}`}
                    </span>
                  </div>
                </div>

                <div className="col-format">
                  <div className="format-preview">
                    <span className="format-example">
                      {formatCurrency(1234.56, currency)}
                    </span>
                    <span className="format-details">
                      {currency.decimal_places} decimals, {currency.position}
                    </span>
                  </div>
                </div>

                <div className="col-status">
                  <div className="status-controls">
                    <label className="toggle-switch">
                      <input
                        type="checkbox"
                        checked={currency.active}
                        onChange={() => handleToggleActive(currency.id)}
                      />
                      <span className="toggle-slider"></span>
                    </label>
                    <span className={`status-label ${currency.active ? 'active' : 'inactive'}`}>
                      {currency.active ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>

                <div className="col-actions">
                  <div className="action-buttons">
                    {!currency.is_base && (
                      <button
                        className="btn-action"
                        onClick={() => handleSetBase(currency.id)}
                        title="Set as base currency"
                      >
                        <i className="fas fa-star"></i>
                      </button>
                    )}
                    <button
                      className="btn-action"
                      onClick={() => setEditingCurrency(currency)}
                      title="Edit currency"
                    >
                      <i className="fas fa-edit"></i>
                    </button>
                    <button
                      className="btn-action danger"
                      onClick={() => console.log('Delete currency', currency.id)}
                      title="Delete currency"
                      disabled={currency.is_base}
                    >
                      <i className="fas fa-trash"></i>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {filteredCurrencies.length === 0 && (
          <div className="empty-state">
            <i className="fas fa-coins"></i>
            <h3>No currencies found</h3>
            <p>Try adjusting your search criteria or add a new currency.</p>
          </div>
        )}
      </div>

      {/* Exchange Rate Info */}
      <div className="exchange-info">
        <div className="info-card">
          <h4>
            <i className="fas fa-info-circle"></i>
            Exchange Rate Information
          </h4>
          <ul>
            <li>Exchange rates are relative to the base currency ({currencies.find(c => c.is_base)?.code || 'USD'})</li>
            <li>Rates are updated manually or can be fetched from external services</li>
            <li>Only one currency can be set as the base currency</li>
            <li>Inactive currencies won't appear in transaction forms</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default CurrencySettings;
