# Generated by Django 4.2.21 on 2025-07-21 16:53

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('purchases', '0001_initial'),
        ('accounting', '0001_initial'),
        ('sales', '0001_initial'),
        ('core', '0001_initial'),
        ('mrp', '0001_initial'),
        ('inventory', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='stockrule',
            name='picking_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='purchases.stockpickingtype'),
        ),
        migrations.AddField(
            model_name='stockrule',
            name='write_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='stockremovalstrategy',
            name='create_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='stockremovalstrategy',
            name='write_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='stockquantpackage',
            name='company',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company'),
        ),
        migrations.AddField(
            model_name='stockquantpackage',
            name='create_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='stockquantpackage',
            name='location',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='inventory.stocklocation'),
        ),
        migrations.AddField(
            model_name='stockquantpackage',
            name='package_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='inventory.stockpackagetype'),
        ),
        migrations.AddField(
            model_name='stockquantpackage',
            name='parent',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='child_packages', to='inventory.stockquantpackage'),
        ),
        migrations.AddField(
            model_name='stockquantpackage',
            name='write_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='stockquant',
            name='company',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company'),
        ),
        migrations.AddField(
            model_name='stockquant',
            name='create_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='stockquant',
            name='currency',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.currency'),
        ),
        migrations.AddField(
            model_name='stockquant',
            name='location',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='inventory.stocklocation'),
        ),
        migrations.AddField(
            model_name='stockquant',
            name='lot',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='inventory.stocklot'),
        ),
        migrations.AddField(
            model_name='stockquant',
            name='owner',
            field=models.ForeignKey(blank=True, help_text='Owner', null=True, on_delete=django.db.models.deletion.PROTECT, to='core.partner'),
        ),
        migrations.AddField(
            model_name='stockquant',
            name='package',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='inventory.stockquantpackage'),
        ),
        migrations.AddField(
            model_name='stockquant',
            name='product',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='inventory.product'),
        ),
        migrations.AddField(
            model_name='stockquant',
            name='write_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='stockpicking',
            name='company',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company'),
        ),
        migrations.AddField(
            model_name='stockpicking',
            name='create_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='stockpicking',
            name='location',
            field=models.ForeignKey(help_text='Source Location', on_delete=django.db.models.deletion.PROTECT, to='inventory.stocklocation'),
        ),
        migrations.AddField(
            model_name='stockpicking',
            name='location_dest',
            field=models.ForeignKey(help_text='Destination Location', on_delete=django.db.models.deletion.PROTECT, related_name='picking_dest', to='inventory.stocklocation'),
        ),
        migrations.AddField(
            model_name='stockpicking',
            name='partner',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.partner'),
        ),
        migrations.AddField(
            model_name='stockpicking',
            name='picking_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='purchases.stockpickingtype'),
        ),
        migrations.AddField(
            model_name='stockpicking',
            name='user',
            field=models.ForeignKey(blank=True, help_text='Responsible', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='stockpicking',
            name='write_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='stockpackagetype',
            name='company',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company'),
        ),
        migrations.AddField(
            model_name='stockpackagetype',
            name='create_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='stockpackagetype',
            name='write_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='stockmove',
            name='company',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company'),
        ),
        migrations.AddField(
            model_name='stockmove',
            name='create_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='stockmove',
            name='group',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.procurementgroup'),
        ),
        migrations.AddField(
            model_name='stockmove',
            name='location',
            field=models.ForeignKey(help_text='Source Location', on_delete=django.db.models.deletion.PROTECT, to='inventory.stocklocation'),
        ),
        migrations.AddField(
            model_name='stockmove',
            name='location_dest',
            field=models.ForeignKey(help_text='Destination Location', on_delete=django.db.models.deletion.PROTECT, related_name='move_dest', to='inventory.stocklocation'),
        ),
        migrations.AddField(
            model_name='stockmove',
            name='move_origs',
            field=models.ManyToManyField(blank=True, help_text='Original Moves', related_name='move_dests', to='inventory.stockmove'),
        ),
        migrations.AddField(
            model_name='stockmove',
            name='partner',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.partner'),
        ),
        migrations.AddField(
            model_name='stockmove',
            name='picking',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='moves', to='inventory.stockpicking'),
        ),
        migrations.AddField(
            model_name='stockmove',
            name='product',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='inventory.product'),
        ),
        migrations.AddField(
            model_name='stockmove',
            name='product_uom',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='sales.productuom'),
        ),
        migrations.AddField(
            model_name='stockmove',
            name='production',
            field=models.ForeignKey(blank=True, help_text='Manufacturing Order (Finished Product)', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='move_finisheds', to='mrp.mrpproduction'),
        ),
        migrations.AddField(
            model_name='stockmove',
            name='raw_material_production',
            field=models.ForeignKey(blank=True, help_text='Manufacturing Order (Raw Material)', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='move_raws', to='mrp.mrpproduction'),
        ),
        migrations.AddField(
            model_name='stockmove',
            name='rule',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='inventory.stockrule'),
        ),
        migrations.AddField(
            model_name='stockmove',
            name='unbuild',
            field=models.ForeignKey(blank=True, help_text='Unbuild Order', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='moves', to='mrp.mrpunbuild'),
        ),
        migrations.AddField(
            model_name='stockmove',
            name='workorder',
            field=models.ForeignKey(blank=True, help_text='Work Order', null=True, on_delete=django.db.models.deletion.SET_NULL, to='mrp.mrpworkorder'),
        ),
        migrations.AddField(
            model_name='stockmove',
            name='write_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='stocklot',
            name='company',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company'),
        ),
        migrations.AddField(
            model_name='stocklot',
            name='create_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='stocklot',
            name='product',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='inventory.product'),
        ),
        migrations.AddField(
            model_name='stocklot',
            name='write_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='stocklocation',
            name='company',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.company'),
        ),
        migrations.AddField(
            model_name='stocklocation',
            name='create_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='stocklocation',
            name='location',
            field=models.ForeignKey(blank=True, help_text='Parent Location', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='child_locations', to='inventory.stocklocation'),
        ),
        migrations.AddField(
            model_name='stocklocation',
            name='removal_strategy',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='inventory.stockremovalstrategy'),
        ),
        migrations.AddField(
            model_name='stocklocation',
            name='valuation_in_account',
            field=models.ForeignKey(blank=True, help_text='Used for real-time inventory valuation', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='location_valuation_in', to='accounting.accountaccount'),
        ),
        migrations.AddField(
            model_name='stocklocation',
            name='valuation_out_account',
            field=models.ForeignKey(blank=True, help_text='Used for real-time inventory valuation', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='location_valuation_out', to='accounting.accountaccount'),
        ),
        migrations.AddField(
            model_name='stocklocation',
            name='write_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='stockinventoryline',
            name='company',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company'),
        ),
        migrations.AddField(
            model_name='stockinventoryline',
            name='create_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='stockinventoryline',
            name='inventory',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lines', to='inventory.stockinventory'),
        ),
        migrations.AddField(
            model_name='stockinventoryline',
            name='location',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='inventory.stocklocation'),
        ),
        migrations.AddField(
            model_name='stockinventoryline',
            name='lot',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='inventory.stocklot'),
        ),
        migrations.AddField(
            model_name='stockinventoryline',
            name='package',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='inventory.stockquantpackage'),
        ),
        migrations.AddField(
            model_name='stockinventoryline',
            name='product',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='inventory.product'),
        ),
        migrations.AddField(
            model_name='stockinventoryline',
            name='write_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='stockinventory',
            name='categories',
            field=models.ManyToManyField(blank=True, to='inventory.productcategory'),
        ),
        migrations.AddField(
            model_name='stockinventory',
            name='company',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company'),
        ),
        migrations.AddField(
            model_name='stockinventory',
            name='create_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='stockinventory',
            name='locations',
            field=models.ManyToManyField(blank=True, to='inventory.stocklocation'),
        ),
        migrations.AddField(
            model_name='stockinventory',
            name='products',
            field=models.ManyToManyField(blank=True, to='inventory.product'),
        ),
        migrations.AddField(
            model_name='stockinventory',
            name='write_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='producttemplate',
            name='categ',
            field=models.ForeignKey(help_text='Product Category', on_delete=django.db.models.deletion.PROTECT, to='inventory.productcategory'),
        ),
        migrations.AddField(
            model_name='producttemplate',
            name='company',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.company'),
        ),
        migrations.AddField(
            model_name='producttemplate',
            name='create_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='producttemplate',
            name='responsible',
            field=models.ForeignKey(blank=True, help_text='Responsible Person', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='producttemplate',
            name='uom',
            field=models.ForeignKey(help_text='Unit of Measure', on_delete=django.db.models.deletion.PROTECT, to='sales.productuom'),
        ),
        migrations.AddField(
            model_name='producttemplate',
            name='uom_po',
            field=models.ForeignKey(help_text='Purchase Unit of Measure', on_delete=django.db.models.deletion.PROTECT, related_name='product_template_po', to='sales.productuom'),
        ),
        migrations.AddField(
            model_name='producttemplate',
            name='write_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='productpackaging',
            name='company',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.company'),
        ),
        migrations.AddField(
            model_name='productpackaging',
            name='create_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='productpackaging',
            name='product',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.product'),
        ),
        migrations.AddField(
            model_name='productpackaging',
            name='product_tmpl',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.producttemplate'),
        ),
        migrations.AddField(
            model_name='productpackaging',
            name='write_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='productcategory',
            name='create_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='productcategory',
            name='parent',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.productcategory'),
        ),
        migrations.AddField(
            model_name='productcategory',
            name='property_stock_account_input_categ',
            field=models.ForeignKey(blank=True, help_text='Stock Input Account', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='category_stock_input', to='accounting.accountaccount'),
        ),
        migrations.AddField(
            model_name='productcategory',
            name='property_stock_account_output_categ',
            field=models.ForeignKey(blank=True, help_text='Stock Output Account', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='category_stock_output', to='accounting.accountaccount'),
        ),
        migrations.AddField(
            model_name='productcategory',
            name='property_stock_valuation_account',
            field=models.ForeignKey(blank=True, help_text='Stock Valuation Account', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='category_stock_valuation', to='accounting.accountaccount'),
        ),
        migrations.AddField(
            model_name='productcategory',
            name='write_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='product',
            name='create_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='product',
            name='product_tmpl',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_variants', to='inventory.producttemplate'),
        ),
        migrations.AddField(
            model_name='product',
            name='write_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='irsequence',
            name='company',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company'),
        ),
        migrations.AddField(
            model_name='irsequence',
            name='create_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='irsequence',
            name='write_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='stockquant',
            index=models.Index(fields=['product', 'location'], name='inventory_s_product_3e8a54_idx'),
        ),
        migrations.AddIndex(
            model_name='stockquant',
            index=models.Index(fields=['location'], name='inventory_s_locatio_dcffc0_idx'),
        ),
        migrations.AddConstraint(
            model_name='stockquant',
            constraint=models.UniqueConstraint(fields=('product', 'location', 'lot', 'package', 'owner'), name='stock_quant_unique'),
        ),
        migrations.AddIndex(
            model_name='stockpicking',
            index=models.Index(fields=['state'], name='inventory_s_state_d3a102_idx'),
        ),
        migrations.AddIndex(
            model_name='stockpicking',
            index=models.Index(fields=['scheduled_date'], name='inventory_s_schedul_0fda34_idx'),
        ),
        migrations.AddIndex(
            model_name='stockpicking',
            index=models.Index(fields=['picking_type'], name='inventory_s_picking_b1d7b6_idx'),
        ),
        migrations.AddIndex(
            model_name='stockmove',
            index=models.Index(fields=['product'], name='inventory_s_product_1773fa_idx'),
        ),
        migrations.AddIndex(
            model_name='stockmove',
            index=models.Index(fields=['location'], name='inventory_s_locatio_9eaafb_idx'),
        ),
        migrations.AddIndex(
            model_name='stockmove',
            index=models.Index(fields=['location_dest'], name='inventory_s_locatio_c98f0a_idx'),
        ),
        migrations.AddIndex(
            model_name='stockmove',
            index=models.Index(fields=['state'], name='inventory_s_state_4128a7_idx'),
        ),
        migrations.AddIndex(
            model_name='stockmove',
            index=models.Index(fields=['date'], name='inventory_s_date_0c801d_idx'),
        ),
        migrations.AddIndex(
            model_name='stockmove',
            index=models.Index(fields=['production'], name='inventory_s_product_a94951_idx'),
        ),
        migrations.AddIndex(
            model_name='stockmove',
            index=models.Index(fields=['raw_material_production'], name='inventory_s_raw_mat_7ab046_idx'),
        ),
        migrations.AddIndex(
            model_name='stockmove',
            index=models.Index(fields=['is_done'], name='inventory_s_is_done_e6a029_idx'),
        ),
        migrations.AddIndex(
            model_name='stocklot',
            index=models.Index(fields=['product'], name='inventory_s_product_c0893e_idx'),
        ),
        migrations.AddIndex(
            model_name='stocklot',
            index=models.Index(fields=['name'], name='inventory_s_name_1f8600_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='stocklot',
            unique_together={('name', 'product', 'company')},
        ),
        migrations.AddIndex(
            model_name='stocklocation',
            index=models.Index(fields=['usage'], name='inventory_s_usage_3a69d4_idx'),
        ),
        migrations.AddIndex(
            model_name='stocklocation',
            index=models.Index(fields=['parent_path'], name='inventory_s_parent__6b0fa8_idx'),
        ),
        migrations.AddConstraint(
            model_name='stocklocation',
            constraint=models.UniqueConstraint(condition=models.Q(('barcode__isnull', False), models.Q(('barcode', ''), _negated=True)), fields=('barcode', 'company'), name='barcode_company_uniq'),
        ),
        migrations.AddIndex(
            model_name='producttemplate',
            index=models.Index(fields=['name'], name='inventory_p_name_44178f_idx'),
        ),
        migrations.AddIndex(
            model_name='producttemplate',
            index=models.Index(fields=['default_code'], name='inventory_p_default_40f998_idx'),
        ),
        migrations.AddIndex(
            model_name='producttemplate',
            index=models.Index(fields=['detailed_type'], name='inventory_p_detaile_368c8e_idx'),
        ),
        migrations.AddIndex(
            model_name='productpackaging',
            index=models.Index(fields=['product'], name='inventory_p_product_9672a8_idx'),
        ),
        migrations.AddIndex(
            model_name='productpackaging',
            index=models.Index(fields=['product_tmpl'], name='inventory_p_product_8ed211_idx'),
        ),
        migrations.AddConstraint(
            model_name='productpackaging',
            constraint=models.CheckConstraint(check=models.Q(('qty__gt', 0)), name='packaging_positive_qty'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['product_tmpl'], name='inventory_p_product_bc4901_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['default_code'], name='inventory_p_default_b9bbbb_idx'),
        ),
    ]
