from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from core.models import Currency


class Command(BaseCommand):
    help = 'Populate database with common currencies'

    def handle(self, *args, **options):
        # Get admin user for create_uid and write_uid
        admin_user = User.objects.filter(is_superuser=True).first()
        if not admin_user:
            self.stdout.write(self.style.ERROR('No admin user found. Please create a superuser first.'))
            return

        currencies_data = [
            {
                'name': 'USD',
                'symbol': '$',
                'full_name': 'US Dollar',
                'decimal_places': 2,
                'active': True
            },
            {
                'name': 'EUR',
                'symbol': '€',
                'full_name': 'Euro',
                'decimal_places': 2,
                'active': True
            },
            {
                'name': 'GBP',
                'symbol': '£',
                'full_name': 'British Pound Sterling',
                'decimal_places': 2,
                'active': True
            },
            {
                'name': 'CAD',
                'symbol': 'C$',
                'full_name': 'Canadian Dollar',
                'decimal_places': 2,
                'active': True
            },
            {
                'name': 'AUD',
                'symbol': 'A$',
                'full_name': 'Australian Dollar',
                'decimal_places': 2,
                'active': True
            },
            {
                'name': 'JPY',
                'symbol': '¥',
                'full_name': 'Japanese Yen',
                'decimal_places': 0,
                'active': True
            },
            {
                'name': 'CHF',
                'symbol': 'CHF',
                'full_name': 'Swiss Franc',
                'decimal_places': 2,
                'active': True
            },
            {
                'name': 'CNY',
                'symbol': '¥',
                'full_name': 'Chinese Yuan',
                'decimal_places': 2,
                'active': True
            },
            {
                'name': 'INR',
                'symbol': '₹',
                'full_name': 'Indian Rupee',
                'decimal_places': 2,
                'active': True
            },
            {
                'name': 'BRL',
                'symbol': 'R$',
                'full_name': 'Brazilian Real',
                'decimal_places': 2,
                'active': False
            }
        ]

        created_count = 0
        updated_count = 0

        for currency_data in currencies_data:
            currency, created = Currency.objects.get_or_create(
                name=currency_data['name'],
                defaults={
                    'symbol': currency_data['symbol'],
                    'full_name': currency_data['full_name'],
                    'decimal_places': currency_data['decimal_places'],
                    'active': currency_data['active'],
                    'create_uid': admin_user,
                    'write_uid': admin_user
                }
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'Created currency: {currency.name} - {currency.full_name}')
                )
            else:
                # Update existing currency
                currency.symbol = currency_data['symbol']
                currency.full_name = currency_data['full_name']
                currency.decimal_places = currency_data['decimal_places']
                currency.active = currency_data['active']
                currency.write_uid = admin_user
                currency.save()
                updated_count += 1
                self.stdout.write(
                    self.style.WARNING(f'Updated currency: {currency.name} - {currency.full_name}')
                )

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully processed {created_count + updated_count} currencies '
                f'({created_count} created, {updated_count} updated)'
            )
        )
