/* Accounting Dashboard Styles */
.accounting-dashboard {
  min-height: 100vh;
  background-color: var(--odoo-bg-secondary);
  padding: var(--odoo-spacing-lg);
}

.accounting-dashboard.loading {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  text-align: center;
  color: var(--odoo-text-secondary);
}

.loading-spinner i {
  font-size: 2rem;
  color: var(--odoo-primary);
  margin-bottom: var(--odoo-spacing-md);
}

/* Header */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--odoo-spacing-xl);
  background: var(--odoo-white);
  padding: var(--odoo-spacing-lg);
  border-radius: var(--odoo-border-radius-lg);
  box-shadow: var(--odoo-shadow-sm);
}

.header-content h1 {
  font-size: var(--odoo-font-size-2xl);
  font-weight: var(--odoo-font-weight-bold);
  color: var(--odoo-text-primary);
  margin-bottom: var(--odoo-spacing-xs);
  display: flex;
  align-items: center;
  gap: var(--odoo-spacing-sm);
}

.header-content h1 i {
  color: var(--odoo-primary);
}

.header-content p {
  color: var(--odoo-text-secondary);
  margin: 0;
}

.header-actions .btn {
  padding: var(--odoo-spacing-sm) var(--odoo-spacing-lg);
  background: var(--odoo-primary);
  color: var(--odoo-white);
  border: none;
  border-radius: var(--odoo-border-radius);
  font-weight: var(--odoo-font-weight-medium);
  cursor: pointer;
  transition: all var(--odoo-transition-fast);
  display: flex;
  align-items: center;
  gap: var(--odoo-spacing-sm);
}

.header-actions .btn:hover {
  background: var(--odoo-primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--odoo-shadow);
}

.header-actions .btn-secondary {
  background: var(--odoo-white);
  color: var(--odoo-text-primary);
  border: 1px solid var(--odoo-border-medium);
}

.header-actions .btn-secondary:hover {
  background: var(--odoo-bg-light);
  border-color: var(--odoo-primary);
  color: var(--odoo-primary);
}

/* Metrics Grid */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--odoo-spacing-lg);
  margin-bottom: var(--odoo-spacing-xl);
}

.metric-card {
  background: var(--odoo-white);
  border-radius: var(--odoo-border-radius-lg);
  padding: var(--odoo-spacing-lg);
  box-shadow: var(--odoo-shadow-sm);
  border-left: 4px solid;
  display: flex;
  align-items: center;
  gap: var(--odoo-spacing-md);
  transition: all var(--odoo-transition-fast);
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--odoo-shadow);
}

.metric-card.receivables {
  border-left-color: var(--odoo-success);
}

.metric-card.payables {
  border-left-color: var(--odoo-danger);
}

.metric-card.bank {
  border-left-color: var(--odoo-info);
}

.metric-card.revenue {
  border-left-color: var(--odoo-primary);
}

.metric-icon {
  width: 60px;
  height: 60px;
  border-radius: var(--odoo-border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--odoo-font-size-xl);
  color: var(--odoo-white);
}

.metric-card.receivables .metric-icon {
  background: var(--odoo-success);
}

.metric-card.payables .metric-icon {
  background: var(--odoo-danger);
}

.metric-card.bank .metric-icon {
  background: var(--odoo-info);
}

.metric-card.revenue .metric-icon {
  background: var(--odoo-primary);
}

.metric-content h3 {
  font-size: var(--odoo-font-size-xl);
  font-weight: var(--odoo-font-weight-bold);
  color: var(--odoo-text-primary);
  margin-bottom: var(--odoo-spacing-xs);
}

.metric-content p {
  color: var(--odoo-text-secondary);
  margin-bottom: var(--odoo-spacing-xs);
  font-size: var(--odoo-font-size-sm);
}

.metric-change {
  font-size: var(--odoo-font-size-xs);
  font-weight: var(--odoo-font-weight-medium);
  padding: 2px 6px;
  border-radius: var(--odoo-border-radius-sm);
}

.metric-change.positive {
  background: rgba(40, 167, 69, 0.1);
  color: var(--odoo-success);
}

.metric-change.negative {
  background: rgba(220, 53, 69, 0.1);
  color: var(--odoo-danger);
}

/* Sections */
.section {
  margin-bottom: var(--odoo-spacing-xl);
}

.section h2 {
  font-size: var(--odoo-font-size-xl);
  font-weight: var(--odoo-font-weight-semibold);
  color: var(--odoo-text-primary);
  margin-bottom: var(--odoo-spacing-lg);
}

/* Quick Actions */
.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--odoo-spacing-md);
}

.quick-action-card {
  background: var(--odoo-white);
  border-radius: var(--odoo-border-radius-lg);
  padding: var(--odoo-spacing-lg);
  box-shadow: var(--odoo-shadow-sm);
  border-left: 4px solid;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all var(--odoo-transition-fast);
  cursor: pointer;
}

.quick-action-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--odoo-shadow);
}

.action-icon {
  font-size: var(--odoo-font-size-2xl);
}

.action-content h4 {
  font-size: var(--odoo-font-size-base);
  font-weight: var(--odoo-font-weight-semibold);
  color: var(--odoo-text-primary);
  margin-bottom: var(--odoo-spacing-xs);
}

.btn-sm {
  padding: var(--odoo-spacing-xs) var(--odoo-spacing-sm);
  font-size: var(--odoo-font-size-sm);
}

.btn-outline {
  background: transparent;
  border: 1px solid var(--odoo-border-medium);
  color: var(--odoo-text-secondary);
}

.btn-outline:hover {
  background: var(--odoo-bg-light);
  border-color: var(--odoo-primary);
  color: var(--odoo-primary);
}

/* Reports Grid */
.reports-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--odoo-spacing-lg);
}

.report-card {
  background: var(--odoo-white);
  border-radius: var(--odoo-border-radius-lg);
  padding: var(--odoo-spacing-lg);
  box-shadow: var(--odoo-shadow-sm);
  text-align: center;
  transition: all var(--odoo-transition-fast);
}

.report-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--odoo-shadow);
}

.report-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--odoo-primary), var(--odoo-primary-light));
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--odoo-spacing-md);
  color: var(--odoo-white);
  font-size: var(--odoo-font-size-xl);
}

.report-content h4 {
  font-size: var(--odoo-font-size-lg);
  font-weight: var(--odoo-font-weight-semibold);
  color: var(--odoo-text-primary);
  margin-bottom: var(--odoo-spacing-sm);
}

.report-content p {
  color: var(--odoo-text-secondary);
  margin-bottom: var(--odoo-spacing-md);
  font-size: var(--odoo-font-size-sm);
}

.btn-primary {
  background: var(--odoo-primary);
  color: var(--odoo-white);
  border: none;
}

.btn-primary:hover {
  background: var(--odoo-primary-hover);
}

/* Activity List */
.activity-list {
  background: var(--odoo-white);
  border-radius: var(--odoo-border-radius-lg);
  box-shadow: var(--odoo-shadow-sm);
  overflow: hidden;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: var(--odoo-spacing-md);
  padding: var(--odoo-spacing-lg);
  border-bottom: 1px solid var(--odoo-border-light);
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--odoo-white);
  font-size: var(--odoo-font-size-base);
}

.activity-icon.invoice {
  background: var(--odoo-success);
}

.activity-icon.payment {
  background: var(--odoo-info);
}

.activity-icon.journal {
  background: var(--odoo-warning);
}

.activity-content h4 {
  font-size: var(--odoo-font-size-base);
  font-weight: var(--odoo-font-weight-medium);
  color: var(--odoo-text-primary);
  margin-bottom: var(--odoo-spacing-xs);
}

.activity-content p {
  color: var(--odoo-text-secondary);
  margin-bottom: var(--odoo-spacing-xs);
  font-size: var(--odoo-font-size-sm);
}

.activity-time {
  font-size: var(--odoo-font-size-xs);
  color: var(--odoo-text-muted);
}

/* Responsive Design */
@media (max-width: 768px) {
  .accounting-dashboard {
    padding: var(--odoo-spacing-md);
  }

  .dashboard-header {
    flex-direction: column;
    gap: var(--odoo-spacing-md);
    text-align: center;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .quick-actions-grid {
    grid-template-columns: 1fr;
  }

  .reports-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}
