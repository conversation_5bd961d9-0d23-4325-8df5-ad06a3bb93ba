/* Odoo <PERSON> Styles */
.odoo-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--odoo-spacing-sm);
  font-family: inherit;
  font-weight: var(--odoo-font-weight-medium);
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: 1px solid transparent;
  border-radius: var(--odoo-border-radius);
  transition: all var(--odoo-transition-fast);
  position: relative;
  overflow: hidden;
}

.odoo-btn:focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(113, 75, 103, 0.25);
}

/* <PERSON><PERSON> Variants */
.odoo-btn--primary {
  color: var(--odoo-white);
  background-color: var(--odoo-primary);
  border-color: var(--odoo-primary);
}

.odoo-btn--primary:hover:not(.odoo-btn--disabled) {
  background-color: var(--odoo-primary-hover);
  border-color: var(--odoo-primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--odoo-shadow);
}

.odoo-btn--secondary {
  color: var(--odoo-text-primary);
  background-color: var(--odoo-white);
  border-color: var(--odoo-border-medium);
}

.odoo-btn--secondary:hover:not(.odoo-btn--disabled) {
  background-color: var(--odoo-bg-light);
  border-color: var(--odoo-border-dark);
}

.odoo-btn--outline {
  color: var(--odoo-primary);
  background-color: transparent;
  border-color: var(--odoo-primary);
}

.odoo-btn--outline:hover:not(.odoo-btn--disabled) {
  color: var(--odoo-white);
  background-color: var(--odoo-primary);
  border-color: var(--odoo-primary);
}

.odoo-btn--ghost {
  color: var(--odoo-primary);
  background-color: transparent;
  border-color: transparent;
}

.odoo-btn--ghost:hover:not(.odoo-btn--disabled) {
  background-color: var(--odoo-bg-light);
}

.odoo-btn--success {
  color: var(--odoo-white);
  background-color: var(--odoo-success);
  border-color: var(--odoo-success);
}

.odoo-btn--danger {
  color: var(--odoo-white);
  background-color: var(--odoo-danger);
  border-color: var(--odoo-danger);
}

/* Button Sizes */
.odoo-btn--small {
  padding: var(--odoo-spacing-xs) var(--odoo-spacing-sm);
  font-size: var(--odoo-font-size-sm);
  line-height: 1.4;
}

.odoo-btn--medium {
  padding: var(--odoo-spacing-sm) var(--odoo-spacing-md);
  font-size: var(--odoo-font-size-base);
  line-height: 1.5;
}

.odoo-btn--large {
  padding: var(--odoo-spacing-md) var(--odoo-spacing-lg);
  font-size: var(--odoo-font-size-lg);
  line-height: 1.5;
}

/* Button States */
.odoo-btn--disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.odoo-btn--loading {
  cursor: wait;
  pointer-events: none;
}

.odoo-btn--full-width {
  width: 100%;
}

/* Button Elements */
.odoo-btn__spinner {
  animation: spin 1s linear infinite;
}

.odoo-btn__icon {
  font-size: 0.9em;
}

.odoo-btn__text {
  display: inline-block;
}

/* Loading Animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 767px) {
  .odoo-btn {
    padding: var(--odoo-spacing-sm) var(--odoo-spacing-md);
    font-size: var(--odoo-font-size-base);
  }
  
  .odoo-btn--small {
    padding: var(--odoo-spacing-xs) var(--odoo-spacing-sm);
    font-size: var(--odoo-font-size-sm);
  }
}
