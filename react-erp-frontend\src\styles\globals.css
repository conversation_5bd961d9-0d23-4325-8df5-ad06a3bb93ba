/* Import Odoo Variables */
@import './variables.css';

/* Import Google Fonts - Roboto (Odoo's font) */
@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700&display=swap');

/* Import Font Awesome */
@import '@fortawesome/fontawesome-free/css/all.min.css';

/* Global Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Root Styles */
html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--odoo-font-family-sans-serif);
  font-size: var(--odoo-font-size-base);
  font-weight: var(--odoo-font-weight-normal);
  line-height: var(--odoo-line-height-base);
  color: var(--odoo-text-primary);
  background-color: var(--odoo-bg-secondary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--odoo-font-family-sans-serif);
  font-weight: var(--odoo-font-weight-medium);
  line-height: var(--odoo-line-height-sm);
  margin-bottom: var(--odoo-spacing-md);
  color: var(--odoo-text-primary);
}

h1 { font-size: var(--odoo-font-size-3xl); }
h2 { font-size: var(--odoo-font-size-2xl); }
h3 { font-size: var(--odoo-font-size-xl); }
h4 { font-size: var(--odoo-font-size-lg); }
h5 { font-size: var(--odoo-font-size-base); }
h6 { font-size: var(--odoo-font-size-sm); }

p {
  margin-bottom: var(--odoo-spacing-md);
  color: var(--odoo-text-secondary);
}

/* Links */
a {
  color: var(--odoo-primary);
  text-decoration: none;
  transition: color var(--odoo-transition-fast);
}

a:hover {
  color: var(--odoo-primary-hover);
  text-decoration: underline;
}

/* Form Elements */
input, textarea, select {
  font-family: inherit;
  font-size: var(--odoo-font-size-base);
}

/* Utility Classes */
.text-primary { color: var(--odoo-primary) !important; }
.text-secondary { color: var(--odoo-text-secondary) !important; }
.text-muted { color: var(--odoo-text-muted) !important; }
.text-white { color: var(--odoo-text-white) !important; }
.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }

.bg-primary { background-color: var(--odoo-primary) !important; }
.bg-secondary { background-color: var(--odoo-bg-secondary) !important; }
.bg-light { background-color: var(--odoo-bg-light) !important; }
.bg-white { background-color: var(--odoo-white) !important; }

.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }

.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.align-items-center { align-items: center !important; }
.flex-column { flex-direction: column !important; }

.w-100 { width: 100% !important; }
.h-100 { height: 100% !important; }

.m-0 { margin: 0 !important; }
.m-1 { margin: var(--odoo-spacing-xs) !important; }
.m-2 { margin: var(--odoo-spacing-sm) !important; }
.m-3 { margin: var(--odoo-spacing-md) !important; }
.m-4 { margin: var(--odoo-spacing-lg) !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: var(--odoo-spacing-xs) !important; }
.p-2 { padding: var(--odoo-spacing-sm) !important; }
.p-3 { padding: var(--odoo-spacing-md) !important; }
.p-4 { padding: var(--odoo-spacing-lg) !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: var(--odoo-spacing-xs) !important; }
.mb-2 { margin-bottom: var(--odoo-spacing-sm) !important; }
.mb-3 { margin-bottom: var(--odoo-spacing-md) !important; }
.mb-4 { margin-bottom: var(--odoo-spacing-lg) !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: var(--odoo-spacing-xs) !important; }
.mt-2 { margin-top: var(--odoo-spacing-sm) !important; }
.mt-3 { margin-top: var(--odoo-spacing-md) !important; }
.mt-4 { margin-top: var(--odoo-spacing-lg) !important; }

/* Responsive Utilities */
@media (max-width: 767px) {
  .d-md-none { display: none !important; }
  .d-md-block { display: block !important; }
}

@media (min-width: 768px) {
  .d-md-none { display: block !important; }
  .d-sm-none { display: none !important; }
}

/* Scrollbar Styling (Webkit) */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--odoo-bg-light);
}

::-webkit-scrollbar-thumb {
  background: var(--odoo-border-medium);
  border-radius: var(--odoo-border-radius-sm);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--odoo-border-dark);
}
