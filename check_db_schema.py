#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to check the database schema and column names.
"""

import os
import sys

# Add the django_erp directory to the Python path
sys.path.insert(0, os.path.join(os.getcwd(), 'django_erp'))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')

import django
django.setup()

from django.db import connection

def check_table_columns(table_name):
    """Check columns for a specific table."""
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = %s 
            ORDER BY ordinal_position
        """, [table_name])
        columns = cursor.fetchall()
        
        print(f"\nTable: {table_name}")
        print("-" * 50)
        for col_name, data_type in columns:
            print(f"  {col_name:<30} {data_type}")

def main():
    """Check key tables for correct column names."""
    tables_to_check = [
        'accounting_accountaccount',
        'accounting_accountgroup', 
        'core_company',
        'core_partner',
        'core_countrygroup',
        'core_countrystate'
    ]
    
    with connection.cursor() as cursor:
        # Get all table names
        cursor.execute("""
            SELECT tablename FROM pg_tables 
            WHERE schemaname = 'public' 
            AND tablename NOT LIKE 'auth_%'
            AND tablename NOT LIKE 'django_%'
            ORDER BY tablename
        """)
        all_tables = [row[0] for row in cursor.fetchall()]
        
        print(f"Found {len(all_tables)} tables in database:")
        for table in all_tables[:10]:  # Show first 10 tables
            print(f"  - {table}")
        
        if len(all_tables) > 10:
            print(f"  ... and {len(all_tables) - 10} more")
    
    # Check specific tables for foreign key column names
    for table in tables_to_check:
        try:
            check_table_columns(table)
        except Exception as e:
            print(f"\nError checking table {table}: {e}")

if __name__ == "__main__":
    main()
