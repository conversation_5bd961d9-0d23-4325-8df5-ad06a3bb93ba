from django.db import connection

cursor = connection.cursor()
cursor.execute("SELECT column_name FROM information_schema.columns WHERE table_name = 'accounting_accountaccount' ORDER BY ordinal_position")
columns = [row[0] for row in cursor.fetchall()]
print("AccountAccount columns:")
for col in columns:
    print(f"  - {col}")

cursor.execute("SELECT column_name FROM information_schema.columns WHERE table_name = 'core_company' ORDER BY ordinal_position")
columns = [row[0] for row in cursor.fetchall()]
print("\nCompany columns:")
for col in columns:
    print(f"  - {col}")
