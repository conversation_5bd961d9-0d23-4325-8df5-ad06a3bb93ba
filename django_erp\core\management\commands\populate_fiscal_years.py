from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from core.models import Company, FiscalYear
from datetime import date


class Command(BaseCommand):
    help = 'Populate database with sample fiscal years'

    def handle(self, *args, **options):
        # Get admin user for create_uid and write_uid
        admin_user = User.objects.filter(is_superuser=True).first()
        if not admin_user:
            self.stdout.write(self.style.ERROR('No admin user found. Please create a superuser first.'))
            return

        # Get first company
        company = Company.objects.first()
        if not company:
            self.stdout.write(self.style.ERROR('No company found. Please create a company first.'))
            return

        fiscal_years_data = [
            {
                'name': 'Fiscal Year 2023',
                'code': 'FY2023',
                'date_start': date(2023, 1, 1),
                'date_end': date(2023, 12, 31),
                'state': 'closed'
            },
            {
                'name': 'Fiscal Year 2024',
                'code': 'FY2024',
                'date_start': date(2024, 1, 1),
                'date_end': date(2024, 12, 31),
                'state': 'open'
            },
            {
                'name': 'Fiscal Year 2025',
                'code': 'FY2025',
                'date_start': date(2025, 1, 1),
                'date_end': date(2025, 12, 31),
                'state': 'draft'
            }
        ]

        created_count = 0
        updated_count = 0

        for fy_data in fiscal_years_data:
            fiscal_year, created = FiscalYear.objects.get_or_create(
                code=fy_data['code'],
                company=company,
                defaults={
                    'name': fy_data['name'],
                    'date_start': fy_data['date_start'],
                    'date_end': fy_data['date_end'],
                    'state': fy_data['state'],
                    'active': True,
                    'create_uid': admin_user,
                    'write_uid': admin_user
                }
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'Created fiscal year: {fiscal_year.name} ({fiscal_year.state})')
                )
            else:
                # Update existing fiscal year
                fiscal_year.name = fy_data['name']
                fiscal_year.date_start = fy_data['date_start']
                fiscal_year.date_end = fy_data['date_end']
                fiscal_year.state = fy_data['state']
                fiscal_year.write_uid = admin_user
                fiscal_year.save()
                updated_count += 1
                self.stdout.write(
                    self.style.WARNING(f'Updated fiscal year: {fiscal_year.name} ({fiscal_year.state})')
                )

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully processed {created_count + updated_count} fiscal years '
                f'({created_count} created, {updated_count} updated)'
            )
        )
