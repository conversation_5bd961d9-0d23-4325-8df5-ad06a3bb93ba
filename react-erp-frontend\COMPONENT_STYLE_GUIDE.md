# 🎨 Odoo ERP Frontend - Component Style Guide

## 🎯 **Design System Overview**

This guide ensures **consistency across all components** in our Odoo ERP frontend application.

## 🎨 **Color System**

### **Primary Colors (Always use CSS variables)**
```css
--odoo-primary: #714B67        /* Main Odoo purple */
--odoo-primary-hover: #6A4460  /* Hover state */
--odoo-primary-light: #8B5A7F  /* Light variant */
--odoo-primary-dark: #5A3B52   /* Dark variant */
```

### **Usage Rules**
- ✅ **DO**: Use `var(--odoo-primary)` 
- ❌ **DON'T**: Use hardcoded `#714B67`

## 🔘 **Button Components**

### **Reusable Button Component**
```jsx
import Button from '../components/common/Button/Button';

<Button variant="primary" size="medium">Save</Button>
<Button variant="secondary" size="small">Cancel</Button>
```

### **Available Variants**
- `primary` - Main Odoo purple
- `secondary` - White with border
- `outline` - Transparent with purple border
- `ghost` - Transparent
- `success` - Green
- `danger` - Red

### **Available Sizes**
- `small` - Compact buttons
- `medium` - Default size
- `large` - Prominent buttons

## 📝 **Form Components**

### **Reusable FormField Component**
```jsx
import { FormField } from '../components/common/Form';

<FormField
  label="Account Name"
  name="name"
  value={formData.name}
  onChange={handleChange}
  size="medium"
  variant="underline"
  required
/>
```

### **Field Variants**
- `underline` - Odoo-style underline (default)
- `bordered` - Traditional border
- `filled` - Filled background

### **Field Sizes**
- `small` - 0.9rem font
- `medium` - 1.3rem font (default)
- `large` - 1.5rem font

## 🎨 **Styling Best Practices**

### **1. Always Use CSS Variables**
```css
/* ✅ CORRECT */
.my-component {
  color: var(--odoo-primary);
  background: var(--odoo-white);
  border: 1px solid var(--odoo-border-light);
}

/* ❌ WRONG */
.my-component {
  color: #714B67;
  background: #FFFFFF;
  border: 1px solid #E9ECEF;
}
```

### **2. Component Structure**
```
src/components/
├── common/              # Reusable components
│   ├── Button/
│   ├── Form/
│   ├── Modal/
│   └── Input/
├── accounting/          # Module-specific components
├── sales/
└── inventory/
```

### **3. CSS Organization**
- Each component has its own CSS file
- Import global variables: `@import '../../../styles/variables.css';`
- Use BEM naming: `.component__element--modifier`

## 🔄 **Reusability Checklist**

### **Before Creating a New Component:**
- [ ] Check if similar component exists in `/common/`
- [ ] Use CSS variables for colors
- [ ] Support multiple variants/sizes
- [ ] Include proper TypeScript/PropTypes
- [ ] Add to style guide documentation

### **Component Props Pattern:**
```jsx
const MyComponent = ({
  variant = 'primary',
  size = 'medium',
  disabled = false,
  className = '',
  children,
  ...props
}) => {
  const componentClass = [
    'odoo-my-component',
    `odoo-my-component--${variant}`,
    `odoo-my-component--${size}`,
    disabled ? 'odoo-my-component--disabled' : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={componentClass} {...props}>
      {children}
    </div>
  );
};
```

## 📱 **Responsive Design**

### **Breakpoints**
```css
/* Mobile */
@media (max-width: 767px) { }

/* Tablet */
@media (min-width: 768px) and (max-width: 1023px) { }

/* Desktop */
@media (min-width: 1024px) { }
```

## 🎯 **Component Usage Examples**

### **Form with Consistent Styling**
```jsx
import { FormField } from '../components/common/Form';
import Button from '../components/common/Button/Button';

const MyForm = () => (
  <form>
    <FormField
      label="Code"
      name="code"
      type="text"
      size="medium"
      variant="underline"
    />
    
    <FormField
      label="Name"
      name="name"
      type="text"
      size="medium"
      variant="underline"
      required
    />
    
    <Button variant="primary" type="submit">
      Save
    </Button>
    
    <Button variant="secondary" type="button">
      Cancel
    </Button>
  </form>
);
```

## 🔧 **Maintenance**

### **When Adding New Colors:**
1. Add to `/src/styles/variables.css`
2. Update this style guide
3. Test across all components

### **When Creating New Components:**
1. Follow the naming convention
2. Use existing CSS variables
3. Support common props (variant, size, disabled)
4. Add to this documentation

---

**Remember**: Consistency is key! Always use the reusable components and CSS variables to maintain a unified design across the entire application. 🎨✨
