from rest_framework import serializers
from django.contrib.auth.models import User
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from core.models import UserProfile, UserRole, UserRoleAssignment


class UserSerializer(serializers.ModelSerializer):
    """
    Serializer for User model.
    Used for displaying user information.
    """
    full_name = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'full_name', 
                 'is_active', 'is_staff', 'date_joined', 'last_login']
        read_only_fields = ['id', 'date_joined', 'last_login']
    
    def get_full_name(self, obj):
        """Get user's full name"""
        return f"{obj.first_name} {obj.last_name}".strip() or obj.username


class UserCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating new users.
    Includes password validation and confirmation.
    """
    password = serializers.CharField(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)
    
    class Meta:
        model = User
        fields = ['username', 'email', 'first_name', 'last_name', 'password', 'password_confirm']
        extra_kwargs = {
            'email': {'required': True},
            'first_name': {'required': True},
            'last_name': {'required': True},
        }
    
    def validate(self, attrs):
        """Validate password confirmation"""
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Passwords don't match")
        return attrs
    
    def validate_email(self, value):
        """Validate email uniqueness"""
        if User.objects.filter(email=value).exists():
            raise serializers.ValidationError("A user with this email already exists.")
        return value
    
    def create(self, validated_data):
        """Create new user with encrypted password"""
        validated_data.pop('password_confirm')
        password = validated_data.pop('password')
        
        user = User.objects.create_user(**validated_data)
        user.set_password(password)
        user.save()
        
        return user


class UserRoleSerializer(serializers.ModelSerializer):
    """Serializer for user roles"""
    class Meta:
        model = UserRole
        fields = ['id', 'name', 'code', 'description', 'can_access_accounting',
                 'can_access_sales', 'can_access_purchases', 'can_access_inventory',
                 'can_access_hr', 'can_access_project', 'can_access_manufacturing',
                 'can_access_crm', 'can_create', 'can_read', 'can_update',
                 'can_delete', 'can_approve_orders', 'can_manage_users',
                 'can_view_reports', 'can_export_data']


class UserRoleAssignmentSerializer(serializers.ModelSerializer):
    """Serializer for user role assignments"""
    role = UserRoleSerializer(read_only=True)
    company_name = serializers.CharField(source='company.name', read_only=True)

    class Meta:
        model = UserRoleAssignment
        fields = ['id', 'role', 'company', 'company_name', 'is_active',
                 'valid_from', 'valid_to']


class UserProfileSerializer(serializers.ModelSerializer):
    """
    Serializer for user profile management with role information.
    """
    full_name = serializers.SerializerMethodField()
    roles = serializers.SerializerMethodField()
    permissions = serializers.SerializerMethodField()
    profile = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'full_name',
                 'is_active', 'date_joined', 'last_login', 'roles', 'permissions', 'profile']
        read_only_fields = ['id', 'username', 'is_active', 'date_joined', 'last_login']

    def get_full_name(self, obj):
        """Get user's full name"""
        return f"{obj.first_name} {obj.last_name}".strip() or obj.username

    def get_roles(self, obj):
        """Get user's active role assignments"""
        assignments = UserRoleAssignment.objects.filter(
            user=obj,
            is_active=True
        ).select_related('role', 'company')
        return UserRoleAssignmentSerializer(assignments, many=True).data

    def get_permissions(self, obj):
        """Get aggregated permissions from all user roles"""
        assignments = UserRoleAssignment.objects.filter(
            user=obj,
            is_active=True
        ).select_related('role')

        # Aggregate permissions (OR operation - if any role has permission, user has it)
        permissions = {
            'can_access_accounting': False,
            'can_access_sales': False,
            'can_access_purchases': False,
            'can_access_inventory': False,
            'can_access_hr': False,
            'can_access_project': False,
            'can_access_manufacturing': False,
            'can_access_crm': False,
            'can_create': False,
            'can_read': False,
            'can_update': False,
            'can_delete': False,
            'can_approve_orders': False,
            'can_manage_users': False,
            'can_view_reports': False,
            'can_export_data': False,
        }

        for assignment in assignments:
            role = assignment.role
            for perm in permissions.keys():
                if getattr(role, perm, False):
                    permissions[perm] = True

        return permissions

    def get_profile(self, obj):
        """Get extended user profile information"""
        try:
            profile = obj.profile
            return {
                'employee_id': profile.employee_id,
                'phone': profile.phone,
                'mobile': profile.mobile,
                'company': profile.company.name if profile.company else None,
                'department': profile.department.name if profile.department else None,
                'language': profile.language,
                'timezone': profile.timezone,
                'is_active_employee': profile.is_active_employee,
                'hire_date': profile.hire_date,
            }
        except UserProfile.DoesNotExist:
            return None

    def validate_email(self, value):
        """Validate email uniqueness (excluding current user)"""
        user = self.instance
        if user and User.objects.filter(email=value).exclude(id=user.id).exists():
            raise serializers.ValidationError("A user with this email already exists.")
        return value


class PasswordChangeSerializer(serializers.Serializer):
    """
    Serializer for password change functionality.
    """
    old_password = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True, validators=[validate_password])
    new_password_confirm = serializers.CharField(required=True)
    
    def validate(self, attrs):
        """Validate password confirmation"""
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError("New passwords don't match")
        return attrs
    
    def validate_old_password(self, value):
        """Validate old password"""
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError("Invalid old password")
        return value


class LoginSerializer(serializers.Serializer):
    """
    Serializer for user login.
    """
    username = serializers.CharField(required=True)
    password = serializers.CharField(required=True, write_only=True)
    
    def validate(self, attrs):
        """Validate login credentials"""
        from django.contrib.auth import authenticate
        
        username = attrs.get('username')
        password = attrs.get('password')
        
        if username and password:
            user = authenticate(username=username, password=password)
            if not user:
                raise serializers.ValidationError("Invalid credentials")
            if not user.is_active:
                raise serializers.ValidationError("User account is disabled")
            
            attrs['user'] = user
        else:
            raise serializers.ValidationError("Must include username and password")
        
        return attrs
