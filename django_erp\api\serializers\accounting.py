from rest_framework import serializers
from accounting.models import (
    AccountGroup, AccountAccount, AccountJournal, AccountMove, 
    AccountMoveLine, AccountPayment, AccountTax
)
from core.models import Company, Partner, Currency


class AccountGroupSerializer(serializers.ModelSerializer):
    """Serializer for Account Groups"""
    company_name = serializers.CharField(source='company.name', read_only=True)
    
    class Meta:
        model = AccountGroup
        fields = ['id', 'name', 'code_prefix_start', 'code_prefix_end', 
                 'company', 'company_name', 'create_date', 'write_date']
        read_only_fields = ['id', 'create_date', 'write_date']


class AccountAccountSerializer(serializers.ModelSerializer):
    """Serializer for Chart of Accounts"""
    group_name = serializers.CharField(source='group.name', read_only=True)
    company_name = serializers.CharField(source='company.name', read_only=True)
    currency_name = serializers.Char<PERSON><PERSON>(source='currency.name', read_only=True)
    balance = serializers.SerializerMethodField()
    
    class Meta:
        model = AccountAccount
        fields = ['id', 'name', 'code', 'account_type', 'reconcile', 'active',
                 'group', 'group_name', 'company', 'company_name', 
                 'currency', 'currency_name', 'balance', 'create_date', 'write_date']
        read_only_fields = ['id', 'balance', 'create_date', 'write_date']
    
    def get_balance(self, obj):
        """Calculate account balance from move lines"""
        # TODO: Implement balance calculation from AccountMoveLine
        # For now, return a mock balance based on account type
        if obj.account_type in ['asset_cash', 'asset_receivable', 'asset_current', 'asset_fixed']:
            return 25000.00  # Mock positive balance for assets
        elif obj.account_type in ['liability_payable', 'liability_current', 'liability_non_current']:
            return -15000.00  # Mock negative balance for liabilities
        elif obj.account_type == 'income':
            return -50000.00  # Mock negative balance for income
        elif obj.account_type in ['expense', 'expense_direct_cost']:
            return 30000.00  # Mock positive balance for expenses
        else:
            return 0.00


class AccountJournalSerializer(serializers.ModelSerializer):
    """Serializer for Account Journals"""
    company_name = serializers.CharField(source='company.name', read_only=True)
    default_account_name = serializers.CharField(source='default_account.name', read_only=True)
    currency_name = serializers.CharField(source='currency.name', read_only=True)
    
    class Meta:
        model = AccountJournal
        fields = ['id', 'name', 'code', 'type', 'active', 'sequence',
                 'default_account', 'default_account_name', 'company', 'company_name',
                 'currency', 'currency_name', 'create_date', 'write_date']
        read_only_fields = ['id', 'create_date', 'write_date']


class AccountMoveLineSerializer(serializers.ModelSerializer):
    """Serializer for Account Move Lines (Journal Entry Lines)"""
    account_name = serializers.CharField(source='account.name', read_only=True)
    account_code = serializers.CharField(source='account.code', read_only=True)
    partner_name = serializers.CharField(source='partner.name', read_only=True)
    
    class Meta:
        model = AccountMoveLine
        fields = ['id', 'name', 'account', 'account_name', 'account_code',
                 'partner', 'partner_name', 'debit', 'credit', 'balance',
                 'reconciled', 'date', 'create_date', 'write_date']
        read_only_fields = ['id', 'balance', 'create_date', 'write_date']


class AccountMoveSerializer(serializers.ModelSerializer):
    """Serializer for Account Moves (Journal Entries)"""
    journal_name = serializers.CharField(source='journal.name', read_only=True)
    partner_name = serializers.CharField(source='partner.name', read_only=True)
    company_name = serializers.CharField(source='company.name', read_only=True)
    line_ids = AccountMoveLineSerializer(many=True, read_only=True)
    
    class Meta:
        model = AccountMove
        fields = ['id', 'name', 'ref', 'date', 'journal', 'journal_name',
                 'partner', 'partner_name', 'company', 'company_name',
                 'state', 'move_type', 'amount_total', 'amount_residual',
                 'line_ids', 'create_date', 'write_date']
        read_only_fields = ['id', 'name', 'amount_total', 'amount_residual', 'create_date', 'write_date']


class AccountPaymentSerializer(serializers.ModelSerializer):
    """Serializer for Account Payments"""
    partner_name = serializers.CharField(source='partner.name', read_only=True)
    journal_name = serializers.CharField(source='journal.name', read_only=True)
    company_name = serializers.CharField(source='company.name', read_only=True)
    currency_name = serializers.CharField(source='currency.name', read_only=True)
    
    class Meta:
        model = AccountPayment
        fields = ['id', 'name', 'payment_type', 'partner_type', 'amount',
                 'currency', 'currency_name', 'date', 'ref', 'state',
                 'partner', 'partner_name', 'journal', 'journal_name',
                 'company', 'company_name', 'create_date', 'write_date']
        read_only_fields = ['id', 'name', 'create_date', 'write_date']


class AccountTaxSerializer(serializers.ModelSerializer):
    """Serializer for Account Taxes"""
    company_name = serializers.CharField(source='company.name', read_only=True)
    
    class Meta:
        model = AccountTax
        fields = ['id', 'name', 'amount', 'amount_type', 'type_tax_use',
                 'active', 'sequence', 'company', 'company_name',
                 'create_date', 'write_date']
        read_only_fields = ['id', 'create_date', 'write_date']


# Dashboard Statistics Serializer
class AccountingDashboardSerializer(serializers.Serializer):
    """Serializer for accounting dashboard statistics"""
    total_receivables = serializers.DecimalField(max_digits=16, decimal_places=2)
    total_payables = serializers.DecimalField(max_digits=16, decimal_places=2)
    bank_balance = serializers.DecimalField(max_digits=16, decimal_places=2)
    monthly_revenue = serializers.DecimalField(max_digits=16, decimal_places=2)
    pending_invoices = serializers.IntegerField()
    overdue_invoices = serializers.IntegerField()
    
    # Recent activities
    recent_activities = serializers.ListField(
        child=serializers.DictField(), 
        read_only=True
    )
