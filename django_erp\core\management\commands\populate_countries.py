from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from core.models import Country


class Command(BaseCommand):
    help = 'Populate database with common countries'

    def handle(self, *args, **options):
        # Get admin user for create_uid and write_uid
        admin_user = User.objects.filter(is_superuser=True).first()
        if not admin_user:
            self.stdout.write(self.style.ERROR('No admin user found. Please create a superuser first.'))
            return

        countries_data = [
            {'name': 'United States', 'code': 'US', 'phone_code': 1},
            {'name': 'Canada', 'code': 'CA', 'phone_code': 1},
            {'name': 'United Kingdom', 'code': 'GB', 'phone_code': 44},
            {'name': 'Germany', 'code': 'DE', 'phone_code': 49},
            {'name': 'France', 'code': 'FR', 'phone_code': 33},
            {'name': 'Italy', 'code': 'IT', 'phone_code': 39},
            {'name': 'Spain', 'code': 'ES', 'phone_code': 34},
            {'name': 'Netherlands', 'code': 'NL', 'phone_code': 31},
            {'name': 'Belgium', 'code': 'BE', 'phone_code': 32},
            {'name': 'Switzerland', 'code': 'CH', 'phone_code': 41},
            {'name': 'Austria', 'code': 'AT', 'phone_code': 43},
            {'name': 'Australia', 'code': 'AU', 'phone_code': 61},
            {'name': 'New Zealand', 'code': 'NZ', 'phone_code': 64},
            {'name': 'Japan', 'code': 'JP', 'phone_code': 81},
            {'name': 'South Korea', 'code': 'KR', 'phone_code': 82},
            {'name': 'China', 'code': 'CN', 'phone_code': 86},
            {'name': 'India', 'code': 'IN', 'phone_code': 91},
            {'name': 'Brazil', 'code': 'BR', 'phone_code': 55},
            {'name': 'Mexico', 'code': 'MX', 'phone_code': 52},
            {'name': 'Argentina', 'code': 'AR', 'phone_code': 54},
            {'name': 'South Africa', 'code': 'ZA', 'phone_code': 27},
            {'name': 'Egypt', 'code': 'EG', 'phone_code': 20},
            {'name': 'Nigeria', 'code': 'NG', 'phone_code': 234},
            {'name': 'Kenya', 'code': 'KE', 'phone_code': 254},
            {'name': 'Singapore', 'code': 'SG', 'phone_code': 65},
            {'name': 'Malaysia', 'code': 'MY', 'phone_code': 60},
            {'name': 'Thailand', 'code': 'TH', 'phone_code': 66},
            {'name': 'Philippines', 'code': 'PH', 'phone_code': 63},
            {'name': 'Indonesia', 'code': 'ID', 'phone_code': 62},
            {'name': 'Vietnam', 'code': 'VN', 'phone_code': 84},
        ]

        created_count = 0
        updated_count = 0

        for country_data in countries_data:
            country, created = Country.objects.get_or_create(
                code=country_data['code'],
                defaults={
                    'name': country_data['name'],
                    'phone_code': country_data['phone_code'],
                    'active': True,
                    'create_uid': admin_user,
                    'write_uid': admin_user
                }
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'Created country: {country.code} - {country.name}')
                )
            else:
                # Update existing country
                country.name = country_data['name']
                country.phone_code = country_data['phone_code']
                country.write_uid = admin_user
                country.save()
                updated_count += 1
                self.stdout.write(
                    self.style.WARNING(f'Updated country: {country.code} - {country.name}')
                )

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully processed {created_count + updated_count} countries '
                f'({created_count} created, {updated_count} updated)'
            )
        )
