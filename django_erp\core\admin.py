from django.contrib import admin
from .models import (
    Company, Partner, Currency, Country, CountryState, CountryGroup,
    PartnerCategory
)


@admin.register(Company)
class CompanyAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'currency_id', 'active']
    list_filter = ['active', 'currency_id']
    search_fields = ['name', 'email', 'phone', 'code']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'code', 'active', 'parent_id']
        }),
        ('Contact Information', {
            'fields': ['email', 'phone', 'website']
        }),
        ('Financial', {
            'fields': ['currency_id']
        }),
        ('Address', {
            'fields': ['street', 'street2', 'city', 'state_id', 'zip', 'country_id']
        }),
        ('Additional', {
            'fields': ['vat']
        }),
    ]


@admin.register(Partner)
class PartnerAdmin(admin.ModelAdmin):
    list_display = ['name', 'email', 'phone', 'is_company', 'customer_rank', 'supplier_rank', 'active']
    list_filter = ['is_company', 'active', 'customer_rank', 'supplier_rank', 'country_id', 'partner_type']
    search_fields = ['name', 'email', 'phone', 'vat', 'ref']
    filter_horizontal = ['category_id']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'display_name', 'active', 'is_company', 'parent_id']
        }),
        ('Contact Information', {
            'fields': ['email', 'phone', 'mobile', 'website']
        }),
        ('Address', {
            'fields': ['street', 'street2', 'city', 'state_id', 'zip', 'country_id']
        }),
        ('Business', {
            'fields': ['customer_rank', 'supplier_rank', 'vat', 'partner_type']
        }),
        ('Financial', {
            'fields': ['credit_limit']
        }),
        ('Categories', {
            'fields': ['category_id']
        }),
        ('Additional', {
            'fields': ['ref']
        }),
    ]


@admin.register(Currency)
class CurrencyAdmin(admin.ModelAdmin):
    list_display = ['name', 'symbol', 'position', 'rounding', 'active']
    list_filter = ['active', 'position']
    search_fields = ['name', 'symbol']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'symbol', 'active']
        }),
        ('Display', {
            'fields': ['position', 'decimal_places']
        }),
        ('Precision', {
            'fields': ['rounding']
        }),
    ]


@admin.register(Country)
class CountryAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'phone_code']
    search_fields = ['name', 'code']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'code']
        }),
        ('Contact', {
            'fields': ['phone_code']
        }),
    ]


@admin.register(CountryState)
class CountryStateAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'country_id']
    list_filter = ['country_id']
    search_fields = ['name', 'code']


@admin.register(CountryGroup)
class CountryGroupAdmin(admin.ModelAdmin):
    list_display = ['name']
    search_fields = ['name']
    filter_horizontal = ['country_ids']


@admin.register(PartnerCategory)
class PartnerCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'parent_id', 'color']
    list_filter = ['parent_id']
    search_fields = ['name']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'parent_id']
        }),
        ('Display', {
            'fields': ['color']
        }),
    ]
