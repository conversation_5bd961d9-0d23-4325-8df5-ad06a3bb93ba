from django.contrib import admin
from .models import (
    Company, Partner, Currency, Country, CountryState, CountryGroup,
    PartnerCategory, UserProfile, UserRole, UserRoleAssignment, AccessLog
)


@admin.register(Company)
class CompanyAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'currency', 'active']
    list_filter = ['active', 'currency']
    search_fields = ['name', 'email', 'phone', 'code']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'code', 'active', 'parent']
        }),
        ('Contact Information', {
            'fields': ['email', 'phone', 'website']
        }),
        ('Financial', {
            'fields': ['currency']
        }),
        ('Address', {
            'fields': ['street', 'street2', 'city', 'state', 'zip', 'country']
        }),
        ('Additional', {
            'fields': ['vat']
        }),
    ]


@admin.register(Partner)
class PartnerAdmin(admin.ModelAdmin):
    list_display = ['name', 'email', 'phone', 'is_company', 'customer_rank', 'supplier_rank', 'active']
    list_filter = ['is_company', 'active', 'customer_rank', 'supplier_rank', 'country', 'partner_type']
    search_fields = ['name', 'email', 'phone', 'vat', 'ref']
    filter_horizontal = ['category']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'display_name', 'active', 'is_company', 'parent']
        }),
        ('Contact Information', {
            'fields': ['email', 'phone', 'mobile', 'website']
        }),
        ('Address', {
            'fields': ['street', 'street2', 'city', 'state', 'zip', 'country']
        }),
        ('Business', {
            'fields': ['customer_rank', 'supplier_rank', 'vat', 'partner_type']
        }),
        ('Financial', {
            'fields': ['credit_limit']
        }),
        ('Categories', {
            'fields': ['category']
        }),
        ('Additional', {
            'fields': ['ref']
        }),
    ]


@admin.register(Currency)
class CurrencyAdmin(admin.ModelAdmin):
    list_display = ['name', 'symbol', 'position', 'rounding', 'active']
    list_filter = ['active', 'position']
    search_fields = ['name', 'symbol']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'symbol', 'active']
        }),
        ('Display', {
            'fields': ['position', 'decimal_places']
        }),
        ('Precision', {
            'fields': ['rounding']
        }),
    ]


@admin.register(Country)
class CountryAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'phone_code']
    search_fields = ['name', 'code']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'code']
        }),
        ('Contact', {
            'fields': ['phone_code']
        }),
    ]


@admin.register(CountryState)
class CountryStateAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'country']
    list_filter = ['country']
    search_fields = ['name', 'code']


@admin.register(CountryGroup)
class CountryGroupAdmin(admin.ModelAdmin):
    list_display = ['name']
    search_fields = ['name']
    filter_horizontal = ['countries']


@admin.register(PartnerCategory)
class PartnerCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'parent', 'color']
    list_filter = ['parent']
    search_fields = ['name']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'parent']
        }),
        ('Display', {
            'fields': ['color']
        }),
    ]


# ===== USER ROLE AND PERMISSION MANAGEMENT =====

@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ['user', 'employee_id', 'company', 'department', 'is_active_employee']
    list_filter = ['company', 'department', 'is_active_employee', 'language']
    search_fields = ['user__username', 'user__first_name', 'user__last_name', 'employee_id']
    raw_id_fields = ['user', 'company', 'department']

    fieldsets = [
        ('User Information', {
            'fields': ['user', 'employee_id']
        }),
        ('Contact', {
            'fields': ['phone', 'mobile']
        }),
        ('Organization', {
            'fields': ['company', 'department']
        }),
        ('Settings', {
            'fields': ['language', 'timezone']
        }),
        ('Status', {
            'fields': ['is_active_employee', 'hire_date']
        }),
        ('Avatar', {
            'fields': ['avatar']
        }),
    ]


@admin.register(UserRole)
class UserRoleAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'can_create', 'can_update', 'can_delete', 'can_manage_users']
    list_filter = [
        'can_access_accounting', 'can_access_sales', 'can_access_purchases',
        'can_access_inventory', 'can_access_hr', 'can_create', 'can_update', 'can_delete'
    ]
    search_fields = ['name', 'code', 'description']
    filter_horizontal = ['allowed_companies']

    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'code', 'description']
        }),
        ('Module Access', {
            'fields': [
                'can_access_accounting', 'can_access_sales', 'can_access_purchases',
                'can_access_inventory', 'can_access_hr', 'can_access_project',
                'can_access_manufacturing', 'can_access_crm'
            ]
        }),
        ('CRUD Permissions', {
            'fields': ['can_create', 'can_read', 'can_update', 'can_delete']
        }),
        ('Special Permissions', {
            'fields': [
                'can_approve_orders', 'can_manage_users',
                'can_view_reports', 'can_export_data'
            ]
        }),
        ('Company Restrictions', {
            'fields': ['company_restricted', 'allowed_companies']
        }),
    ]


class UserRoleAssignmentInline(admin.TabularInline):
    model = UserRoleAssignment
    extra = 1
    raw_id_fields = ['role', 'company']


@admin.register(UserRoleAssignment)
class UserRoleAssignmentAdmin(admin.ModelAdmin):
    list_display = ['user', 'role', 'company', 'is_active', 'valid_from', 'valid_to']
    list_filter = ['role', 'company', 'is_active']
    search_fields = ['user__username', 'user__first_name', 'user__last_name', 'role__name']
    raw_id_fields = ['user', 'role', 'company']
    date_hierarchy = 'valid_from'

    fieldsets = [
        ('Assignment', {
            'fields': ['user', 'role', 'company']
        }),
        ('Validity', {
            'fields': ['valid_from', 'valid_to', 'is_active']
        }),
    ]


@admin.register(AccessLog)
class AccessLogAdmin(admin.ModelAdmin):
    list_display = ['user', 'action', 'model_name', 'object_id', 'create_date', 'ip_address']
    list_filter = ['action', 'model_name', 'create_date']
    search_fields = ['user__username', 'model_name', 'object_id', 'ip_address']
    readonly_fields = ['user', 'action', 'model_name', 'object_id', 'ip_address', 'user_agent', 'details', 'create_date']
    date_hierarchy = 'create_date'

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return request.user.is_superuser
