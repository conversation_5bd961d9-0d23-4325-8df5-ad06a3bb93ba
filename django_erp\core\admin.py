from django.contrib import admin
from .models import (
    Company, Partner, Currency, Country, CountryState, CountryGroup,
    PartnerCategory
)


@admin.register(Company)
class CompanyAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'currency', 'active']
    list_filter = ['active', 'currency']
    search_fields = ['name', 'email', 'phone', 'code']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'code', 'active', 'parent']
        }),
        ('Contact Information', {
            'fields': ['email', 'phone', 'website']
        }),
        ('Financial', {
            'fields': ['currency']
        }),
        ('Address', {
            'fields': ['street', 'street2', 'city', 'state', 'zip', 'country']
        }),
        ('Additional', {
            'fields': ['vat']
        }),
    ]


@admin.register(Partner)
class PartnerAdmin(admin.ModelAdmin):
    list_display = ['name', 'email', 'phone', 'is_company', 'customer_rank', 'supplier_rank', 'active']
    list_filter = ['is_company', 'active', 'customer_rank', 'supplier_rank', 'country', 'partner_type']
    search_fields = ['name', 'email', 'phone', 'vat', 'ref']
    filter_horizontal = ['category']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'display_name', 'active', 'is_company', 'parent']
        }),
        ('Contact Information', {
            'fields': ['email', 'phone', 'mobile', 'website']
        }),
        ('Address', {
            'fields': ['street', 'street2', 'city', 'state', 'zip', 'country']
        }),
        ('Business', {
            'fields': ['customer_rank', 'supplier_rank', 'vat', 'partner_type']
        }),
        ('Financial', {
            'fields': ['credit_limit']
        }),
        ('Categories', {
            'fields': ['category']
        }),
        ('Additional', {
            'fields': ['ref']
        }),
    ]


@admin.register(Currency)
class CurrencyAdmin(admin.ModelAdmin):
    list_display = ['name', 'symbol', 'position', 'rounding', 'active']
    list_filter = ['active', 'position']
    search_fields = ['name', 'symbol']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'symbol', 'active']
        }),
        ('Display', {
            'fields': ['position', 'decimal_places']
        }),
        ('Precision', {
            'fields': ['rounding']
        }),
    ]


@admin.register(Country)
class CountryAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'phone_code']
    search_fields = ['name', 'code']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'code']
        }),
        ('Contact', {
            'fields': ['phone_code']
        }),
    ]


@admin.register(CountryState)
class CountryStateAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'country']
    list_filter = ['country']
    search_fields = ['name', 'code']


@admin.register(CountryGroup)
class CountryGroupAdmin(admin.ModelAdmin):
    list_display = ['name']
    search_fields = ['name']
    filter_horizontal = ['countries']


@admin.register(PartnerCategory)
class PartnerCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'parent', 'color']
    list_filter = ['parent']
    search_fields = ['name']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'parent']
        }),
        ('Display', {
            'fields': ['color']
        }),
    ]
