import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { settingsAPI } from '../../services/api';
import Button from '../../components/common/Button/Button';
import CountrySettings from '../../components/settings/CountrySettings';
import CompanySettings from '../../components/settings/CompanySettings';
import CurrencySettings from '../../components/settings/CurrencySettings';
import FiscalYearSettings from '../../components/settings/FiscalYearSettings';
import './GeneralSettings.css';

const GeneralSettings = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('company');
  const [loading, setLoading] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  const tabs = [
    {
      id: 'company',
      label: 'Company',
      icon: 'fas fa-building',
      description: 'Company information and settings'
    },
    {
      id: 'currency',
      label: 'Currencies',
      icon: 'fas fa-dollar-sign',
      description: 'Manage currencies and exchange rates'
    },
    {
      id: 'country',
      label: 'Countries',
      icon: 'fas fa-globe',
      description: 'Country and localization settings'
    },
    {
      id: 'fiscal',
      label: 'Fiscal Year',
      icon: 'fas fa-calendar-alt',
      description: 'Fiscal year and accounting periods'
    }
  ];

  const handleTabChange = (tabId) => {
    if (hasChanges) {
      const confirmLeave = window.confirm(
        'You have unsaved changes. Are you sure you want to leave this tab?'
      );
      if (!confirmLeave) return;
    }
    setActiveTab(tabId);
    setHasChanges(false);
  };

  const handleDataChange = () => {
    setHasChanges(true);
  };

  const handleSave = async () => {
    setLoading(true);
    try {
      // The individual components will handle their own save logic
      setHasChanges(false);
    } catch (error) {
      console.error('Error saving settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'company':
        return <CompanySettings onDataChange={handleDataChange} />;
      case 'currency':
        return <CurrencySettings onDataChange={handleDataChange} />;
      case 'country':
        return <CountrySettings onDataChange={handleDataChange} />;
      case 'fiscal':
        return <FiscalYearSettings onDataChange={handleDataChange} />;
      default:
        return <CompanySettings onDataChange={handleDataChange} />;
    }
  };

  return (
    <div className="general-settings">
      {/* Header */}
      <div className="settings-header">
        <div className="header-content">
          <h1>
            <i className="fas fa-cog"></i>
            General Settings
          </h1>
          <p>Configure your system's basic settings and preferences</p>
        </div>
        <div className="header-actions">
          <Button
            variant="secondary"
            icon="fas fa-arrow-left"
            onClick={() => navigate('/')}
          >
            Back to Dashboard
          </Button>
          {hasChanges && (
            <Button
              variant="primary"
              icon="fas fa-save"
              onClick={handleSave}
              loading={loading}
            >
              Save Changes
            </Button>
          )}
        </div>
      </div>

      {/* Settings Navigation */}
      <div className="settings-container">
        <div className="settings-sidebar">
          <div className="settings-nav">
            {tabs.map(tab => (
              <div
                key={tab.id}
                className={`nav-item ${activeTab === tab.id ? 'active' : ''}`}
                onClick={() => handleTabChange(tab.id)}
              >
                <div className="nav-icon">
                  <i className={tab.icon}></i>
                </div>
                <div className="nav-content">
                  <h4>{tab.label}</h4>
                  <p>{tab.description}</p>
                </div>
                {activeTab === tab.id && (
                  <div className="nav-indicator">
                    <i className="fas fa-chevron-right"></i>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Quick Actions */}
          <div className="quick-actions">
            <h3>Quick Actions</h3>
            <div className="action-list">
              <button className="action-item">
                <i className="fas fa-download"></i>
                <span>Export Settings</span>
              </button>
              <button className="action-item">
                <i className="fas fa-upload"></i>
                <span>Import Settings</span>
              </button>
              <button className="action-item">
                <i className="fas fa-undo"></i>
                <span>Reset to Defaults</span>
              </button>
            </div>
          </div>
        </div>

        {/* Settings Content */}
        <div className="settings-content">
          <div className="content-header">
            <div className="tab-info">
              <h2>
                <i className={tabs.find(t => t.id === activeTab)?.icon}></i>
                {tabs.find(t => t.id === activeTab)?.label}
              </h2>
              <p>{tabs.find(t => t.id === activeTab)?.description}</p>
            </div>
            {hasChanges && (
              <div className="unsaved-indicator">
                <i className="fas fa-exclamation-circle"></i>
                <span>Unsaved changes</span>
              </div>
            )}
          </div>

          <div className="content-body">
            {renderTabContent()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default GeneralSettings;
