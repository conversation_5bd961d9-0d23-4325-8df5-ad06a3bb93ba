import React from 'react';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import LoginPage from './pages/auth/LoginPage';
import LoginPageDebug from './pages/auth/LoginPageDebug';
import Dashboard from './pages/dashboard/Dashboard';
import './styles/globals.css';

// Main App Content Component
const AppContent = () => {
  const { isAuthenticated, isLoading } = useAuth();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        backgroundColor: 'var(--odoo-bg-secondary)'
      }}>
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: '1rem'
        }}>
          <i className="fas fa-spinner fa-spin" style={{
            fontSize: '2rem',
            color: 'var(--odoo-primary)'
          }}></i>
          <p style={{
            color: 'var(--odoo-text-secondary)',
            margin: 0
          }}>Loading...</p>
        </div>
      </div>
    );
  }

  // Show appropriate page based on authentication status
  return isAuthenticated ? <Dashboard /> : <LoginPage />;
};

function App() {
  return (
    <AuthProvider>
      <div className="App">
        <AppContent />
      </div>
    </AuthProvider>
  );
}

export default App;
