/* Company Settings - Odoo Style */
.company-settings {
  max-width: 800px;
}

.company-settings.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.loading-spinner {
  text-align: center;
  color: var(--odoo-text-secondary);
}

.loading-spinner i {
  font-size: 2rem;
  color: var(--odoo-primary);
  margin-bottom: var(--odoo-spacing-md);
}

/* Form Sections */
.form-section {
  margin-bottom: var(--odoo-spacing-xl);
  padding-bottom: var(--odoo-spacing-lg);
  border-bottom: 1px solid var(--odoo-border-light);
}

.form-section:last-of-type {
  border-bottom: none;
  margin-bottom: 0;
}

.form-section h3 {
  font-size: var(--odoo-font-size-title);
  font-weight: var(--odoo-font-weight-semibold);
  color: var(--odoo-text-primary);
  margin-bottom: var(--odoo-spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--odoo-spacing-sm);
  padding-bottom: var(--odoo-spacing-sm);
  border-bottom: 2px solid var(--odoo-primary);
}

.form-section h3 i {
  color: var(--odoo-primary);
}

.form-section p {
  color: var(--odoo-text-secondary);
  margin-bottom: var(--odoo-spacing-lg);
  font-size: var(--odoo-font-size-base);
}

/* Company Actions */
.company-actions {
  display: flex;
  gap: var(--odoo-spacing-sm);
  margin-bottom: var(--odoo-spacing-xl);
}

@media (max-width: 768px) {
  .company-actions {
    flex-direction: column;
  }
}

/* Form Layout */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--odoo-spacing-lg);
  margin-bottom: var(--odoo-spacing-lg);
}

.form-row:last-child {
  margin-bottom: 0;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-size: var(--odoo-font-size-sm);
  font-weight: var(--odoo-font-weight-medium);
  color: var(--odoo-text-primary);
  margin-bottom: var(--odoo-spacing-xs);
  display: flex;
  align-items: center;
}

.form-group label.required::after {
  content: '*';
  color: var(--odoo-danger);
  margin-left: 4px;
}

.form-group input,
.form-group select {
  padding: var(--odoo-spacing-sm);
  border: 1px solid var(--odoo-border-medium);
  border-radius: var(--odoo-border-radius);
  font-size: var(--odoo-font-size-base);
  transition: border-color var(--odoo-transition-fast), box-shadow var(--odoo-transition-fast);
  background: var(--odoo-white);
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--odoo-primary);
  box-shadow: 0 0 0 0.2rem rgba(113, 75, 103, 0.25);
}

.form-group input.error,
.form-group select.error {
  border-color: var(--odoo-danger);
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-group input::placeholder {
  color: var(--odoo-text-muted);
}

/* Error Messages */
.error-text {
  color: var(--odoo-danger);
  font-size: var(--odoo-font-size-xs);
  margin-top: var(--odoo-spacing-xs);
  display: flex;
  align-items: center;
  gap: 4px;
}

.error-text::before {
  content: '⚠';
  font-size: 10px;
}

/* Form Actions */
.form-actions {
  margin-top: var(--odoo-spacing-xl);
  padding-top: var(--odoo-spacing-lg);
  border-top: 1px solid var(--odoo-border-light);
  display: flex;
  justify-content: flex-end;
}

/* Responsive Design */
@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
    gap: var(--odoo-spacing-md);
  }
  
  .form-section {
    margin-bottom: var(--odoo-spacing-lg);
    padding-bottom: var(--odoo-spacing-md);
  }
  
  .form-section h3 {
    font-size: var(--odoo-font-size-base);
    margin-bottom: var(--odoo-spacing-md);
  }
  
  .form-actions {
    margin-top: var(--odoo-spacing-lg);
  }
  
  .form-actions button {
    width: 100%;
  }
}
