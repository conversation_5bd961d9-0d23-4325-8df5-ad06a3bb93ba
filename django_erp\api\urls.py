from django.urls import path, include
from rest_framework.routers import DefaultRout<PERSON>
from rest_framework_simplejwt.views import (
    TokenObtainPairView,
    TokenRefreshView,
    TokenVerifyView,
)

# Import API viewsets
from .views.auth import UserViewSet, RegisterView, ProfileView, LoginView
from .views.core import CompanyViewSet, PartnerViewSet, CurrencyViewSet, CountryViewSet
from .views.accounting import AccountViewSet, JournalViewSet, MoveViewSet, AccountGroupViewSet
from .views.sales import SaleOrderViewSet, SaleOrderLineViewSet, ProductPricelistViewSet
from .views.purchases import PurchaseOrderViewSet, PurchaseOrderLineViewSet
from .views.inventory import ProductViewSet, ProductTemplateViewSet, StockMoveViewSet, StockPickingViewSet
from .views.mrp import ProductionViewSet, BomViewSet, WorkcenterViewSet, WorkorderViewSet

# Create router and register viewsets
router = DefaultRouter()

# Authentication & User Management
router.register(r'users', UserViewSet)

# Core Module
router.register(r'companies', CompanyViewSet)
router.register(r'partners', PartnerViewSet)
router.register(r'currencies', CurrencyViewSet)
router.register(r'countries', CountryViewSet)

# Accounting Module
router.register(r'accounting/accounts', AccountViewSet, basename='account')
router.register(r'accounting/groups', AccountGroupViewSet, basename='accountgroup')
router.register(r'accounting/journals', JournalViewSet, basename='journal')
router.register(r'accounting/moves', MoveViewSet, basename='accountmove')

# Sales Module (placeholder - will be implemented later)
router.register(r'sale-orders', SaleOrderViewSet, basename='saleorder')
router.register(r'sale-order-lines', SaleOrderLineViewSet, basename='saleorderline')
router.register(r'pricelists', ProductPricelistViewSet, basename='pricelist')

# Purchase Module (placeholder - will be implemented later)
router.register(r'purchase-orders', PurchaseOrderViewSet, basename='purchaseorder')
router.register(r'purchase-order-lines', PurchaseOrderLineViewSet, basename='purchaseorderline')

# Inventory Module (placeholder - will be implemented later)
router.register(r'products', ProductViewSet, basename='product')
router.register(r'product-templates', ProductTemplateViewSet, basename='producttemplate')
router.register(r'stock-moves', StockMoveViewSet, basename='stockmove')
router.register(r'stock-pickings', StockPickingViewSet, basename='stockpicking')

# MRP Module (placeholder - will be implemented later)
router.register(r'manufacturing-orders', ProductionViewSet, basename='production')
router.register(r'boms', BomViewSet, basename='bom')
router.register(r'workcenters', WorkcenterViewSet, basename='workcenter')
router.register(r'work-orders', WorkorderViewSet, basename='workorder')

urlpatterns = [
    # JWT Authentication
    path('auth/login/', LoginView.as_view(), name='login'),  # Custom login with user info
    path('auth/token/', TokenObtainPairView.as_view(), name='token_obtain_pair'),  # Standard JWT
    path('auth/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('auth/token/verify/', TokenVerifyView.as_view(), name='token_verify'),
    path('auth/register/', RegisterView.as_view(), name='register'),
    path('auth/profile/', ProfileView.as_view(), name='profile'),
    
    # API Routes
    path('', include(router.urls)),
]
