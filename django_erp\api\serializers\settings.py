from rest_framework import serializers
from core.models import Company, Currency, Country, CountryState, FiscalYear


class CountrySerializer(serializers.ModelSerializer):
    """Serializer for Country model"""
    
    class Meta:
        model = Country
        fields = ['id', 'name', 'code', 'phone_code', 'active', 'create_date', 'write_date']
        read_only_fields = ['id', 'create_date', 'write_date']


class CountryStateSerializer(serializers.ModelSerializer):
    """Serializer for Country State/Province model"""
    country_name = serializers.CharField(source='country.name', read_only=True)
    
    class Meta:
        model = CountryState
        fields = ['id', 'name', 'code', 'country', 'country_name', 'active', 'create_date', 'write_date']
        read_only_fields = ['id', 'create_date', 'write_date']


class CurrencySerializer(serializers.ModelSerializer):
    """Serializer for Currency model"""
    rate = serializers.SerializerMethodField()
    is_base = serializers.SerializerMethodField()
    position = serializers.SerializerMethodField()
    rounding = serializers.SerializerMethodField()
    
    class Meta:
        model = Currency
        fields = ['id', 'name', 'symbol', 'decimal_places', 'full_name', 'active', 
                 'rate', 'is_base', 'position', 'rounding', 'create_date', 'write_date']
        read_only_fields = ['id', 'create_date', 'write_date']
    
    def get_rate(self, obj):
        """Get exchange rate relative to base currency"""
        # For now, return mock rates - implement actual rate logic later
        rate_map = {
            'USD': 1.0,
            'EUR': 0.85,
            'GBP': 0.73,
            'CAD': 1.35,
            'JPY': 110.0,
            'AUD': 1.45,
            'INR': 83.0,
            'CNY': 7.2
        }
        return rate_map.get(obj.name, 1.0)
    
    def get_is_base(self, obj):
        """Check if this is the base currency"""
        # For now, USD is base currency - implement actual base currency logic later
        return obj.name == 'USD'
    
    def get_position(self, obj):
        """Get symbol position (before/after amount)"""
        # Most currencies have symbol before, except EUR
        return 'after' if obj.name == 'EUR' else 'before'
    
    def get_rounding(self, obj):
        """Get rounding precision"""
        return 1.0 if obj.decimal_places == 0 else 0.01


class CompanySerializer(serializers.ModelSerializer):
    """Serializer for Company model"""
    country_name = serializers.CharField(source='country.name', read_only=True)
    currency_name = serializers.CharField(source='currency.name', read_only=True)
    currency_symbol = serializers.CharField(source='currency.symbol', read_only=True)
    state_name = serializers.CharField(source='state.name', read_only=True)

    # Add fields that don't exist in the model as SerializerMethodFields
    mobile = serializers.SerializerMethodField()
    company_registry = serializers.SerializerMethodField()
    logo = serializers.SerializerMethodField()
    favicon = serializers.SerializerMethodField()

    class Meta:
        model = Company
        fields = ['id', 'name', 'code', 'email', 'phone', 'mobile', 'website',
                 'vat', 'company_registry', 'street', 'street2', 'city', 'state',
                 'state_name', 'zip', 'country', 'country_name', 'currency', 'currency_name',
                 'currency_symbol', 'logo', 'favicon', 'active', 'create_date', 'write_date']
        read_only_fields = ['id', 'create_date', 'write_date']

    def get_mobile(self, obj):
        """Get mobile phone - placeholder for now"""
        return ""

    def get_company_registry(self, obj):
        """Get company registry - placeholder for now"""
        return ""

    def get_logo(self, obj):
        """Get company logo - placeholder for now"""
        return None

    def get_favicon(self, obj):
        """Get company favicon - placeholder for now"""
        return None

    def validate_email(self, value):
        """Validate email format"""
        if value and '@' not in value:
            raise serializers.ValidationError("Enter a valid email address.")
        return value

    def validate_website(self, value):
        """Validate website URL"""
        if value and not (value.startswith('http://') or value.startswith('https://')):
            value = f'https://{value}'
        return value


class CompanyUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating company information"""

    class Meta:
        model = Company
        fields = ['name', 'email', 'phone', 'website', 'vat',
                 'street', 'street2', 'city',
                 'zip', 'country', 'currency']

    def validate_email(self, value):
        """Validate email format"""
        if value and '@' not in value:
            raise serializers.ValidationError("Enter a valid email address.")
        return value


class FiscalYearSerializer(serializers.ModelSerializer):
    """Serializer for Fiscal Year model"""
    company_name = serializers.CharField(source='company.name', read_only=True)
    is_current = serializers.BooleanField(read_only=True)

    class Meta:
        model = FiscalYear
        fields = ['id', 'name', 'code', 'date_start', 'date_end', 'company',
                 'company_name', 'state', 'is_current', 'active', 'create_date', 'write_date']
        read_only_fields = ['id', 'company', 'company_name', 'is_current', 'create_date', 'write_date']

    def validate(self, data):
        """Validate fiscal year dates"""
        if 'date_start' in data and 'date_end' in data:
            if data['date_start'] >= data['date_end']:
                raise serializers.ValidationError("Start date must be before end date")
        return data


# Settings Dashboard Summary Serializer
class SettingsDashboardSerializer(serializers.Serializer):
    """Serializer for settings dashboard summary"""
    company_count = serializers.IntegerField()
    currency_count = serializers.IntegerField()
    country_count = serializers.IntegerField()
    active_currencies = serializers.IntegerField()
    base_currency = serializers.CharField()
    
    # Company info
    current_company = CompanySerializer()
    
    # Recent changes
    recent_changes = serializers.ListField(
        child=serializers.DictField(), 
        read_only=True
    )
