#!/usr/bin/env python3
"""
<PERSON><PERSON>t to create comprehensive test data for the Django ERP system.
This will test all major modules and functionality.
"""

import os
import sys
import django
from decimal import Decimal
from datetime import date, datetime, timedelta

# Setup Django
sys.path.insert(0, os.path.join(os.getcwd(), 'django_erp'))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'django_erp.settings')
django.setup()

from django.contrib.auth.models import User
from core.models import Company, Partner, Currency, Country, CountryState
from accounting.models import AccountGroup, AccountAccount, AccountJournal, AccountMove, AccountMoveLine
from sales.models import SaleOrder, SaleOrderLine, SalesTeam
from purchases.models import PurchaseOrder, PurchaseOrderLine
from inventory.models import ProductTemplate, Product, ProductCategory
from hr.models import HrEmployee, HrDepartment, HrJob
from crm.models import CrmLead, CrmTeam

def create_test_data():
    print("🚀 Creating comprehensive test data for Django ERP...")

    # Get admin user for create_uid and write_uid
    admin_user = User.objects.get(username='admin')

    # 1. Create Currency and Country
    print("\n1️⃣ Creating Currency and Country...")
    usd, created = Currency.objects.get_or_create(
        name='USD',
        defaults={
            'symbol': '$',
            'decimal_places': 2,
            'full_name': 'US Dollar',
            'position': 'before',
            'create_uid': admin_user,
            'write_uid': admin_user
        }
    )
    print(f"   ✓ Currency: {usd.name} ({usd.symbol})")
    
    usa, created = Country.objects.get_or_create(
        code='US',
        defaults={
            'name': 'United States',
            'phone_code': 1,
            'create_uid': admin_user,
            'write_uid': admin_user
        }
    )
    print(f"   ✓ Country: {usa.name}")
    
    # 2. Create Company
    print("\n2️⃣ Creating Company...")
    company, created = Company.objects.get_or_create(
        code='DEMO',
        defaults={
            'name': 'Demo ERP Company',
            'currency': usd,
            'email': '<EMAIL>',
            'phone': '******-0123',
            'website': 'https://demo-erp.com',
            'vat': 'US123456789',
            'street': '123 Business Ave',
            'city': 'New York',
            'zip': '10001',
            'country': usa
        }
    )
    print(f"   ✓ Company: {company.name}")
    
    # 3. Create Chart of Accounts (COA)
    print("\n3️⃣ Creating Chart of Accounts...")
    
    # Account Groups
    asset_group, _ = AccountGroup.objects.get_or_create(
        name='Assets',
        code_prefix_start='1000',
        code_prefix_end='1999',
        company=company
    )
    
    liability_group, _ = AccountGroup.objects.get_or_create(
        name='Liabilities',
        code_prefix_start='2000',
        code_prefix_end='2999',
        company=company
    )
    
    equity_group, _ = AccountGroup.objects.get_or_create(
        name='Equity',
        code_prefix_start='3000',
        code_prefix_end='3999',
        company=company
    )
    
    income_group, _ = AccountGroup.objects.get_or_create(
        name='Income',
        code_prefix_start='4000',
        code_prefix_end='4999',
        company=company
    )
    
    expense_group, _ = AccountGroup.objects.get_or_create(
        name='Expenses',
        code_prefix_start='5000',
        code_prefix_end='5999',
        company=company
    )
    
    # Main Accounts
    accounts_data = [
        ('1000', 'Cash', 'asset_cash', asset_group),
        ('1100', 'Bank Account', 'asset_cash', asset_group),
        ('1200', 'Accounts Receivable', 'asset_receivable', asset_group, True),
        ('1300', 'Inventory', 'asset_current', asset_group),
        ('1500', 'Equipment', 'asset_fixed', asset_group),
        ('2000', 'Accounts Payable', 'liability_payable', liability_group, True),
        ('2100', 'Credit Card', 'liability_credit_card', liability_group),
        ('3000', 'Owner Equity', 'equity', equity_group),
        ('4000', 'Sales Revenue', 'income', income_group),
        ('4100', 'Service Revenue', 'income', income_group),
        ('5000', 'Cost of Goods Sold', 'expense_direct_cost', expense_group),
        ('5100', 'Office Expenses', 'expense', expense_group),
        ('5200', 'Marketing Expenses', 'expense', expense_group),
        ('5300', 'Salary Expenses', 'expense', expense_group),
    ]
    
    accounts = {}
    for account_data in accounts_data:
        code, name, account_type, group = account_data[:4]
        reconcile = account_data[4] if len(account_data) > 4 else False
        
        account, created = AccountAccount.objects.get_or_create(
            code=code,
            company=company,
            defaults={
                'name': name,
                'account_type': account_type,
                'group': group,
                'reconcile': reconcile,
                'currency': usd
            }
        )
        accounts[code] = account
        print(f"   ✓ Account: {code} - {name}")
    
    # 4. Create Journals
    print("\n4️⃣ Creating Journals...")
    
    bank_journal, _ = AccountJournal.objects.get_or_create(
        code='BANK',
        company=company,
        defaults={
            'name': 'Bank Journal',
            'type': 'bank',
            'default_account': accounts['1100'],
            'currency': usd
        }
    )
    
    sales_journal, _ = AccountJournal.objects.get_or_create(
        code='SALE',
        company=company,
        defaults={
            'name': 'Sales Journal',
            'type': 'sale',
            'default_account': accounts['4000'],
            'currency': usd
        }
    )
    
    purchase_journal, _ = AccountJournal.objects.get_or_create(
        code='BILL',
        company=company,
        defaults={
            'name': 'Purchase Journal',
            'type': 'purchase',
            'default_account': accounts['5000'],
            'currency': usd
        }
    )
    
    print(f"   ✓ Journals: {bank_journal.name}, {sales_journal.name}, {purchase_journal.name}")
    
    # 5. Create Customers and Vendors
    print("\n5️⃣ Creating Customers and Vendors...")
    
    customers_data = [
        ('ABC Corp', 'customer', '<EMAIL>', '******-1001'),
        ('XYZ Ltd', 'customer', '<EMAIL>', '******-1002'),
        ('Tech Solutions Inc', 'customer', '<EMAIL>', '******-1003'),
    ]
    
    vendors_data = [
        ('Office Supplies Co', 'vendor', '<EMAIL>', '******-2001'),
        ('Equipment Rental LLC', 'vendor', '<EMAIL>', '******-2002'),
        ('Software Services Inc', 'vendor', '<EMAIL>', '******-2003'),
    ]
    
    customers = {}
    vendors = {}
    
    for name, partner_type, email, phone in customers_data:
        partner, created = Partner.objects.get_or_create(
            name=name,
            defaults={
                'is_company': True,
                'customer_rank': 1,
                'supplier_rank': 0,
                'email': email,
                'phone': phone,
                'street': f'{name} Street',
                'city': 'New York',
                'zip': '10001',
                'country': usa
            }
        )
        customers[name] = partner
        print(f"   ✓ Customer: {name}")
    
    for name, partner_type, email, phone in vendors_data:
        partner, created = Partner.objects.get_or_create(
            name=name,
            defaults={
                'is_company': True,
                'customer_rank': 0,
                'supplier_rank': 1,
                'email': email,
                'phone': phone,
                'street': f'{name} Street',
                'city': 'New York',
                'zip': '10001',
                'country': usa
            }
        )
        vendors[name] = partner
        print(f"   ✓ Vendor: {name}")
    
    print(f"\n✅ Test data creation completed successfully!")
    print(f"📊 Summary:")
    print(f"   - 1 Company: {company.name}")
    print(f"   - 1 Currency: {usd.name}")
    print(f"   - 5 Account Groups")
    print(f"   - {len(accounts)} Chart of Accounts")
    print(f"   - 3 Journals")
    print(f"   - {len(customers)} Customers")
    print(f"   - {len(vendors)} Vendors")
    print(f"\n🌐 Access the admin interface at: http://localhost:8000/admin/")
    print(f"   Username: admin")
    print(f"   Password: admin")

if __name__ == "__main__":
    create_test_data()
