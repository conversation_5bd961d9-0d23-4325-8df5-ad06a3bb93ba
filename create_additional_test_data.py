from django.contrib.auth.models import User
from core.models import Company, Partner
from inventory.models import ProductTemplate, Product, ProductCategory
from hr.models import HrEmployee, HrDepartment, HrJob
from sales.models import SalesTeam
from crm.models import CrmTeam

# Get admin user and company
admin_user = User.objects.get(username='admin')
company = Company.objects.get(code='DEMO')

print("Creating additional test data...")

# Create Product Categories
print("\n📦 Creating Product Categories...")
categories_data = [
    ('Services', 'Professional Services'),
    ('Software', 'Software Products'),
    ('Hardware', 'Computer Hardware'),
    ('Office Supplies', 'Office and Stationery'),
]

for name, description in categories_data:
    category, created = ProductCategory.objects.get_or_create(
        name=name,
        defaults={
            'create_uid': admin_user,
            'write_uid': admin_user
        }
    )
    print(f"   ✓ Category: {name} ({'created' if created else 'exists'})")

# Create Product Templates
print("\n🛍️ Creating Products...")
products_data = [
    ('Consulting Service', 'service', 'Professional consulting services', 150.00),
    ('Software License', 'service', 'Annual software license', 1200.00),
    ('Laptop Computer', 'product', 'Business laptop computer', 899.00),
    ('Office Chair', 'product', 'Ergonomic office chair', 299.00),
    ('Printer Paper', 'product', 'A4 printer paper - 500 sheets', 12.99),
]

for name, product_type, description, price in products_data:
    # Get appropriate category
    if 'Service' in name or 'Consulting' in name:
        category = ProductCategory.objects.get(name='Services')
    elif 'Software' in name or 'License' in name:
        category = ProductCategory.objects.get(name='Software')
    elif 'Laptop' in name or 'Computer' in name:
        category = ProductCategory.objects.get(name='Hardware')
    else:
        category = ProductCategory.objects.get(name='Office Supplies')
    
    template, created = ProductTemplate.objects.get_or_create(
        name=name,
        defaults={
            'detailed_type': product_type,
            'description': description,
            'list_price': price,
            'categ': category,
            'company': company,
            'create_uid': admin_user,
            'write_uid': admin_user
        }
    )
    print(f"   ✓ Product: {name} - ${price} ({'created' if created else 'exists'})")

# Create HR Departments
print("\n🏢 Creating HR Departments...")
departments_data = [
    ('Sales', 'Sales Department'),
    ('IT', 'Information Technology'),
    ('Finance', 'Finance and Accounting'),
    ('HR', 'Human Resources'),
]

for name, description in departments_data:
    dept, created = HrDepartment.objects.get_or_create(
        name=name,
        company=company,
        defaults={
            'create_uid': admin_user,
            'write_uid': admin_user
        }
    )
    print(f"   ✓ Department: {name} ({'created' if created else 'exists'})")

# Create Job Positions
print("\n💼 Creating Job Positions...")
jobs_data = [
    ('Sales Manager', 'Sales', 'Manage sales team and operations'),
    ('Software Developer', 'IT', 'Develop and maintain software applications'),
    ('Accountant', 'Finance', 'Handle accounting and financial records'),
    ('HR Specialist', 'HR', 'Manage human resources activities'),
]

for title, dept_name, description in jobs_data:
    department = HrDepartment.objects.get(name=dept_name, company=company)
    job, created = HrJob.objects.get_or_create(
        name=title,
        department=department,
        company=company,
        defaults={
            'description': description,
            'state': 'open',
            'no_of_recruitment': 1,
            'create_uid': admin_user,
            'write_uid': admin_user
        }
    )
    print(f"   ✓ Job: {title} in {dept_name} ({'created' if created else 'exists'})")

# Create Employees
print("\n👥 Creating Employees...")
employees_data = [
    ('John Smith', 'Sales Manager', '<EMAIL>', '******-3001'),
    ('Sarah Johnson', 'Software Developer', '<EMAIL>', '******-3002'),
    ('Mike Davis', 'Accountant', '<EMAIL>', '******-3003'),
    ('Lisa Wilson', 'HR Specialist', '<EMAIL>', '******-3004'),
]

for name, job_title, email, phone in employees_data:
    job = HrJob.objects.get(name=job_title, company=company)
    employee, created = HrEmployee.objects.get_or_create(
        name=name,
        company=company,
        defaults={
            'job': job,
            'department': job.department,
            'work_email': email,
            'work_phone': phone,
            'employee_number': f'EMP{len(employees_data):03d}',
            'create_uid': admin_user,
            'write_uid': admin_user
        }
    )
    print(f"   ✓ Employee: {name} - {job_title} ({'created' if created else 'exists'})")

# Create Sales Team
print("\n🎯 Creating Sales Team...")
sales_team, created = SalesTeam.objects.get_or_create(
    name='Main Sales Team',
    company=company,
    defaults={
        'create_uid': admin_user,
        'write_uid': admin_user
    }
)
print(f"   ✓ Sales Team: {sales_team.name} ({'created' if created else 'exists'})")

# Create CRM Team
print("\n📞 Creating CRM Team...")
crm_team, created = CrmTeam.objects.get_or_create(
    name='Lead Generation Team',
    company=company,
    defaults={
        'create_uid': admin_user,
        'write_uid': admin_user
    }
)
print(f"   ✓ CRM Team: {crm_team.name} ({'created' if created else 'exists'})")

print("\n✅ Additional test data created successfully!")
print("\n📊 Summary of created data:")
print(f"   - {len(categories_data)} Product Categories")
print(f"   - {len(products_data)} Products")
print(f"   - {len(departments_data)} HR Departments")
print(f"   - {len(jobs_data)} Job Positions")
print(f"   - {len(employees_data)} Employees")
print(f"   - 1 Sales Team")
print(f"   - 1 CRM Team")
print(f"\n🌐 Access admin interface: http://localhost:8000/admin/")
print(f"   Username: admin | Password: admin")
