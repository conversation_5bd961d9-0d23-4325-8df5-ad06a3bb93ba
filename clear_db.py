#!/usr/bin/env python3
"""
Script to clear all tables from the Django database.
"""

import os
import sys

# Add the django_erp directory to the Python path
sys.path.insert(0, os.path.join(os.getcwd(), 'django_erp'))

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')

import django
django.setup()

from django.db import connection

def clear_database():
    """Drop all tables from the database."""
    with connection.cursor() as cursor:
        # Get all table names
        cursor.execute("""
            SELECT tablename FROM pg_tables 
            WHERE schemaname = 'public' AND tablename NOT LIKE 'pg_%'
        """)
        tables = [row[0] for row in cursor.fetchall()]
        
        if tables:
            # Drop all tables
            table_list = ', '.join(f'"{table}"' for table in tables)
            cursor.execute(f'DROP TABLE IF EXISTS {table_list} CASCADE')
            print(f'Dropped {len(tables)} tables: {", ".join(tables)}')
        else:
            print('No tables to drop')

    print('Database cleared successfully')

if __name__ == "__main__":
    clear_database()
