from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.db.models import Sum, Q
from django.utils import timezone
from datetime import datetime, timedelta

from accounting.models import (
    AccountGroup, AccountAccount, AccountJournal, AccountMove,
    AccountMoveLine, AccountPayment, AccountTax
)
from ..serializers.accounting import (
    AccountGroupSerializer, AccountAccountSerializer, AccountJournalSerializer,
    AccountMoveSerializer, AccountPaymentSerializer, AccountTaxSerializer,
    AccountingDashboardSerializer
)

class AccountViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Chart of Accounts
    """
    serializer_class = AccountAccountSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter accounts by user's company access"""
        user = self.request.user
        # For now, return all accounts - later add company filtering based on user roles
        return AccountAccount.objects.select_related('group', 'company', 'currency').all()

    def perform_create(self, serializer):
        """Set create_uid and write_uid when creating account"""
        serializer.save(
            create_uid=self.request.user,
            write_uid=self.request.user
        )

    def perform_update(self, serializer):
        """Set write_uid when updating account"""
        serializer.save(write_uid=self.request.user)

    @action(detail=False, methods=['get'])
    def dashboard_stats(self, request):
        """Get accounting dashboard statistics"""
        # Calculate dashboard metrics
        accounts = self.get_queryset()

        # Mock calculations for now - replace with real calculations later
        stats = {
            'total_receivables': 125000.00,
            'total_payables': 85000.00,
            'bank_balance': 245000.00,
            'monthly_revenue': 180000.00,
            'pending_invoices': 12,
            'overdue_invoices': 3,
            'recent_activities': [
                {
                    'type': 'invoice',
                    'title': 'Invoice INV-2024-001 created',
                    'description': 'Customer: ABC Corp - Amount: $15,000',
                    'time': '2 hours ago',
                    'icon': 'fas fa-file-invoice'
                },
                {
                    'type': 'payment',
                    'title': 'Payment received',
                    'description': 'From: XYZ Ltd - Amount: $8,500',
                    'time': '4 hours ago',
                    'icon': 'fas fa-credit-card'
                },
                {
                    'type': 'journal',
                    'title': 'Journal entry posted',
                    'description': 'Entry: JE-2024-045 - Office Expenses',
                    'time': '1 day ago',
                    'icon': 'fas fa-edit'
                }
            ]
        }

        serializer = AccountingDashboardSerializer(stats)
        return Response(serializer.data)

class JournalViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Account Journals
    """
    serializer_class = AccountJournalSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter journals by user's company access"""
        return AccountJournal.objects.select_related('company', 'default_account', 'currency').all()

    def perform_create(self, serializer):
        """Set create_uid and write_uid when creating journal"""
        serializer.save(
            create_uid=self.request.user,
            write_uid=self.request.user
        )

    def perform_update(self, serializer):
        """Set write_uid when updating journal"""
        serializer.save(write_uid=self.request.user)

class MoveViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Account Moves (Journal Entries)
    """
    serializer_class = AccountMoveSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter moves by user's company access"""
        return AccountMove.objects.select_related(
            'journal', 'partner', 'company'
        ).prefetch_related('line_ids').all()

    def perform_create(self, serializer):
        """Set create_uid and write_uid when creating move"""
        serializer.save(
            create_uid=self.request.user,
            write_uid=self.request.user
        )

    def perform_update(self, serializer):
        """Set write_uid when updating move"""
        serializer.save(write_uid=self.request.user)

class AccountGroupViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Account Groups
    """
    serializer_class = AccountGroupSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter account groups by user's company access"""
        return AccountGroup.objects.select_related('company').all()

    def perform_create(self, serializer):
        """Set create_uid and write_uid when creating account group"""
        serializer.save(
            create_uid=self.request.user,
            write_uid=self.request.user
        )

    def perform_update(self, serializer):
        """Set write_uid when updating account group"""
        serializer.save(write_uid=self.request.user)


# PaymentViewSet will be implemented later when needed
