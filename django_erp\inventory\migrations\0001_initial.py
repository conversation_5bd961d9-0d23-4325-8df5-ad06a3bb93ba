# Generated by Django 4.2.21 on 2025-07-21 16:30

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='IrSequence',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=255)),
                ('code', models.CharField(max_length=100)),
                ('prefix', models.CharField(blank=True, max_length=50)),
                ('suffix', models.CharField(blank=True, max_length=50)),
                ('number_next', models.IntegerField(default=1)),
                ('number_increment', models.IntegerField(default=1)),
                ('padding', models.IntegerField(default=4)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('default_code', models.CharField(blank=True, help_text='Internal Reference', max_length=100)),
                ('barcode', models.CharField(blank=True, max_length=100, null=True, unique=True)),
                ('qty_available', models.DecimalField(decimal_places=4, default=0.0, help_text='Quantity On Hand', max_digits=16)),
                ('virtual_available', models.DecimalField(decimal_places=4, default=0.0, help_text='Forecasted Quantity', max_digits=16)),
                ('incoming_qty', models.DecimalField(decimal_places=4, default=0.0, help_text='Incoming Quantity', max_digits=16)),
                ('outgoing_qty', models.DecimalField(decimal_places=4, default=0.0, help_text='Outgoing Quantity', max_digits=16)),
                ('active', models.BooleanField(default=True)),
            ],
        ),
        migrations.CreateModel(
            name='ProductCategory',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(db_index=True, max_length=255)),
                ('complete_name', models.CharField(blank=True, max_length=500)),
                ('parent_path', models.CharField(blank=True, db_index=True, max_length=500)),
                ('property_valuation', models.CharField(choices=[('manual_periodic', 'Manual'), ('real_time', 'Automated')], default='manual_periodic', max_length=20)),
                ('property_cost_method', models.CharField(choices=[('standard', 'Standard Price'), ('fifo', 'First In First Out (FIFO)'), ('average', 'Average Cost (AVCO)')], default='standard', max_length=20)),
            ],
            options={
                'verbose_name_plural': 'Product Categories',
            },
        ),
        migrations.CreateModel(
            name='ProductPackaging',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(help_text='Packaging Name', max_length=255)),
                ('qty', models.FloatField(default=1.0, help_text='Quantity per Package')),
                ('barcode', models.CharField(blank=True, help_text='Barcode', max_length=255)),
                ('sales', models.BooleanField(default=False, help_text='Available in Sales')),
                ('purchase', models.BooleanField(default=False, help_text='Available in Purchase')),
            ],
        ),
        migrations.CreateModel(
            name='ProductTemplate',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(db_index=True, max_length=255)),
                ('default_code', models.CharField(blank=True, help_text='Internal Reference', max_length=100)),
                ('barcode', models.CharField(blank=True, max_length=100, null=True, unique=True)),
                ('detailed_type', models.CharField(choices=[('consu', 'Consumable'), ('service', 'Service'), ('product', 'Storable Product')], default='consu', max_length=20)),
                ('list_price', models.DecimalField(decimal_places=4, default=0.0, help_text='Sales Price', max_digits=20)),
                ('standard_price', models.DecimalField(decimal_places=4, default=0.0, help_text='Cost Price', max_digits=20)),
                ('tracking', models.CharField(choices=[('serial', 'By Unique Serial Number'), ('lot', 'By Lots'), ('none', 'No Tracking')], default='none', max_length=20)),
                ('description', models.TextField(blank=True, help_text='Internal Notes')),
                ('description_sale', models.TextField(blank=True, help_text='Sales Description')),
                ('description_purchase', models.TextField(blank=True, help_text='Purchase Description')),
                ('description_picking', models.TextField(blank=True, help_text='Description on Picking')),
                ('description_pickingout', models.TextField(blank=True, help_text='Description on Delivery Orders')),
                ('description_pickingin', models.TextField(blank=True, help_text='Description on Receptions')),
                ('qty_available', models.DecimalField(decimal_places=4, default=0.0, help_text='Quantity On Hand', max_digits=16)),
                ('virtual_available', models.DecimalField(decimal_places=4, default=0.0, help_text='Forecasted Quantity', max_digits=16)),
                ('incoming_qty', models.DecimalField(decimal_places=4, default=0.0, help_text='Incoming Quantity', max_digits=16)),
                ('outgoing_qty', models.DecimalField(decimal_places=4, default=0.0, help_text='Outgoing Quantity', max_digits=16)),
                ('active', models.BooleanField(default=True)),
            ],
        ),
        migrations.CreateModel(
            name='StockInventory',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=255)),
                ('date', models.DateTimeField(default=django.utils.timezone.now)),
                ('state', models.CharField(choices=[('draft', 'Draft'), ('cancel', 'Cancelled'), ('confirm', 'In Progress'), ('done', 'Validated')], default='draft', max_length=20)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='StockInventoryLine',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('theoretical_qty', models.DecimalField(decimal_places=4, default=0.0, help_text='Theoretical Quantity', max_digits=16)),
                ('product_qty', models.DecimalField(decimal_places=4, default=0.0, help_text='Real Quantity', max_digits=16)),
                ('difference_qty', models.DecimalField(decimal_places=4, default=0.0, help_text='Difference', max_digits=16)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='StockLocation',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('complete_name', models.CharField(blank=True, max_length=500)),
                ('active', models.BooleanField(default=True)),
                ('usage', models.CharField(choices=[('supplier', 'Vendor Location'), ('view', 'View'), ('internal', 'Internal Location'), ('customer', 'Customer Location'), ('inventory', 'Inventory Loss'), ('production', 'Production'), ('transit', 'Transit Location')], db_index=True, default='internal', max_length=20)),
                ('parent_path', models.CharField(blank=True, db_index=True, max_length=500)),
                ('posx', models.IntegerField(default=0, help_text='Corridor (X)')),
                ('posy', models.IntegerField(default=0, help_text='Shelves (Y)')),
                ('posz', models.IntegerField(default=0, help_text='Height (Z)')),
                ('barcode', models.CharField(blank=True, max_length=100)),
                ('comment', models.TextField(blank=True, help_text='Additional Information')),
            ],
        ),
        migrations.CreateModel(
            name='StockLot',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(help_text='Lot/Serial Number', max_length=255)),
                ('ref', models.CharField(blank=True, help_text='Internal Reference', max_length=255)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('use_date', models.DateField(blank=True, help_text='Best before Date', null=True)),
                ('removal_date', models.DateField(blank=True, help_text='Removal Date', null=True)),
                ('alert_date', models.DateField(blank=True, help_text='Alert Date', null=True)),
                ('note', models.TextField(blank=True)),
            ],
        ),
        migrations.CreateModel(
            name='StockMove',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(help_text='Description', max_length=255)),
                ('sequence', models.IntegerField(default=10)),
                ('product_uom_qty', models.DecimalField(decimal_places=4, default=1.0, help_text='Demand', max_digits=16)),
                ('product_qty', models.DecimalField(decimal_places=4, default=1.0, help_text='Real Quantity', max_digits=16)),
                ('quantity_done', models.DecimalField(decimal_places=4, default=0.0, help_text='Quantity Done', max_digits=16)),
                ('state', models.CharField(choices=[('draft', 'New'), ('waiting', 'Waiting Another Move'), ('confirmed', 'Waiting Availability'), ('partially_available', 'Partially Available'), ('assigned', 'Available'), ('done', 'Done'), ('cancel', 'Cancelled')], db_index=True, default='draft', max_length=20)),
                ('date', models.DateTimeField(default=django.utils.timezone.now, help_text='Date')),
                ('date_deadline', models.DateTimeField(blank=True, help_text='Deadline', null=True)),
                ('origin', models.CharField(blank=True, max_length=255)),
                ('reference', models.CharField(blank=True, max_length=255)),
                ('description_picking', models.TextField(blank=True)),
                ('priority', models.CharField(choices=[('0', 'Normal'), ('1', 'Urgent')], default='0', max_length=1)),
                ('is_done', models.BooleanField(default=False, help_text='Is Done (for MRP)')),
                ('unit_factor', models.DecimalField(decimal_places=6, default=1.0, help_text='Unit Factor (for MRP)', max_digits=12)),
            ],
        ),
        migrations.CreateModel(
            name='StockPackageType',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=255)),
                ('sequence', models.IntegerField(default=1)),
                ('height', models.IntegerField(default=0, help_text='Height in mm')),
                ('width', models.IntegerField(default=0, help_text='Width in mm')),
                ('packaging_length', models.IntegerField(default=0, help_text='Length in mm')),
                ('max_weight', models.DecimalField(decimal_places=4, default=0.0, help_text='Max Weight in kg', max_digits=16)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='StockPicking',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(default='New', max_length=255)),
                ('origin', models.CharField(blank=True, help_text='Source Document', max_length=255)),
                ('state', models.CharField(choices=[('draft', 'Draft'), ('waiting', 'Waiting Another Operation'), ('confirmed', 'Waiting'), ('assigned', 'Ready'), ('done', 'Done'), ('cancel', 'Cancelled')], db_index=True, default='draft', max_length=20)),
                ('move_type', models.CharField(choices=[('direct', 'As soon as possible'), ('one', 'When all products are ready')], default='direct', max_length=20)),
                ('scheduled_date', models.DateTimeField(default=django.utils.timezone.now, help_text='Scheduled Date')),
                ('date_done', models.DateTimeField(blank=True, help_text='Date Done', null=True)),
                ('priority', models.CharField(choices=[('0', 'Normal'), ('1', 'Urgent')], default='0', max_length=1)),
                ('note', models.TextField(blank=True)),
            ],
        ),
        migrations.CreateModel(
            name='StockQuant',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('quantity', models.DecimalField(decimal_places=4, default=0.0, help_text='Quantity', max_digits=16)),
                ('reserved_quantity', models.DecimalField(decimal_places=4, default=0.0, help_text='Reserved Quantity', max_digits=16)),
                ('available_quantity', models.DecimalField(decimal_places=4, default=0.0, help_text='Available Quantity', max_digits=16)),
                ('in_date', models.DateTimeField(default=django.utils.timezone.now, help_text='Incoming Date')),
                ('value', models.DecimalField(decimal_places=2, default=0.0, help_text='Inventory Value', max_digits=20)),
            ],
        ),
        migrations.CreateModel(
            name='StockQuantPackage',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(help_text='Package Reference', max_length=255)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='StockRemovalStrategy',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=255)),
                ('method', models.CharField(choices=[('fifo', 'First In First Out (FIFO)'), ('lifo', 'Last In First Out (LIFO)'), ('closest', 'Closest Location'), ('fefo', 'First Expiry First Out (FEFO)')], default='fifo', max_length=20)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='StockRule',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('active', models.BooleanField(default=True)),
                ('action', models.CharField(choices=[('pull', 'Pull From'), ('push', 'Push To'), ('pull_push', 'Pull & Push'), ('buy', 'Buy'), ('manufacture', 'Manufacture')], max_length=20)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('location', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='inventory.stocklocation')),
                ('location_src', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='rule_src', to='inventory.stocklocation')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
