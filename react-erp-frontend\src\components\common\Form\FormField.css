/* Reusable Odoo Form Field Styles */

.odoo-form-field {
  display: flex;
  flex-direction: column;
  margin-bottom: var(--odoo-spacing-md);
}

.odoo-form-label {
  font-size: var(--odoo-font-size-sm);
  font-weight: var(--odoo-font-weight-medium);
  color: var(--odoo-text-secondary);
  margin-bottom: var(--odoo-spacing-xs);
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.odoo-form-required {
  color: var(--odoo-danger);
  font-weight: bold;
}

.odoo-form-input-wrapper {
  position: relative;
}

/* Base Input Styles */
.odoo-form-input {
  width: 100%;
  font-family: var(--odoo-font-family-sans-serif);
  font-weight: 300;
  color: var(--odoo-dark-gray);
  background: transparent;
  transition: all 0.15s ease-in-out;
  outline: none;
}

/* Underline Variant (Default - matches Odoo) */
.odoo-form-input--underline {
  border: none;
  border-bottom: 1px solid transparent;
  border-radius: 0;
  padding: 0.25rem 0;
  box-shadow: none;
}

.odoo-form-input--underline:hover,
.odoo-form-input--underline:focus {
  border-bottom-color: var(--odoo-primary);
  background: transparent;
}

/* Bordered Variant */
.odoo-form-input--bordered {
  border: 1px solid var(--odoo-border-light);
  border-radius: var(--odoo-border-radius);
  padding: var(--odoo-spacing-sm);
}

.odoo-form-input--bordered:hover,
.odoo-form-input--bordered:focus {
  border-color: var(--odoo-primary);
}

/* Filled Variant */
.odoo-form-input--filled {
  border: 1px solid var(--odoo-border-light);
  border-radius: var(--odoo-border-radius);
  background-color: var(--odoo-bg-light);
  padding: var(--odoo-spacing-sm);
}

.odoo-form-input--filled:hover,
.odoo-form-input--filled:focus {
  border-color: var(--odoo-primary);
  background-color: var(--odoo-white);
}

/* Size Variants */
.odoo-form-input--small {
  font-size: 0.9rem;
  padding: 0.2rem 0;
}

.odoo-form-input--medium {
  font-size: 1.3rem;
  padding: 0.25rem 0;
}

.odoo-form-input--large {
  font-size: 1.5rem;
  padding: 0.3rem 0;
}

/* Special styling for code/number fields */
.odoo-form-input[name="code"],
.odoo-form-input[type="number"] {
  font-family: var(--odoo-font-family-monospace);
  font-variant-numeric: tabular-nums;
  font-size: 1.2rem;
}

/* Error State */
.odoo-form-field--error .odoo-form-input {
  border-bottom-color: var(--odoo-danger);
}

.odoo-form-field--error .odoo-form-input:hover,
.odoo-form-field--error .odoo-form-input:focus {
  border-bottom-color: var(--odoo-danger);
}

.odoo-form-error {
  color: var(--odoo-danger);
  font-size: var(--odoo-font-size-sm);
  margin-top: var(--odoo-spacing-xs);
}

/* Disabled State */
.odoo-form-field--disabled .odoo-form-input {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: var(--odoo-bg-light);
}

/* Select Dropdown Styling */
.odoo-form-input select {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px 12px;
  padding-right: 2.5rem;
}

/* Textarea Styling */
.odoo-form-input textarea {
  resize: vertical;
  min-height: 100px;
}

/* Responsive */
@media (max-width: 768px) {
  .odoo-form-field {
    margin-bottom: var(--odoo-spacing-sm);
  }
  
  .odoo-form-input--large {
    font-size: 1.2rem;
  }
  
  .odoo-form-input--medium {
    font-size: 1.1rem;
  }
}
