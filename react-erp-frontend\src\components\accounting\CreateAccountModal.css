/* Create Account Modal - Odoo Style */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--odoo-spacing-lg);
}

.modal-container {
  background: var(--odoo-white);
  border-radius: var(--odoo-border-radius-lg);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.create-account-modal {
  min-width: 500px;
}

/* Modal Header */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--odoo-spacing-lg);
  border-bottom: 1px solid var(--odoo-border-light);
  background: var(--odoo-bg-light);
}

.modal-header h2 {
  font-size: var(--odoo-font-size-xl);
  font-weight: var(--odoo-font-weight-semibold);
  color: var(--odoo-text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--odoo-spacing-sm);
}

.modal-header h2 i {
  color: var(--odoo-primary);
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--odoo-font-size-lg);
  color: var(--odoo-text-muted);
  cursor: pointer;
  padding: var(--odoo-spacing-xs);
  border-radius: var(--odoo-border-radius-sm);
  transition: all var(--odoo-transition-fast);
}

.modal-close:hover {
  background: var(--odoo-bg-secondary);
  color: var(--odoo-text-primary);
}

/* Modal Body */
.modal-body {
  padding: var(--odoo-spacing-lg);
  overflow-y: auto;
  flex: 1;
}

/* Form Styles */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--odoo-spacing-lg);
  margin-bottom: var(--odoo-spacing-lg);
}

.form-row:last-child {
  margin-bottom: 0;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-size: var(--odoo-font-size-sm);
  font-weight: var(--odoo-font-weight-medium);
  color: var(--odoo-text-primary);
  margin-bottom: var(--odoo-spacing-xs);
  display: flex;
  align-items: center;
}

.form-group label.required::after {
  content: '*';
  color: var(--odoo-danger);
  margin-left: 4px;
}

.form-group input,
.form-group select {
  padding: var(--odoo-spacing-sm);
  border: 1px solid var(--odoo-border-medium);
  border-radius: var(--odoo-border-radius);
  font-size: var(--odoo-font-size-base);
  transition: border-color var(--odoo-transition-fast), box-shadow var(--odoo-transition-fast);
  background: var(--odoo-white);
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--odoo-primary);
  box-shadow: 0 0 0 0.2rem rgba(113, 75, 103, 0.25);
}

.form-group input.error,
.form-group select.error {
  border-color: var(--odoo-danger);
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-group input::placeholder {
  color: var(--odoo-text-muted);
}

/* Select Optgroups */
.form-group select optgroup {
  font-weight: var(--odoo-font-weight-semibold);
  color: var(--odoo-primary);
  background: var(--odoo-bg-light);
}

.form-group select option {
  padding: var(--odoo-spacing-xs);
  color: var(--odoo-text-primary);
}

/* Checkbox Styles */
.checkbox-group {
  grid-column: 1 / -1;
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: var(--odoo-spacing-sm);
  cursor: pointer;
  font-size: var(--odoo-font-size-base);
  color: var(--odoo-text-primary);
  margin-bottom: var(--odoo-spacing-sm);
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid var(--odoo-border-medium);
  border-radius: var(--odoo-border-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--odoo-transition-fast);
  flex-shrink: 0;
  margin-top: 2px;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background: var(--odoo-primary);
  border-color: var(--odoo-primary);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  color: var(--odoo-white);
  font-size: 12px;
  font-weight: bold;
}

.checkbox-label small {
  display: block;
  color: var(--odoo-text-muted);
  font-size: var(--odoo-font-size-xs);
  margin-top: 2px;
}

/* Error Messages */
.error-text {
  color: var(--odoo-danger);
  font-size: var(--odoo-font-size-xs);
  margin-top: var(--odoo-spacing-xs);
  display: flex;
  align-items: center;
  gap: 4px;
}

.error-text::before {
  content: '⚠';
  font-size: 10px;
}

.general-error {
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.3);
  border-radius: var(--odoo-border-radius);
  padding: var(--odoo-spacing-sm);
  margin-bottom: var(--odoo-spacing-md);
  color: var(--odoo-danger);
  display: flex;
  align-items: center;
  gap: var(--odoo-spacing-sm);
}

.general-error i {
  color: var(--odoo-danger);
}

/* Modal Footer */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--odoo-spacing-sm);
  padding: var(--odoo-spacing-lg);
  border-top: 1px solid var(--odoo-border-light);
  background: var(--odoo-bg-light);
}

/* Single column layout for smaller screens */
@media (max-width: 768px) {
  .modal-overlay {
    padding: var(--odoo-spacing-md);
  }
  
  .create-account-modal {
    min-width: auto;
    max-width: 100%;
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: var(--odoo-spacing-md);
  }
  
  .modal-header {
    padding: var(--odoo-spacing-md);
  }
  
  .modal-body {
    padding: var(--odoo-spacing-md);
  }
  
  .modal-footer {
    padding: var(--odoo-spacing-md);
    flex-direction: column-reverse;
  }
  
  .modal-footer button {
    width: 100%;
  }
}

/* Loading state */
.modal-container.loading {
  pointer-events: none;
  opacity: 0.7;
}

/* Animation */
.modal-overlay {
  animation: fadeIn 0.2s ease-out;
}

.modal-container {
  animation: slideIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
