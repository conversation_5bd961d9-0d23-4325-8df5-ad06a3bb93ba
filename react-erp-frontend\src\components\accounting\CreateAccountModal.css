/* Odoo Community Form Structure with Enterprise Colors */

/* Action Manager */
.o_action_manager {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f9f9f9;
  font-family: "Lucida Grande", Helvetica, Verdana, Arial, sans-serif;
  font-size: 13px;
}

/* Form View Controller */
.o_form_view.o_view_controller {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.o_form_view_container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Control Panel - Enterprise Colors */
.o_control_panel {
  background-color: #fff;
  border-bottom: 1px solid #dee2e6;
  padding: 0.75rem 1.5rem;
  min-height: 56px;
  display: flex;
  align-items: center;
}

.o_control_panel_main {
  flex: 1;
}

.o_control_panel_breadcrumbs {
  display: flex;
  align-items: center;
}

.o_control_panel_main_buttons {
  display: flex;
  gap: 0.5rem;
}

.o_form_buttons_view {
  display: flex;
  gap: 0.5rem;
}

/* Enterprise Style Buttons - Proper Odoo Colors */
.btn {
  display: inline-block;
  font-weight: 400;
  line-height: 1.5;
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: 1px solid transparent;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  border-radius: 0.25rem;
  transition: all 0.15s ease-in-out;
}

.btn-primary,
.o_form_button_save {
  color: #fff !important;
  background-color: #714B67 !important;
  border-color: #714B67 !important;
}

.btn-primary:hover,
.o_form_button_save:hover {
  color: #fff !important;
  background-color: #6A4460 !important;
  border-color: #6A4460 !important;
}

.btn-secondary,
.o_form_button_cancel {
  color: #495057 !important;
  background-color: #f8f9fa !important;
  border-color: #dee2e6 !important;
}

.btn-secondary:hover,
.o_form_button_cancel:hover {
  color: #495057 !important;
  background-color: #e9ecef !important;
  border-color: #adb5bd !important;
}

.btn:disabled {
  pointer-events: none;
  opacity: 0.65;
}

/* Content Area */
.o_content {
  flex: 1;
  overflow: auto;
  padding: 1.5rem;
}

/* Form Renderer */
.o_form_renderer {
  height: 100%;
}

.o_form_sheet_bg {
  background-color: #f9f9f9;
  min-height: 100%;
  padding: 1.5rem;
}

.o_form_sheet {
  background-color: #fff;
  border: 1px solid #dee2e6;
  border-radius: 0.25rem;
  padding: 1.5rem;
  margin: 0 auto;
  max-width: 1140px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
}

/* Alert Messages */
.alert {
  position: relative;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}

.alert-danger {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

/* Form Controls */
.form-control {
  display: block;
  width: 100%;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
  color: #495057;
  background-color: #fff;
  border-color: #714B67;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(113, 75, 103, 0.25);
}

.form-control.is-invalid {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* Special styling for Account Name field */
.form-control[name="name"] {
  border: none !important;
  border-bottom: 2px solid transparent !important;
  border-radius: 0 !important;
  background: transparent !important;
  font-size: 1.5rem !important;
  font-weight: 400 !important;
  padding: 0.5rem 0 !important;
  box-shadow: none !important;
  transition: border-bottom-color 0.15s ease-in-out !important;
}

.form-control[name="name"]:hover,
.form-control[name="name"]:focus {
  border-bottom: 2px solid #714B67 !important;
  box-shadow: none !important;
  outline: none !important;
}

.form-control[name="name"]::placeholder {
  color: #adb5bd;
  font-style: italic;
}

/* Special styling for Code field - Same size as Account Name */
.form-control[name="code"] {
  border: none !important;
  border-bottom: 2px solid transparent !important;
  border-radius: 0 !important;
  background: transparent !important;
  font-size: 1.5rem !important;
  font-weight: 400 !important;
  padding: 0.5rem 0 !important;
  box-shadow: none !important;
  transition: border-bottom-color 0.15s ease-in-out !important;
}

.form-control[name="code"]:hover,
.form-control[name="code"]:focus {
  border-bottom: 2px solid #714B67 !important;
  box-shadow: none !important;
  outline: none !important;
}

/* Special styling for Type and Account Group fields - Smaller font */
.form-control[name="account_type"],
.form-control[name="group"] {
  border: none !important;
  border-bottom: 2px solid transparent !important;
  border-radius: 0 !important;
  background: transparent !important;
  font-size: 1rem !important;
  font-weight: 400 !important;
  padding: 0.5rem 0 !important;
  box-shadow: none !important;
  transition: border-bottom-color 0.15s ease-in-out !important;
}

.form-control[name="account_type"]:hover,
.form-control[name="account_type"]:focus,
.form-control[name="group"]:hover,
.form-control[name="group"]:focus {
  border-bottom: 2px solid #714B67 !important;
  box-shadow: none !important;
  outline: none !important;
}

/* Odoo Specific Classes */
.oe_inline {
  display: inline-block !important;
}

.o_notebook {
  margin-top: 1rem;
}

.tab-content {
  border: none;
}

.tab-pane {
  padding: 1rem 0;
}

.o_group {
  display: block;
  margin-bottom: 1rem;
}

.o_inner_group {
  display: block;
}

.o_wrap_field {
  margin-bottom: 0.75rem;
  display: flex;
  align-items: flex-start;
}

.o_cell {
  vertical-align: top;
}

.o_wrap_label {
  font-weight: 500;
  color: #495057;
  margin-bottom: 0.25rem;
  width: 200px;
  flex-shrink: 0;
  padding-right: 1rem;
  padding-top: 0.375rem;
}

.o_form_label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #495057;
  margin: 0;
  display: block;
}

.o_wrap_input {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.o_input {
  display: block;
  width: 100%;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.o_input:focus {
  color: #495057;
  background-color: #fff;
  border-color: #714B67;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(113, 75, 103, 0.25);
}

.o_field_boolean {
  display: flex;
  align-items: center;
  padding-top: 0.375rem;
}

/* Bootstrap Grid */
.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -0.75rem;
  margin-left: -0.75rem;
}

.col {
  flex-basis: 0;
  flex-grow: 1;
  max-width: 100%;
  padding-right: 0.75rem;
  padding-left: 0.75rem;
}

.col-md-auto {
  flex: 0 0 auto;
  width: auto;
  max-width: 100%;
}

.col-md-8 {
  flex: 0 0 66.666667%;
  max-width: 66.666667%;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .o_content {
    padding: 1rem;
  }

  .o_form_sheet_bg {
    padding: 1rem;
  }

  .o_form_sheet {
    padding: 1rem;
  }

  .o_wrap_field {
    flex-direction: column;
    align-items: stretch;
  }

  .o_wrap_label {
    width: auto;
    padding-right: 0;
    padding-top: 0;
    margin-bottom: 0.25rem;
  }

  .o_wrap_input {
    max-width: none;
  }

  .col-md-8,
  .col-md-auto {
    flex: 0 0 100%;
    max-width: 100%;
  }
}


