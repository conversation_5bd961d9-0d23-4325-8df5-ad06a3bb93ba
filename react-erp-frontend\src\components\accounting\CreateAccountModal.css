/* Odoo-Style Modal CSS */

/* Dialog Container */
.o_dialog_container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1050;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Modal Base */
.modal {
  display: block;
  position: relative;
  z-index: 1055;
}

.modal-dialog {
  position: relative;
  width: auto;
  margin: 1.75rem;
  pointer-events: none;
}

.modal-lg {
  max-width: 800px;
}

.modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  pointer-events: auto;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0.3rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  outline: 0;
}

/* Modal Header */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1rem;
  border-bottom: 1px solid #dee2e6;
  border-top-left-radius: calc(0.3rem - 1px);
  border-top-right-radius: calc(0.3rem - 1px);
  background-color: #f8f9fa;
}

.modal-title {
  margin-bottom: 0;
  line-height: 1.5;
  font-size: 1.25rem;
  font-weight: 500;
  color: #212529;
}

.btn-close {
  box-sizing: content-box;
  width: 1em;
  height: 1em;
  padding: 0.25em 0.25em;
  color: #000;
  background: transparent;
  border: 0;
  border-radius: 0.25rem;
  opacity: 0.5;
  cursor: pointer;
  font-size: 1.5rem;
  line-height: 1;
}

.btn-close:hover {
  opacity: 0.75;
}

.btn-close span {
  font-size: 1.5rem;
  line-height: 1;
}

/* Modal Body */
.modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: 1rem;
}

/* Odoo Form Styles */
.o_form_view {
  background-color: #fff;
}

.o_form_sheet {
  background-color: #fff;
  border: 1px solid #dee2e6;
  border-radius: 0.25rem;
  padding: 1.5rem;
  margin: 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Form Groups */
.o_group {
  display: block;
  margin-bottom: 1rem;
}

.o_inner_group {
  display: block;
}

/* Form Fields */
.o_wrap_field {
  margin-bottom: 0.75rem;
}

.o_cell {
  vertical-align: top;
}

.o_wrap_label {
  font-weight: 500;
  color: #495057;
  margin-bottom: 0.25rem;
}

.o_form_label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #495057;
  margin-bottom: 0.25rem;
  display: block;
}

.o_wrap_input {
  position: relative;
}

.form-group input.error,
.form-group select.error {
  border-color: var(--odoo-danger);
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-group input::placeholder {
  color: var(--odoo-text-muted);
}

/* Select Optgroups */
.form-group select optgroup {
  font-weight: var(--odoo-font-weight-semibold);
  color: var(--odoo-primary);
  background: var(--odoo-bg-light);
}

.form-group select option {
  padding: var(--odoo-spacing-xs);
  color: var(--odoo-text-primary);
}

/* Checkbox Styles */
.checkbox-group {
  grid-column: 1 / -1;
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: var(--odoo-spacing-sm);
  cursor: pointer;
  font-size: var(--odoo-font-size-base);
  color: var(--odoo-text-primary);
  margin-bottom: var(--odoo-spacing-sm);
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid var(--odoo-border-medium);
  border-radius: var(--odoo-border-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--odoo-transition-fast);
  flex-shrink: 0;
  margin-top: 2px;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background: var(--odoo-primary);
  border-color: var(--odoo-primary);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  color: var(--odoo-white);
  font-size: 12px;
  font-weight: bold;
}

.checkbox-label small {
  display: block;
  color: var(--odoo-text-muted);
  font-size: var(--odoo-font-size-xs);
  margin-top: 2px;
}

/* Error Messages */
.error-text {
  color: var(--odoo-danger);
  font-size: var(--odoo-font-size-xs);
  margin-top: var(--odoo-spacing-xs);
  display: flex;
  align-items: center;
  gap: 4px;
}

.error-text::before {
  content: '⚠';
  font-size: 10px;
}

.general-error {
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.3);
  border-radius: var(--odoo-border-radius);
  padding: var(--odoo-spacing-sm);
  margin-bottom: var(--odoo-spacing-md);
  color: var(--odoo-danger);
  display: flex;
  align-items: center;
  gap: var(--odoo-spacing-sm);
}

.general-error i {
  color: var(--odoo-danger);
}

/* Modal Footer */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--odoo-spacing-sm);
  padding: var(--odoo-spacing-lg);
  border-top: 1px solid var(--odoo-border-light);
  background: var(--odoo-bg-light);
}

/* Single column layout for smaller screens */
@media (max-width: 768px) {
  .modal-overlay {
    padding: var(--odoo-spacing-md);
  }
  
  .create-account-modal {
    min-width: auto;
    max-width: 100%;
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: var(--odoo-spacing-md);
  }
  
  .modal-header {
    padding: var(--odoo-spacing-md);
  }
  
  .modal-body {
    padding: var(--odoo-spacing-md);
  }
  
  .modal-footer {
    padding: var(--odoo-spacing-md);
    flex-direction: column-reverse;
  }
  
  .modal-footer button {
    width: 100%;
  }
}

/* Loading state */
.modal-container.loading {
  pointer-events: none;
  opacity: 0.7;
}

/* Animation */
.modal-overlay {
  animation: fadeIn 0.2s ease-out;
}

.modal-container {
  animation: slideIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
