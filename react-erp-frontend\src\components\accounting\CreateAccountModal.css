/* Exact Odoo Form Replication */
.odoo-page {
  min-height: 100vh;
  background-color: #f8f9fa;
  font-family: "Lucida Grande", Helvetica, Verdana, Arial, sans-serif;
  font-size: 13px;
  color: #4c4c4c;
}

/* Control Panel - Exact Odoo Style */
.odoo-control-panel {
  background: #fff;
  border-bottom: 1px solid #ddd;
  padding: 8px 16px;
  min-height: 40px;
  display: flex;
  align-items: center;
}

.odoo-control-buttons {
  display: flex;
  gap: 4px;
}

.odoo-btn {
  padding: 4px 8px;
  font-size: 13px;
  font-weight: normal;
  border-radius: 3px;
  border: 1px solid;
  cursor: pointer;
  transition: all 0.1s;
  min-width: 60px;
  text-align: center;
}

.odoo-btn-primary {
  background: linear-gradient(to bottom, #729fcf 0%, #3465a4 100%);
  border-color: #204a87;
  color: #fff;
  text-shadow: 0 1px 1px rgba(0,0,0,0.2);
}

.odoo-btn-primary:hover {
  background: linear-gradient(to bottom, #8ab4d3 0%, #4f7bd4 100%);
}

.odoo-btn-secondary {
  background: linear-gradient(to bottom, #f7f7f7 0%, #d7d7d7 100%);
  border-color: #ccc;
  color: #333;
  text-shadow: 0 1px 1px rgba(255,255,255,0.8);
}

.odoo-btn-secondary:hover {
  background: linear-gradient(to bottom, #fff 0%, #e6e6e6 100%);
}

/* Main Content */
.odoo-content {
  padding: 16px;
  max-width: 1200px;
  margin: 0 auto;
}

.odoo-form {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 3px;
  padding: 16px;
  max-width: 800px;
  margin: 0 auto;
  box-shadow: 0 1px 3px rgba(0,0,0,0.13);
}

/* Alert Messages */
.alert {
  padding: 8px 12px;
  margin-bottom: 16px;
  border-radius: 3px;
  border: 1px solid;
}

.alert-danger {
  background-color: #f2dede;
  border-color: #ebccd1;
  color: #a94442;
}

/* Account Name Section - Large Title */
.account-name-section {
  margin-bottom: 24px;
  border-bottom: 1px solid #eee;
  padding-bottom: 16px;
}

.account-name-input {
  width: 100%;
  border: none;
  background: transparent;
  font-size: 24px;
  font-weight: normal;
  color: #4c4c4c;
  padding: 8px 0;
  outline: none;
}

.account-name-input::placeholder {
  color: #999;
  font-style: italic;
}

/* Form Fields */
.form-fields {
  margin-bottom: 24px;
}

.field-row {
  margin-bottom: 12px;
  display: flex;
  align-items: flex-start;
  min-height: 28px;
}

.field-row label {
  width: 120px;
  font-size: 13px;
  color: #666;
  padding-top: 6px;
  padding-right: 8px;
  flex-shrink: 0;
}

.field-input {
  flex: 1;
  max-width: 300px;
  padding: 4px 6px;
  font-size: 13px;
  border: 1px solid #ccc;
  border-radius: 3px;
  background-color: #fff;
  color: #4c4c4c;
}

.field-input:focus {
  border-color: #729fcf;
  outline: none;
  box-shadow: 0 0 3px rgba(114, 159, 207, 0.5);
}

.field-input.error {
  border-color: #d9534f;
  background-color: #fff5f5;
}

.field-error {
  color: #d9534f;
  font-size: 11px;
  margin-top: 2px;
  margin-left: 128px;
}

/* Options Section */
.options-section {
  border-top: 1px solid #eee;
  padding-top: 16px;
}

.section-title {
  font-size: 11px;
  font-weight: bold;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 12px;
  padding-bottom: 4px;
  border-bottom: 1px solid #eee;
}

/* Checkbox Rows */
.checkbox-row {
  margin-bottom: 12px;
  display: flex;
  align-items: flex-start;
  min-height: 20px;
}

.checkbox-label {
  width: 120px;
  font-size: 13px;
  color: #666;
  padding-top: 2px;
  padding-right: 8px;
  flex-shrink: 0;
}

.checkbox-row input[type="checkbox"] {
  margin-top: 2px;
  margin-right: 8px;
}

.field-help {
  font-size: 11px;
  color: #999;
  margin-top: 2px;
  margin-left: 20px;
  line-height: 1.3;
  max-width: 300px;
}


