/* Odoo Form View CSS - Exact Match */

/* Action Manager */
.o_action_manager {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f9f9f9;
}

/* Form View Controller */
.o_form_view.o_view_controller {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.o_form_view_container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Control Panel */
.o_control_panel {
  background-color: #fff;
  border-bottom: 1px solid #dee2e6;
  padding: 0.75rem 1.5rem;
  min-height: 56px;
  align-items: center;
}

.o_control_panel_main {
  flex: 1;
}

.o_control_panel_breadcrumbs {
  display: flex;
  align-items: center;
}

.o_control_panel_main_buttons {
  display: flex;
  gap: 0.5rem;
}

.o_form_buttons_view {
  display: flex;
  gap: 0.5rem;
}

/* Form Buttons */
.o_form_button_save {
  background-color: #0d6efd;
  border-color: #0d6efd;
  color: #fff;
  font-weight: 500;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  border-radius: 0.25rem;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
}

.o_form_button_save:hover {
  background-color: #0b5ed7;
  border-color: #0a58ca;
}

.o_form_button_save:disabled {
  opacity: 0.65;
  pointer-events: none;
}

.o_form_button_cancel {
  background-color: #6c757d;
  border-color: #6c757d;
  color: #fff;
  font-weight: 500;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  border-radius: 0.25rem;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
}

.o_form_button_cancel:hover {
  background-color: #5c636a;
  border-color: #565e64;
}

/* Content Area */
.o_content {
  flex: 1;
  overflow: auto;
  padding: 1.5rem;
}

/* Form Renderer */
.o_form_renderer {
  height: 100%;
}

.o_form_sheet_bg {
  background-color: #f9f9f9;
  min-height: 100%;
  padding: 1.5rem;
}

.o_form_sheet {
  background-color: #fff;
  border: 1px solid #dee2e6;
  border-radius: 0.25rem;
  padding: 1.5rem;
  margin: 0 auto;
  max-width: 1140px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
}

/* Form Title */
.oe_title {
  margin-bottom: 2rem;
}

.oe_title h1 {
  font-size: 2rem;
  font-weight: 300;
  margin: 0;
  color: #212529;
}

.o_row {
  display: flex;
  align-items: center;
}

.o_field_widget {
  display: flex;
  align-items: center;
}

.o_field_widget_text input {
  border: none;
  background: transparent;
  font-size: 2rem;
  font-weight: 300;
  color: #212529;
  padding: 0;
  width: 100%;
  outline: none;
}

.o_field_widget_text input::placeholder {
  color: #6c757d;
  opacity: 0.7;
}

/* Form Groups */
.o_group {
  display: block;
  margin-bottom: 1.5rem;
}

.o_inner_group {
  display: block;
}

/* Horizontal Separator */
.o_horizontal_separator {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 0.5rem;
  margin: 1.5rem 0 1rem 0;
}

/* Form Fields */
.o_wrap_field {
  margin-bottom: 0.75rem;
  display: flex;
  align-items: flex-start;
}

.o_cell {
  vertical-align: top;
}

.o_wrap_label {
  font-weight: 500;
  color: #495057;
  margin-bottom: 0.25rem;
  width: 200px;
  flex-shrink: 0;
  padding-right: 1rem;
  padding-top: 0.375rem;
}

.o_form_label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #495057;
  margin: 0;
  display: block;
}

.o_wrap_input {
  position: relative;
  flex: 1;
  max-width: 400px;
}

/* Input Styles */
.o_input {
  display: block;
  width: 100%;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.o_input:focus {
  color: #495057;
  background-color: #fff;
  border-color: #86b7fe;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.o_input.is-invalid {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* Validation Feedback */
.invalid-feedback {
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: #dc3545;
}

.invalid-feedback.d-block {
  display: block !important;
}

/* Alert Styles */
.alert {
  position: relative;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}

.alert-danger {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

/* Boolean Field */
.o_field_boolean {
  display: flex;
  align-items: center;
  padding-top: 0.375rem;
}

.custom-control {
  position: relative;
  display: block;
  min-height: 1.5rem;
  padding-left: 1.5rem;
}

.custom-control-input {
  position: absolute;
  left: 0;
  z-index: -1;
  width: 1rem;
  height: 1.25rem;
  opacity: 0;
}

.custom-control-label {
  position: relative;
  margin-bottom: 0;
  vertical-align: top;
}

.custom-control-label::before {
  position: absolute;
  top: 0.25rem;
  left: -1.5rem;
  display: block;
  width: 1rem;
  height: 1rem;
  pointer-events: none;
  content: "";
  background-color: #fff;
  border: 1px solid #adb5bd;
  border-radius: 0.25rem;
}

.custom-control-input:checked ~ .custom-control-label::after {
  position: absolute;
  top: 0.25rem;
  left: -1.5rem;
  display: block;
  width: 1rem;
  height: 1rem;
  content: "";
  background: no-repeat 50%/50% 50%;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%23fff' d='m6.564.75-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z'/%3e%3c/svg%3e");
}

.custom-control-input:checked ~ .custom-control-label::before {
  color: #fff;
  border-color: #0d6efd;
  background-color: #0d6efd;
}

/* Form Text */
.form-text {
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: #6c757d;
}

.text-muted {
  color: #6c757d !important;
}

/* Utility Classes */
.d-flex {
  display: flex !important;
}

.d-block {
  display: block !important;
}

.flex-column {
  flex-direction: column !important;
}

.flex-grow-1 {
  flex-grow: 1 !important;
}

.w-100 {
  width: 100% !important;
}

.text-break {
  word-wrap: break-word !important;
  word-break: break-word !important;
}

.text-900 {
  font-weight: 900 !important;
}

.mb-3 {
  margin-bottom: 1rem !important;
}

.mb-sm-0 {
  margin-bottom: 0 !important;
}

.mt-4 {
  margin-top: 1.5rem !important;
}

.me-1 {
  margin-right: 0.25rem !important;
}

.me-2 {
  margin-right: 0.5rem !important;
}

.gap-1 {
  gap: 0.25rem !important;
}

.position-relative {
  position: relative !important;
}

/* Button Utilities */
.btn {
  display: inline-block;
  font-weight: 400;
  line-height: 1.5;
  color: #212529;
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  background-color: transparent;
  border: 1px solid transparent;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  border-radius: 0.25rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.btn:hover {
  text-decoration: none;
}

.btn:focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.btn:disabled {
  pointer-events: none;
  opacity: 0.65;
}

/* Responsive */
@media (min-width: 576px) {
  .d-sm-contents {
    display: contents !important;
  }

  .flex-sm-grow-0 {
    flex-grow: 0 !important;
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .o_content {
    padding: 1rem;
  }

  .o_form_sheet_bg {
    padding: 1rem;
  }

  .o_form_sheet {
    padding: 1rem;
  }

  .o_wrap_field {
    flex-direction: column;
    align-items: stretch;
  }

  .o_wrap_label {
    width: auto;
    padding-right: 0;
    padding-top: 0;
    margin-bottom: 0.25rem;
  }

  .o_wrap_input {
    max-width: none;
  }

  .oe_title h1 {
    font-size: 1.5rem;
  }

  .o_field_widget_text input {
    font-size: 1.5rem;
  }
}
