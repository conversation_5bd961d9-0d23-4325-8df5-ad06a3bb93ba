#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create sample accounting data for testing the ERP system.
This will populate the accounting tables with realistic test data.
"""

import os
import sys
import django
from decimal import Decimal

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'django_erp.settings')
sys.path.append('django_erp')
django.setup()

from django.contrib.auth.models import User
from core.models import Company, Currency
from accounting.models import (
    AccountGroup, AccountAccount, AccountJournal, AccountMove, 
    AccountMoveLine, AccountTax
)

def create_accounting_data():
    """Create comprehensive accounting test data"""
    
    print("Creating accounting test data...")
    
    # Get or create admin user and company
    admin_user = User.objects.filter(is_superuser=True).first()
    if not admin_user:
        print("No admin user found. Please create a superuser first.")
        return
    
    # Get or create default company
    company, created = Company.objects.get_or_create(
        name="Demo Company Ltd",
        defaults={
            'email': '<EMAIL>',
            'phone': '******-0123',
            'website': 'https://democompany.com',
            'create_uid': admin_user,
            'write_uid': admin_user,
        }
    )
    if created:
        print(f"✓ Created company: {company.name}")
    
    # Get or create default currency
    currency, created = Currency.objects.get_or_create(
        name="USD",
        defaults={
            'symbol': '$',
            'position': 'before',
            'decimal_places': 2,
            'create_uid': admin_user,
            'write_uid': admin_user,
        }
    )
    if created:
        print(f"✓ Created currency: {currency.name}")
    
    # Create Account Groups
    account_groups = [
        {'name': 'Assets', 'code_prefix_start': '1000', 'code_prefix_end': '1999'},
        {'name': 'Liabilities', 'code_prefix_start': '2000', 'code_prefix_end': '2999'},
        {'name': 'Equity', 'code_prefix_start': '3000', 'code_prefix_end': '3999'},
        {'name': 'Income', 'code_prefix_start': '4000', 'code_prefix_end': '4999'},
        {'name': 'Expenses', 'code_prefix_start': '5000', 'code_prefix_end': '5999'},
    ]
    
    created_groups = {}
    for group_data in account_groups:
        group, created = AccountGroup.objects.get_or_create(
            name=group_data['name'],
            company=company,
            defaults={
                'code_prefix_start': group_data['code_prefix_start'],
                'code_prefix_end': group_data['code_prefix_end'],
                'create_uid': admin_user,
                'write_uid': admin_user,
            }
        )
        created_groups[group_data['name']] = group
        if created:
            print(f"✓ Created account group: {group.name}")
    
    # Create Chart of Accounts
    accounts_data = [
        # Assets
        {'code': '1000', 'name': 'Cash', 'account_type': 'asset_cash', 'group': 'Assets', 'reconcile': False},
        {'code': '1100', 'name': 'Bank Account - Main', 'account_type': 'asset_cash', 'group': 'Assets', 'reconcile': True},
        {'code': '1110', 'name': 'Bank Account - Savings', 'account_type': 'asset_cash', 'group': 'Assets', 'reconcile': True},
        {'code': '1200', 'name': 'Accounts Receivable', 'account_type': 'asset_receivable', 'group': 'Assets', 'reconcile': True},
        {'code': '1300', 'name': 'Inventory', 'account_type': 'asset_current', 'group': 'Assets', 'reconcile': False},
        {'code': '1400', 'name': 'Prepaid Expenses', 'account_type': 'asset_current', 'group': 'Assets', 'reconcile': False},
        {'code': '1500', 'name': 'Office Equipment', 'account_type': 'asset_fixed', 'group': 'Assets', 'reconcile': False},
        {'code': '1510', 'name': 'Accumulated Depreciation - Equipment', 'account_type': 'asset_fixed', 'group': 'Assets', 'reconcile': False},
        
        # Liabilities
        {'code': '2000', 'name': 'Accounts Payable', 'account_type': 'liability_payable', 'group': 'Liabilities', 'reconcile': True},
        {'code': '2100', 'name': 'Credit Card Payable', 'account_type': 'liability_current', 'group': 'Liabilities', 'reconcile': True},
        {'code': '2200', 'name': 'Accrued Expenses', 'account_type': 'liability_current', 'group': 'Liabilities', 'reconcile': False},
        {'code': '2300', 'name': 'Sales Tax Payable', 'account_type': 'liability_current', 'group': 'Liabilities', 'reconcile': False},
        {'code': '2400', 'name': 'Long-term Debt', 'account_type': 'liability_non_current', 'group': 'Liabilities', 'reconcile': False},
        
        # Equity
        {'code': '3000', 'name': 'Owner\'s Equity', 'account_type': 'equity', 'group': 'Equity', 'reconcile': False},
        {'code': '3100', 'name': 'Retained Earnings', 'account_type': 'equity', 'group': 'Equity', 'reconcile': False},
        
        # Income
        {'code': '4000', 'name': 'Sales Revenue', 'account_type': 'income', 'group': 'Income', 'reconcile': False},
        {'code': '4100', 'name': 'Service Revenue', 'account_type': 'income', 'group': 'Income', 'reconcile': False},
        {'code': '4200', 'name': 'Interest Income', 'account_type': 'income_other', 'group': 'Income', 'reconcile': False},
        
        # Expenses
        {'code': '5000', 'name': 'Cost of Goods Sold', 'account_type': 'expense_direct_cost', 'group': 'Expenses', 'reconcile': False},
        {'code': '5100', 'name': 'Office Supplies', 'account_type': 'expense', 'group': 'Expenses', 'reconcile': False},
        {'code': '5200', 'name': 'Rent Expense', 'account_type': 'expense', 'group': 'Expenses', 'reconcile': False},
        {'code': '5300', 'name': 'Utilities Expense', 'account_type': 'expense', 'group': 'Expenses', 'reconcile': False},
        {'code': '5400', 'name': 'Marketing Expense', 'account_type': 'expense', 'group': 'Expenses', 'reconcile': False},
        {'code': '5500', 'name': 'Professional Services', 'account_type': 'expense', 'group': 'Expenses', 'reconcile': False},
        {'code': '5600', 'name': 'Depreciation Expense', 'account_type': 'expense_depreciation', 'group': 'Expenses', 'reconcile': False},
    ]
    
    created_accounts = {}
    for account_data in accounts_data:
        account, created = AccountAccount.objects.get_or_create(
            code=account_data['code'],
            company=company,
            defaults={
                'name': account_data['name'],
                'account_type': account_data['account_type'],
                'group': created_groups[account_data['group']],
                'reconcile': account_data['reconcile'],
                'currency': currency,
                'active': True,
                'create_uid': admin_user,
                'write_uid': admin_user,
            }
        )
        created_accounts[account_data['code']] = account
        if created:
            print(f"✓ Created account: {account.code} - {account.name}")
    
    # Create Journals
    journals_data = [
        {'name': 'Sales Journal', 'code': 'SAL', 'type': 'sale', 'default_account': '4000'},
        {'name': 'Purchase Journal', 'code': 'PUR', 'type': 'purchase', 'default_account': '5000'},
        {'name': 'Cash Journal', 'code': 'CSH', 'type': 'cash', 'default_account': '1000'},
        {'name': 'Bank Journal', 'code': 'BNK', 'type': 'bank', 'default_account': '1100'},
        {'name': 'General Journal', 'code': 'GEN', 'type': 'general', 'default_account': '1000'},
    ]
    
    created_journals = {}
    for journal_data in journals_data:
        journal, created = AccountJournal.objects.get_or_create(
            code=journal_data['code'],
            company=company,
            defaults={
                'name': journal_data['name'],
                'type': journal_data['type'],
                'default_account': created_accounts[journal_data['default_account']],
                'currency': currency,
                'active': True,
                'sequence': 10,
                'create_uid': admin_user,
                'write_uid': admin_user,
            }
        )
        created_journals[journal_data['code']] = journal
        if created:
            print(f"✓ Created journal: {journal.code} - {journal.name}")
    
    # Create Tax Records
    taxes_data = [
        {'name': 'Sales Tax 10%', 'amount': 10.0, 'amount_type': 'percent', 'type_tax_use': 'sale'},
        {'name': 'Purchase Tax 10%', 'amount': 10.0, 'amount_type': 'percent', 'type_tax_use': 'purchase'},
        {'name': 'VAT 15%', 'amount': 15.0, 'amount_type': 'percent', 'type_tax_use': 'sale'},
    ]
    
    for tax_data in taxes_data:
        tax, created = AccountTax.objects.get_or_create(
            name=tax_data['name'],
            company=company,
            defaults={
                'amount': tax_data['amount'],
                'amount_type': tax_data['amount_type'],
                'type_tax_use': tax_data['type_tax_use'],
                'active': True,
                'sequence': 10,
                'create_uid': admin_user,
                'write_uid': admin_user,
            }
        )
        if created:
            print(f"✓ Created tax: {tax.name}")
    
    print("\n" + "="*50)
    print("ACCOUNTING DATA CREATED SUCCESSFULLY!")
    print("="*50)
    print(f"✓ Account Groups: {AccountGroup.objects.count()}")
    print(f"✓ Chart of Accounts: {AccountAccount.objects.count()}")
    print(f"✓ Journals: {AccountJournal.objects.count()}")
    print(f"✓ Taxes: {AccountTax.objects.count()}")
    print("\nYou can now test the accounting module with real data!")
    print("Navigate to: http://localhost:3000")
    print("Login and click on the Accounting module to see the data.")

if __name__ == '__main__':
    create_accounting_data()
