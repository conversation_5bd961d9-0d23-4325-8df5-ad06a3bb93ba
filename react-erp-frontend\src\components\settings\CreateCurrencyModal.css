/* Create Currency Modal - Odoo Style */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--odoo-spacing-lg);
}

.modal-content {
  background: var(--odoo-white);
  border-radius: var(--odoo-border-radius-lg);
  box-shadow: var(--odoo-shadow-lg);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--odoo-spacing-lg);
  border-bottom: 1px solid var(--odoo-border-light);
}

.modal-header h2 {
  font-size: var(--odoo-font-size-title);
  font-weight: var(--odoo-font-weight-semibold);
  color: var(--odoo-text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--odoo-spacing-sm);
}

.modal-header h2 i {
  color: var(--odoo-primary);
}

.modal-close {
  width: 32px;
  height: 32px;
  border: none;
  background: var(--odoo-bg-light);
  color: var(--odoo-text-secondary);
  border-radius: var(--odoo-border-radius-sm);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--odoo-transition-fast);
}

.modal-close:hover {
  background: var(--odoo-danger);
  color: var(--odoo-white);
}

.modal-close:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Modal Form */
.modal-form {
  padding: var(--odoo-spacing-lg);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--odoo-spacing-lg);
  margin-bottom: var(--odoo-spacing-lg);
}

.form-row:last-child {
  margin-bottom: 0;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-size: var(--odoo-font-size-sm);
  font-weight: var(--odoo-font-weight-medium);
  color: var(--odoo-text-primary);
  margin-bottom: var(--odoo-spacing-xs);
  display: flex;
  align-items: center;
}

.form-group label.required::after {
  content: '*';
  color: var(--odoo-danger);
  margin-left: 4px;
}

.form-group input,
.form-group select {
  padding: var(--odoo-spacing-sm);
  border: 1px solid var(--odoo-border-medium);
  border-radius: var(--odoo-border-radius);
  font-size: var(--odoo-font-size-base);
  transition: border-color var(--odoo-transition-fast), box-shadow var(--odoo-transition-fast);
  background: var(--odoo-white);
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--odoo-primary);
  box-shadow: 0 0 0 0.2rem rgba(113, 75, 103, 0.25);
}

.form-group input.error,
.form-group select.error {
  border-color: var(--odoo-danger);
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-group input:disabled,
.form-group select:disabled {
  background: var(--odoo-bg-light);
  color: var(--odoo-text-muted);
  cursor: not-allowed;
}

.form-group input::placeholder {
  color: var(--odoo-text-muted);
}

/* Checkbox */
.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--odoo-spacing-sm);
  cursor: pointer;
  font-size: var(--odoo-font-size-base);
  color: var(--odoo-text-primary);
}

.checkbox-label input[type="checkbox"] {
  width: 18px;
  height: 18px;
  margin: 0;
  cursor: pointer;
}

.checkbox-text {
  font-weight: var(--odoo-font-weight-medium);
}

/* Error Messages */
.error-text {
  color: var(--odoo-danger);
  font-size: var(--odoo-font-size-xs);
  margin-top: var(--odoo-spacing-xs);
  display: flex;
  align-items: center;
  gap: 4px;
}

.error-text::before {
  content: '⚠';
  font-size: 10px;
}

/* Modal Actions */
.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--odoo-spacing-sm);
  margin-top: var(--odoo-spacing-xl);
  padding-top: var(--odoo-spacing-lg);
  border-top: 1px solid var(--odoo-border-light);
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal-overlay {
    padding: var(--odoo-spacing-md);
  }
  
  .modal-content {
    max-height: 95vh;
  }
  
  .modal-header {
    padding: var(--odoo-spacing-md);
  }
  
  .modal-form {
    padding: var(--odoo-spacing-md);
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: var(--odoo-spacing-md);
    margin-bottom: var(--odoo-spacing-md);
  }
  
  .modal-actions {
    flex-direction: column-reverse;
    margin-top: var(--odoo-spacing-lg);
  }
  
  .modal-actions button {
    width: 100%;
  }
}
