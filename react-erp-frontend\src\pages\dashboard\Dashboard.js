import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import Button from '../../components/common/Button/Button';
import './Dashboard.css';

const Dashboard = () => {
  const { user, logout } = useAuth();

  const handleLogout = async () => {
    await logout();
  };

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <div className="dashboard-welcome">
          <h1>Welcome to ERP System</h1>
          <p>Hello, {user?.full_name || user?.username}!</p>
          {user?.roles && user.roles.length > 0 && (
            <div className="user-roles">
              <span className="roles-label">Roles: </span>
              {user.roles.map((assignment, index) => (
                <span key={assignment.id} className="role-badge">
                  {assignment.role.name}
                  {assignment.company_name && ` (${assignment.company_name})`}
                  {index < user.roles.length - 1 && ', '}
                </span>
              ))}
            </div>
          )}
        </div>
        
        <div className="dashboard-actions">
          <Button 
            variant="outline" 
            onClick={handleLogout}
            icon="fas fa-sign-out-alt"
          >
            Logout
          </Button>
        </div>
      </div>

      <div className="dashboard-content">
        <div className="dashboard-grid">
          {/* Sales Module */}
          {user?.permissions?.can_access_sales && (
            <div className="dashboard-card">
              <div className="card-icon">
                <i className="fas fa-chart-line"></i>
              </div>
              <div className="card-content">
                <h3>Sales</h3>
                <p>Manage sales orders and customers</p>
              </div>
            </div>
          )}

          {/* Accounting Module */}
          {user?.permissions?.can_access_accounting && (
            <div className="dashboard-card">
              <div className="card-icon">
                <i className="fas fa-calculator"></i>
              </div>
              <div className="card-content">
                <h3>Accounting</h3>
                <p>Journal entries and financial reports</p>
              </div>
            </div>
          )}

          {/* Inventory Module */}
          {user?.permissions?.can_access_inventory && (
            <div className="dashboard-card">
              <div className="card-icon">
                <i className="fas fa-boxes"></i>
              </div>
              <div className="card-content">
                <h3>Inventory</h3>
                <p>Product catalog and stock management</p>
              </div>
            </div>
          )}

          {/* HR Module */}
          {user?.permissions?.can_access_hr && (
            <div className="dashboard-card">
              <div className="card-icon">
                <i className="fas fa-users"></i>
              </div>
              <div className="card-content">
                <h3>HR</h3>
                <p>Employee management and payroll</p>
              </div>
            </div>
          )}

          {/* Purchases Module */}
          {user?.permissions?.can_access_purchases && (
            <div className="dashboard-card">
              <div className="card-icon">
                <i className="fas fa-shopping-cart"></i>
              </div>
              <div className="card-content">
                <h3>Purchases</h3>
                <p>Purchase orders and vendor management</p>
              </div>
            </div>
          )}

          {/* Manufacturing Module */}
          {user?.permissions?.can_access_manufacturing && (
            <div className="dashboard-card">
              <div className="card-icon">
                <i className="fas fa-industry"></i>
              </div>
              <div className="card-content">
                <h3>Manufacturing</h3>
                <p>Production orders and BOMs</p>
              </div>
            </div>
          )}

          {/* CRM Module */}
          {user?.permissions?.can_access_crm && (
            <div className="dashboard-card">
              <div className="card-icon">
                <i className="fas fa-phone"></i>
              </div>
              <div className="card-content">
                <h3>CRM</h3>
                <p>Customer relationship management</p>
              </div>
            </div>
          )}

          {/* Project Module */}
          {user?.permissions?.can_access_project && (
            <div className="dashboard-card">
              <div className="card-icon">
                <i className="fas fa-tasks"></i>
              </div>
              <div className="card-content">
                <h3>Projects</h3>
                <p>Project management and tasks</p>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="dashboard-footer">
        <p>&copy; 2024 ERP System. All rights reserved.</p>
      </div>
    </div>
  );
};

export default Dashboard;
