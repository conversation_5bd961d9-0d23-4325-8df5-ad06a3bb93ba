# Generated by Django 4.2.21 on 2025-07-21 16:53

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounting', '0001_initial'),
        ('project', '0001_initial'),
        ('core', '0001_initial'),
        ('inventory', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DeliveryCarrier',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('active', models.BooleanField(default=True)),
                ('delivery_type', models.CharField(choices=[('fixed', 'Fixed Price'), ('base_on_rule', 'Based on Rules')], default='fixed', max_length=50)),
                ('fixed_price', models.DecimalField(decimal_places=2, default=0.0, max_digits=16)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ProcurementGroup',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=255)),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('partner', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.partner')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ProductPricelist',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('active', models.BooleanField(default=True)),
                ('sequence', models.IntegerField(default=16)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.currency')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='StockWarehouse',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('code', models.CharField(max_length=10)),
                ('active', models.BooleanField(default=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('partner', models.ForeignKey(help_text='Address', on_delete=django.db.models.deletion.PROTECT, to='core.partner')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('code', 'company')},
            },
        ),
        migrations.CreateModel(
            name='SalesTeam',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('code', models.CharField(blank=True, max_length=10)),
                ('active', models.BooleanField(default=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('members', models.ManyToManyField(blank=True, related_name='sales_team_members', to=settings.AUTH_USER_MODEL)),
                ('user', models.ForeignKey(blank=True, help_text='Team Leader', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='SaleOrder',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(default='New', help_text='Order Reference', max_length=255)),
                ('origin', models.CharField(blank=True, help_text='Source Document', max_length=255)),
                ('client_order_ref', models.CharField(blank=True, help_text='Customer Reference', max_length=255)),
                ('date_order', models.DateTimeField(default=django.utils.timezone.now, help_text='Order Date')),
                ('validity_date', models.DateField(blank=True, help_text='Expiration Date', null=True)),
                ('commitment_date', models.DateTimeField(blank=True, help_text='Delivery Date', null=True)),
                ('state', models.CharField(choices=[('draft', 'Quotation'), ('sent', 'Quotation Sent'), ('sale', 'Sales Order'), ('cancel', 'Cancelled')], db_index=True, default='draft', max_length=20)),
                ('locked', models.BooleanField(default=False, help_text='Locked orders cannot be modified')),
                ('require_signature', models.BooleanField(default=False, help_text='Request online signature from customer')),
                ('require_payment', models.BooleanField(default=False, help_text='Request online payment from customer')),
                ('prepayment_percent', models.FloatField(default=0.0, help_text='Prepayment percentage required')),
                ('signature', models.ImageField(blank=True, help_text='Customer Signature', null=True, upload_to='signatures/')),
                ('signed_by', models.CharField(blank=True, help_text='Signed By', max_length=255)),
                ('signed_on', models.DateTimeField(blank=True, help_text='Signature Date', null=True)),
                ('reference', models.CharField(blank=True, help_text='Payment Reference', max_length=255)),
                ('amount_untaxed', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('amount_tax', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('amount_total', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('amount_undiscounted', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('invoice_status', models.CharField(choices=[('upselling', 'Upselling Opportunity'), ('invoiced', 'Fully Invoiced'), ('to invoice', 'To Invoice'), ('no', 'Nothing to Invoice')], default='no', max_length=20)),
                ('amount_to_invoice', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('amount_invoiced', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('invoice_policy', models.CharField(choices=[('order', 'Ordered quantities'), ('delivery', 'Delivered quantities')], default='order', help_text='Invoicing Policy', max_length=20)),
                ('is_subscription', models.BooleanField(default=False, help_text='Is Subscription Order')),
                ('subscription_management', models.CharField(blank=True, choices=[('create', 'Create a new subscription'), ('renew', 'Renew existing subscription'), ('upsell', 'Upsell existing subscription')], help_text='Subscription Management', max_length=20)),
                ('recurring_rule_type', models.CharField(blank=True, choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly'), ('yearly', 'Yearly')], help_text='Recurring Rule', max_length=20)),
                ('recurring_interval', models.IntegerField(default=1, help_text='Repeat every X periods')),
                ('next_invoice_date', models.DateField(blank=True, help_text='Next Invoice Date', null=True)),
                ('milestone_billing', models.BooleanField(default=False, help_text='Milestone-based billing')),
                ('approval_status', models.CharField(choices=[('draft', 'Draft'), ('pending', 'Pending Approval'), ('approved', 'Approved'), ('rejected', 'Rejected')], default='draft', help_text='Approval Status', max_length=20)),
                ('approved_date', models.DateTimeField(blank=True, help_text='Approval Date', null=True)),
                ('credit_limit_check', models.BooleanField(default=False, help_text='Check Credit Limit')),
                ('credit_limit_exceeded', models.BooleanField(default=False, help_text='Credit Limit Exceeded')),
                ('delivery_status', models.CharField(choices=[('no', 'Nothing to Deliver'), ('to_deliver', 'To Deliver'), ('partial', 'Partially Delivered'), ('done', 'Delivered')], default='no', max_length=20)),
                ('note', models.TextField(blank=True, help_text='Terms and conditions')),
                ('approved_by', models.ForeignKey(blank=True, help_text='Approved By', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_orders', to=settings.AUTH_USER_MODEL)),
                ('carrier', models.ForeignKey(blank=True, help_text='Delivery Method', null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.deliverycarrier')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.currency')),
                ('fiscal_position', models.ForeignKey(blank=True, help_text='Fiscal Position', null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountfiscalposition')),
                ('journal', models.ForeignKey(blank=True, help_text='Invoicing Journal', null=True, on_delete=django.db.models.deletion.PROTECT, to='accounting.accountjournal')),
                ('partner', models.ForeignKey(help_text='Customer', on_delete=django.db.models.deletion.PROTECT, to='core.partner')),
                ('partner_invoice', models.ForeignKey(help_text='Invoice Address', on_delete=django.db.models.deletion.PROTECT, related_name='sale_invoice_orders', to='core.partner')),
                ('partner_shipping', models.ForeignKey(help_text='Delivery Address', on_delete=django.db.models.deletion.PROTECT, related_name='sale_shipping_orders', to='core.partner')),
                ('payment_term', models.ForeignKey(blank=True, help_text='Payment Terms', null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountpaymentterm')),
                ('pricelist', models.ForeignKey(help_text='Pricelist', on_delete=django.db.models.deletion.PROTECT, to='sales.productpricelist')),
                ('procurement_group', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.procurementgroup')),
                ('team', models.ForeignKey(blank=True, help_text='Sales Team', null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.salesteam')),
                ('user', models.ForeignKey(blank=True, help_text='Salesperson', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('warehouse', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.stockwarehouse')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='ProductUomCategory',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(max_length=255)),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ProductUom',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('factor', models.DecimalField(decimal_places=6, default=1.0, max_digits=16)),
                ('factor_inv', models.DecimalField(decimal_places=6, default=1.0, max_digits=16)),
                ('uom_type', models.CharField(choices=[('bigger', 'Bigger than the reference Unit of Measure'), ('reference', 'Reference Unit of Measure for this category'), ('smaller', 'Smaller than the reference Unit of Measure')], default='reference', max_length=20)),
                ('rounding', models.DecimalField(decimal_places=6, default=0.01, max_digits=16)),
                ('active', models.BooleanField(default=True)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='sales.productuomcategory')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='SaleOrderLine',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.TextField(help_text='Description')),
                ('is_downpayment', models.BooleanField(default=False, help_text='Is a down payment line')),
                ('is_expense', models.BooleanField(default=False, help_text='Is an expense line')),
                ('product_uom_qty', models.DecimalField(decimal_places=4, default=1.0, help_text='Ordered Quantity', max_digits=16)),
                ('qty_delivered', models.DecimalField(decimal_places=4, default=0.0, help_text='Delivered Quantity', max_digits=16)),
                ('qty_invoiced', models.DecimalField(decimal_places=4, default=0.0, help_text='Invoiced Quantity', max_digits=16)),
                ('qty_to_invoice', models.DecimalField(decimal_places=4, default=0.0, help_text='To Invoice Quantity', max_digits=16)),
                ('price_unit', models.DecimalField(decimal_places=4, default=0.0, help_text='Unit Price', max_digits=20)),
                ('discount', models.DecimalField(decimal_places=2, default=0.0, help_text='Discount (%)', max_digits=16)),
                ('price_subtotal', models.DecimalField(decimal_places=2, default=0.0, help_text='Subtotal', max_digits=20)),
                ('price_tax', models.DecimalField(decimal_places=2, default=0.0, help_text='Tax Amount', max_digits=20)),
                ('price_total', models.DecimalField(decimal_places=2, default=0.0, help_text='Total', max_digits=20)),
                ('price_reduce', models.DecimalField(decimal_places=4, default=0.0, help_text='Unit Price with Discount', max_digits=20)),
                ('price_reduce_taxexcl', models.DecimalField(decimal_places=4, default=0.0, help_text='Unit Price with Discount (Tax Excluded)', max_digits=20)),
                ('customer_lead', models.IntegerField(default=0, help_text='Lead Time (days)')),
                ('product_packaging_qty', models.FloatField(default=0.0, help_text='Packaging Quantity')),
                ('qty_delivered_method', models.CharField(choices=[('manual', 'Manual'), ('analytic', 'Analytic From Expenses')], default='manual', help_text='Method to update delivered quantity', max_length=20)),
                ('sequence', models.IntegerField(default=10, help_text='Sequence')),
                ('display_type', models.CharField(blank=True, choices=[('line_section', 'Section'), ('line_note', 'Note')], help_text='Technical field for UX purpose', max_length=20, null=True)),
                ('is_milestone', models.BooleanField(default=False, help_text='Is Milestone Line')),
                ('milestone_name', models.CharField(blank=True, help_text='Milestone Name', max_length=255)),
                ('milestone_date', models.DateField(blank=True, help_text='Milestone Due Date', null=True)),
                ('milestone_reached', models.BooleanField(default=False, help_text='Milestone Reached')),
                ('milestone_percentage', models.DecimalField(decimal_places=2, default=0.0, help_text='Milestone Percentage', max_digits=5)),
                ('is_service', models.BooleanField(default=False, help_text='Is Service Line')),
                ('is_subscription_line', models.BooleanField(default=False, help_text='Is Subscription Line')),
                ('subscription_start_date', models.DateField(blank=True, help_text='Subscription Start', null=True)),
                ('subscription_end_date', models.DateField(blank=True, help_text='Subscription End', null=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.currency')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='order_line', to='sales.saleorder')),
                ('product', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='inventory.product')),
                ('product_packaging', models.ForeignKey(blank=True, help_text='Product Packaging', null=True, on_delete=django.db.models.deletion.PROTECT, to='inventory.productpackaging')),
                ('product_template', models.ForeignKey(blank=True, help_text='Product Template', null=True, on_delete=django.db.models.deletion.PROTECT, to='inventory.producttemplate')),
                ('product_uom', models.ForeignKey(blank=True, help_text='Unit of Measure', null=True, on_delete=django.db.models.deletion.PROTECT, to='sales.productuom')),
                ('tax', models.ManyToManyField(blank=True, help_text='Taxes', to='accounting.accounttax')),
                ('timesheets', models.ManyToManyField(blank=True, help_text='Related Timesheets', to='project.accountanalyticline')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'indexes': [models.Index(fields=['order'], name='sales_saleo_order_i_99a681_idx'), models.Index(fields=['sequence'], name='sales_saleo_sequenc_df64ed_idx')],
            },
        ),
        migrations.AddConstraint(
            model_name='saleorderline',
            constraint=models.CheckConstraint(check=models.Q(('display_type__isnull', False), models.Q(('product_id__isnull', False), ('product_uom__isnull', False)), _connector='OR'), name='accountable_required_fields'),
        ),
        migrations.AddConstraint(
            model_name='saleorderline',
            constraint=models.CheckConstraint(check=models.Q(('display_type__isnull', True), models.Q(('product_id__isnull', True), ('price_unit', 0), ('product_uom_qty', 0), ('product_uom__isnull', True), ('customer_lead', 0)), _connector='OR'), name='non_accountable_null_fields'),
        ),
        migrations.AddIndex(
            model_name='saleorder',
            index=models.Index(fields=['date_order'], name='sales_saleo_date_or_c2ee01_idx'),
        ),
        migrations.AddIndex(
            model_name='saleorder',
            index=models.Index(fields=['state'], name='sales_saleo_state_31bbbf_idx'),
        ),
        migrations.AddIndex(
            model_name='saleorder',
            index=models.Index(fields=['partner'], name='sales_saleo_partner_9ab797_idx'),
        ),
        migrations.AddIndex(
            model_name='saleorder',
            index=models.Index(fields=['user'], name='sales_saleo_user_id_44882d_idx'),
        ),
        migrations.AddConstraint(
            model_name='saleorder',
            constraint=models.CheckConstraint(check=models.Q(models.Q(models.Q(('date_order__isnull', False), ('state', 'sale')), models.Q(('state', 'sale'), _negated=True), _connector='OR')), name='date_order_conditional_required'),
        ),
    ]
