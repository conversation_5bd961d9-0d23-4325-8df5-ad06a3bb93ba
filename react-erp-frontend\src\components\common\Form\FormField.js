import React from 'react';
import './FormField.css';

const FormField = ({ 
  label, 
  name, 
  type = 'text', 
  value, 
  onChange, 
  placeholder,
  required = false,
  disabled = false,
  error,
  className = '',
  size = 'medium', // small, medium, large
  variant = 'underline', // underline, bordered, filled
  ...props 
}) => {
  const fieldClass = [
    'odoo-form-field',
    `odoo-form-field--${size}`,
    `odoo-form-field--${variant}`,
    error ? 'odoo-form-field--error' : '',
    disabled ? 'odoo-form-field--disabled' : '',
    className
  ].filter(Boolean).join(' ');

  const inputClass = [
    'odoo-form-input',
    `odoo-form-input--${size}`,
    `odoo-form-input--${variant}`
  ].filter(Boolean).join(' ');

  return (
    <div className={fieldClass}>
      {label && (
        <label htmlFor={name} className="odoo-form-label">
          {label}
          {required && <span className="odoo-form-required">*</span>}
        </label>
      )}
      
      <div className="odoo-form-input-wrapper">
        {type === 'select' ? (
          <select
            id={name}
            name={name}
            value={value}
            onChange={onChange}
            disabled={disabled}
            className={inputClass}
            {...props}
          >
            {props.children}
          </select>
        ) : type === 'textarea' ? (
          <textarea
            id={name}
            name={name}
            value={value}
            onChange={onChange}
            placeholder={placeholder}
            disabled={disabled}
            className={inputClass}
            {...props}
          />
        ) : (
          <input
            id={name}
            name={name}
            type={type}
            value={value}
            onChange={onChange}
            placeholder={placeholder}
            disabled={disabled}
            className={inputClass}
            {...props}
          />
        )}
      </div>
      
      {error && (
        <div className="odoo-form-error">
          {error}
        </div>
      )}
    </div>
  );
};

export default FormField;
