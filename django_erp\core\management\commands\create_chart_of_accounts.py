from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from core.models import Company
from accounting.models import AccountAccount


class Command(BaseCommand):
    help = 'Create basic chart of accounts for a company'

    def add_arguments(self, parser):
        parser.add_argument('--company-id', type=str, help='Company ID to create chart of accounts for')
        parser.add_argument('--template', type=str, default='standard', 
                          choices=['standard', 'manufacturing', 'service', 'retail'],
                          help='Chart of accounts template')

    def handle(self, *args, **options):
        company_id = options.get('company_id')
        template = options.get('template', 'standard')
        
        if company_id:
            try:
                company = Company.objects.get(id=company_id)
            except Company.DoesNotExist:
                self.stdout.write(self.style.ERROR(f'Company with ID {company_id} not found'))
                return
        else:
            company = Company.objects.first()
            if not company:
                self.stdout.write(self.style.ERROR('No company found'))
                return

        # Get admin user for create_uid and write_uid
        admin_user = User.objects.filter(is_superuser=True).first()
        if not admin_user:
            self.stdout.write(self.style.ERROR('No admin user found'))
            return

        self.stdout.write(f'Creating {template} chart of accounts for company: {company.name}')
        
        # Create account groups and accounts based on template
        if template == 'standard':
            self._create_standard_coa(company, admin_user)
        elif template == 'manufacturing':
            self._create_manufacturing_coa(company, admin_user)
        elif template == 'service':
            self._create_service_coa(company, admin_user)
        elif template == 'retail':
            self._create_retail_coa(company, admin_user)

        self.stdout.write(self.style.SUCCESS(f'Successfully created {template} chart of accounts'))

    def _create_standard_coa(self, company, user):
        """Create standard business chart of accounts using Odoo's exact account types"""

        # Assets - Current Assets
        self._create_account('101000', 'Cash', 'asset_cash', None, company, user)
        self._create_account('102000', 'Bank', 'asset_cash', None, company, user)
        self._create_account('121000', 'Account Receivable', 'asset_receivable', None, company, user)
        self._create_account('131000', 'Inventory', 'asset_current', None, company, user)
        self._create_account('141000', 'Prepayments', 'asset_prepayments', None, company, user)

        # Assets - Non-Current Assets
        self._create_account('151000', 'Fixed Assets', 'asset_non_current', None, company, user)
        self._create_account('152000', 'Accumulated Depreciation', 'asset_non_current', None, company, user)

        # Liabilities - Current Liabilities
        self._create_account('201000', 'Account Payable', 'liability_payable', None, company, user)
        self._create_account('211000', 'Tax Payable', 'liability_current', None, company, user)
        self._create_account('221000', 'Accrued Expenses', 'liability_current', None, company, user)

        # Liabilities - Non-Current Liabilities
        self._create_account('251000', 'Long-term Debt', 'liability_non_current', None, company, user)

        # Equity
        self._create_account('301000', 'Common Stock', 'equity', None, company, user)
        self._create_account('311000', 'Retained Earnings', 'equity_unaffected', None, company, user)

        # Income
        self._create_account('401000', 'Product Sales', 'income', None, company, user)
        self._create_account('402000', 'Service Sales', 'income_other', None, company, user)

        # Expenses
        self._create_account('501000', 'Cost of Revenue', 'expense', None, company, user)
        self._create_account('601000', 'Expenses', 'expense', None, company, user)
        self._create_account('602000', 'Depreciation', 'expense_depreciation', None, company, user)

    def _create_manufacturing_coa(self, company, user):
        """Create manufacturing-specific chart of accounts"""
        # Start with standard COA
        self._create_standard_coa(company, user)

        # Add manufacturing-specific accounts
        self._create_account('132000', 'Raw Materials', 'asset_current', None, company, user)
        self._create_account('133000', 'Work in Progress', 'asset_current', None, company, user)
        self._create_account('134000', 'Finished Goods', 'asset_current', None, company, user)

        # Manufacturing expense accounts
        self._create_account('502000', 'Direct Materials', 'expense_direct_cost', None, company, user)
        self._create_account('503000', 'Direct Labor', 'expense_direct_cost', None, company, user)
        self._create_account('504000', 'Manufacturing Overhead', 'expense_direct_cost', None, company, user)

    def _create_service_coa(self, company, user):
        """Create service company chart of accounts"""
        # Start with standard COA but skip inventory
        self._create_standard_coa(company, user)

        # Remove inventory account for service companies
        try:
            inventory_account = AccountAccount.objects.get(code='131000', company=company)
            inventory_account.active = False
            inventory_account.save()
        except AccountAccount.DoesNotExist:
            pass

    def _create_retail_coa(self, company, user):
        """Create retail/trading chart of accounts"""
        # Start with standard COA
        self._create_standard_coa(company, user)

        # Add retail-specific accounts
        self._create_account('403000', 'Retail Sales', 'income', None, company, user)
        self._create_account('404000', 'Online Sales', 'income', None, company, user)

        # Retail expense accounts
        self._create_account('603000', 'Store Rent', 'expense', None, company, user)
        self._create_account('604000', 'Utilities', 'expense', None, company, user)

    def _create_account(self, code, name, account_type, group, company, user):
        """Create an account"""
        account, created = AccountAccount.objects.get_or_create(
            code=code,
            company=company,
            defaults={
                'name': name,
                'account_type': account_type,
                'active': True,
                'create_uid': user,
                'write_uid': user
            }
        )
        if created:
            self.stdout.write(f'    Created account: {code} - {name}')
        return account
