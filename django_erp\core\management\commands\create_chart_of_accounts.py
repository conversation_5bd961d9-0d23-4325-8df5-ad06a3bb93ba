from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from core.models import Company
from accounting.models import AccountGroup, Account


class Command(BaseCommand):
    help = 'Create basic chart of accounts for a company'

    def add_arguments(self, parser):
        parser.add_argument('--company-id', type=str, help='Company ID to create chart of accounts for')
        parser.add_argument('--template', type=str, default='standard', 
                          choices=['standard', 'manufacturing', 'service', 'retail'],
                          help='Chart of accounts template')

    def handle(self, *args, **options):
        company_id = options.get('company_id')
        template = options.get('template', 'standard')
        
        if company_id:
            try:
                company = Company.objects.get(id=company_id)
            except Company.DoesNotExist:
                self.stdout.write(self.style.ERROR(f'Company with ID {company_id} not found'))
                return
        else:
            company = Company.objects.first()
            if not company:
                self.stdout.write(self.style.ERROR('No company found'))
                return

        # Get admin user for create_uid and write_uid
        admin_user = User.objects.filter(is_superuser=True).first()
        if not admin_user:
            self.stdout.write(self.style.ERROR('No admin user found'))
            return

        self.stdout.write(f'Creating {template} chart of accounts for company: {company.name}')
        
        # Create account groups and accounts based on template
        if template == 'standard':
            self._create_standard_coa(company, admin_user)
        elif template == 'manufacturing':
            self._create_manufacturing_coa(company, admin_user)
        elif template == 'service':
            self._create_service_coa(company, admin_user)
        elif template == 'retail':
            self._create_retail_coa(company, admin_user)

        self.stdout.write(self.style.SUCCESS(f'Successfully created {template} chart of accounts'))

    def _create_standard_coa(self, company, user):
        """Create standard business chart of accounts"""
        
        # Assets
        assets_group = self._create_group('ASSETS', 'Assets', 'asset', None, company, user)
        current_assets = self._create_group('CURRENT_ASSETS', 'Current Assets', 'asset', assets_group, company, user)
        fixed_assets = self._create_group('FIXED_ASSETS', 'Fixed Assets', 'asset', assets_group, company, user)
        
        # Current Assets Accounts
        self._create_account('1000', 'Cash', 'asset', current_assets, company, user)
        self._create_account('1100', 'Bank Account', 'asset', current_assets, company, user)
        self._create_account('1200', 'Accounts Receivable', 'receivable', current_assets, company, user)
        self._create_account('1300', 'Inventory', 'asset', current_assets, company, user)
        
        # Fixed Assets Accounts
        self._create_account('1500', 'Equipment', 'asset', fixed_assets, company, user)
        self._create_account('1600', 'Accumulated Depreciation', 'asset', fixed_assets, company, user)
        
        # Liabilities
        liabilities_group = self._create_group('LIABILITIES', 'Liabilities', 'liability', None, company, user)
        current_liabilities = self._create_group('CURRENT_LIABILITIES', 'Current Liabilities', 'liability', liabilities_group, company, user)
        
        # Current Liabilities Accounts
        self._create_account('2000', 'Accounts Payable', 'payable', current_liabilities, company, user)
        self._create_account('2100', 'Tax Payable', 'liability', current_liabilities, company, user)
        self._create_account('2200', 'Accrued Expenses', 'liability', current_liabilities, company, user)
        
        # Equity
        equity_group = self._create_group('EQUITY', 'Equity', 'equity', None, company, user)
        self._create_account('3000', 'Owner\'s Equity', 'equity', equity_group, company, user)
        self._create_account('3100', 'Retained Earnings', 'equity', equity_group, company, user)
        
        # Revenue
        revenue_group = self._create_group('REVENUE', 'Revenue', 'income', None, company, user)
        self._create_account('4000', 'Sales Revenue', 'income', revenue_group, company, user)
        self._create_account('4100', 'Service Revenue', 'income', revenue_group, company, user)
        
        # Expenses
        expenses_group = self._create_group('EXPENSES', 'Expenses', 'expense', None, company, user)
        self._create_account('5000', 'Cost of Goods Sold', 'expense', expenses_group, company, user)
        self._create_account('5100', 'Office Expenses', 'expense', expenses_group, company, user)
        self._create_account('5200', 'Marketing Expenses', 'expense', expenses_group, company, user)
        self._create_account('5300', 'Salaries and Wages', 'expense', expenses_group, company, user)

    def _create_manufacturing_coa(self, company, user):
        """Create manufacturing-specific chart of accounts"""
        # Start with standard COA
        self._create_standard_coa(company, user)
        
        # Add manufacturing-specific accounts
        current_assets = AccountGroup.objects.get(code='CURRENT_ASSETS', company=company)
        expenses_group = AccountGroup.objects.get(code='EXPENSES', company=company)
        
        # Manufacturing inventory accounts
        self._create_account('1310', 'Raw Materials', 'asset', current_assets, company, user)
        self._create_account('1320', 'Work in Progress', 'asset', current_assets, company, user)
        self._create_account('1330', 'Finished Goods', 'asset', current_assets, company, user)
        
        # Manufacturing expense accounts
        self._create_account('5010', 'Direct Materials', 'expense', expenses_group, company, user)
        self._create_account('5020', 'Direct Labor', 'expense', expenses_group, company, user)
        self._create_account('5030', 'Manufacturing Overhead', 'expense', expenses_group, company, user)

    def _create_service_coa(self, company, user):
        """Create service company chart of accounts"""
        # Start with standard COA but skip inventory
        self._create_standard_coa(company, user)
        
        # Remove inventory account for service companies
        try:
            inventory_account = Account.objects.get(code='1300', company=company)
            inventory_account.active = False
            inventory_account.save()
        except Account.DoesNotExist:
            pass

    def _create_retail_coa(self, company, user):
        """Create retail/trading chart of accounts"""
        # Start with standard COA
        self._create_standard_coa(company, user)
        
        # Add retail-specific accounts
        revenue_group = AccountGroup.objects.get(code='REVENUE', company=company)
        expenses_group = AccountGroup.objects.get(code='EXPENSES', company=company)
        
        # Retail revenue accounts
        self._create_account('4200', 'Retail Sales', 'income', revenue_group, company, user)
        self._create_account('4300', 'Online Sales', 'income', revenue_group, company, user)
        
        # Retail expense accounts
        self._create_account('5400', 'Store Rent', 'expense', expenses_group, company, user)
        self._create_account('5500', 'Utilities', 'expense', expenses_group, company, user)

    def _create_group(self, code, name, account_type, parent, company, user):
        """Create an account group"""
        group, created = AccountGroup.objects.get_or_create(
            code=code,
            company=company,
            defaults={
                'name': name,
                'account_type': account_type,
                'parent': parent,
                'active': True,
                'create_uid': user,
                'write_uid': user
            }
        )
        if created:
            self.stdout.write(f'  Created group: {code} - {name}')
        return group

    def _create_account(self, code, name, account_type, group, company, user):
        """Create an account"""
        account, created = Account.objects.get_or_create(
            code=code,
            company=company,
            defaults={
                'name': name,
                'account_type': account_type,
                'group': group,
                'active': True,
                'create_uid': user,
                'write_uid': user
            }
        )
        if created:
            self.stdout.write(f'    Created account: {code} - {name}')
        return account
