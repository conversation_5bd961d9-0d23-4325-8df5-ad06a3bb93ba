import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { accountingAPI } from '../../services/api';
import './AccountingDashboard.css';

const AccountingDashboard = ({ onNavigate }) => {
  const { user } = useAuth();
  const [stats, setStats] = useState({
    totalReceivables: 0,
    totalPayables: 0,
    bankBalance: 0,
    monthlyRevenue: 0,
    pendingInvoices: 0,
    overdueInvoices: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      // TODO: Implement API calls for dashboard stats
      // For now, using mock data
      setStats({
        totalReceivables: 125000,
        totalPayables: 85000,
        bankBalance: 245000,
        monthlyRevenue: 180000,
        pendingInvoices: 12,
        overdueInvoices: 3,
      });
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const quickActions = [
    {
      title: 'Chart of Accounts',
      icon: 'fas fa-list-alt',
      color: '#28a745',
      action: () => onNavigate('chart-of-accounts')
    },
    {
      title: 'Create Invoice',
      icon: 'fas fa-file-invoice',
      color: '#17a2b8',
      action: () => console.log('Create Invoice')
    },
    {
      title: 'Journal Entry',
      icon: 'fas fa-edit',
      color: '#ffc107',
      action: () => console.log('Journal Entry')
    },
    {
      title: 'Bank Reconciliation',
      icon: 'fas fa-university',
      color: '#6f42c1',
      action: () => console.log('Bank Reconciliation')
    }
  ];

  const reports = [
    {
      title: 'Profit & Loss',
      description: 'Income statement for current period',
      icon: 'fas fa-chart-line',
      path: '/accounting/reports/profit-loss'
    },
    {
      title: 'Balance Sheet',
      description: 'Financial position statement',
      icon: 'fas fa-balance-scale',
      path: '/accounting/reports/balance-sheet'
    },
    {
      title: 'Cash Flow',
      description: 'Cash flow statement',
      icon: 'fas fa-money-bill-wave',
      path: '/accounting/reports/cash-flow'
    },
    {
      title: 'Aged Receivables',
      description: 'Customer payment analysis',
      icon: 'fas fa-clock',
      path: '/accounting/reports/aged-receivables'
    }
  ];

  if (loading) {
    return (
      <div className="accounting-dashboard loading">
        <div className="loading-spinner">
          <i className="fas fa-spinner fa-spin"></i>
          <p>Loading accounting data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="accounting-dashboard">
      {/* Header */}
      <div className="dashboard-header">
        <div className="header-content">
          <h1>
            <i className="fas fa-calculator"></i>
            Accounting Dashboard
          </h1>
          <p>Financial overview and quick actions</p>
        </div>
        <div className="header-actions">
          <button className="btn btn-secondary" onClick={() => onNavigate('dashboard')}>
            <i className="fas fa-arrow-left"></i>
            Back to Dashboard
          </button>
          <button className="btn btn-primary">
            <i className="fas fa-plus"></i>
            New Transaction
          </button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="metrics-grid">
        <div className="metric-card receivables">
          <div className="metric-icon">
            <i className="fas fa-arrow-down"></i>
          </div>
          <div className="metric-content">
            <h3>{formatCurrency(stats.totalReceivables)}</h3>
            <p>Total Receivables</p>
            <span className="metric-change positive">+5.2%</span>
          </div>
        </div>

        <div className="metric-card payables">
          <div className="metric-icon">
            <i className="fas fa-arrow-up"></i>
          </div>
          <div className="metric-content">
            <h3>{formatCurrency(stats.totalPayables)}</h3>
            <p>Total Payables</p>
            <span className="metric-change negative">-2.1%</span>
          </div>
        </div>

        <div className="metric-card bank">
          <div className="metric-icon">
            <i className="fas fa-university"></i>
          </div>
          <div className="metric-content">
            <h3>{formatCurrency(stats.bankBalance)}</h3>
            <p>Bank Balance</p>
            <span className="metric-change positive">+8.7%</span>
          </div>
        </div>

        <div className="metric-card revenue">
          <div className="metric-icon">
            <i className="fas fa-chart-line"></i>
          </div>
          <div className="metric-content">
            <h3>{formatCurrency(stats.monthlyRevenue)}</h3>
            <p>Monthly Revenue</p>
            <span className="metric-change positive">+12.3%</span>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="section">
        <h2>Quick Actions</h2>
        <div className="quick-actions-grid">
          {quickActions.map((action, index) => (
            <div
              key={index}
              className="quick-action-card"
              style={{ borderLeftColor: action.color }}
              onClick={action.action}
            >
              <div className="action-icon" style={{ color: action.color }}>
                <i className={action.icon}></i>
              </div>
              <div className="action-content">
                <h4>{action.title}</h4>
                <button className="btn btn-sm btn-outline">
                  <i className="fas fa-arrow-right"></i>
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Reports Section */}
      <div className="section">
        <h2>Financial Reports</h2>
        <div className="reports-grid">
          {reports.map((report, index) => (
            <div key={index} className="report-card">
              <div className="report-icon">
                <i className={report.icon}></i>
              </div>
              <div className="report-content">
                <h4>{report.title}</h4>
                <p>{report.description}</p>
                <button className="btn btn-sm btn-primary">
                  View Report
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Activity */}
      <div className="section">
        <h2>Recent Activity</h2>
        <div className="activity-list">
          <div className="activity-item">
            <div className="activity-icon invoice">
              <i className="fas fa-file-invoice"></i>
            </div>
            <div className="activity-content">
              <h4>Invoice INV-2024-001 created</h4>
              <p>Customer: ABC Corp - Amount: {formatCurrency(15000)}</p>
              <span className="activity-time">2 hours ago</span>
            </div>
          </div>

          <div className="activity-item">
            <div className="activity-icon payment">
              <i className="fas fa-credit-card"></i>
            </div>
            <div className="activity-content">
              <h4>Payment received</h4>
              <p>From: XYZ Ltd - Amount: {formatCurrency(8500)}</p>
              <span className="activity-time">4 hours ago</span>
            </div>
          </div>

          <div className="activity-item">
            <div className="activity-icon journal">
              <i className="fas fa-edit"></i>
            </div>
            <div className="activity-content">
              <h4>Journal entry posted</h4>
              <p>Entry: JE-2024-045 - Office Expenses</p>
              <span className="activity-time">1 day ago</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AccountingDashboard;
