# Generated by Django 4.2.21 on 2025-07-21 16:30

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProjectMilestone',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(help_text='Milestone Name', max_length=255)),
                ('deadline', models.DateField(help_text='Deadline')),
                ('is_reached', models.<PERSON><PERSON>anField(default=False, help_text='Reached')),
                ('description', models.TextField(blank=True, help_text='Description')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='ProjectProject',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(help_text='Project Name', max_length=255)),
                ('active', models.BooleanField(default=True)),
                ('sequence', models.IntegerField(default=10)),
                ('description', models.TextField(blank=True, help_text='Project Description')),
                ('privacy_visibility', models.CharField(choices=[('followers', 'Invited internal users'), ('employees', 'All internal users'), ('portal', 'Invited portal users and all internal users')], default='employees', help_text='Privacy', max_length=20)),
                ('date_start', models.DateField(blank=True, help_text='Start Date', null=True)),
                ('date', models.DateField(blank=True, help_text='Expiration Date', null=True)),
                ('allow_timesheets', models.BooleanField(default=True, help_text='Timesheets')),
                ('allow_billable_hours', models.BooleanField(default=False, help_text='Bill from Tasks')),
                ('allow_material', models.BooleanField(default=False, help_text='Bill Materials')),
                ('allow_milestones', models.BooleanField(default=False, help_text='Milestones')),
                ('allow_forecast', models.BooleanField(default=False, help_text='Planning')),
                ('analytic_account', models.CharField(blank=True, help_text='Analytic Account', max_length=255)),
                ('is_favorite', models.BooleanField(default=False)),
                ('color', models.IntegerField(default=0, help_text='Color Index')),
                ('task_count', models.IntegerField(default=0, help_text='Task Count')),
                ('rating_request_deadline', models.DateTimeField(blank=True, null=True)),
                ('portal_show_rating', models.BooleanField(default=False)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('favorite_users', models.ManyToManyField(blank=True, related_name='favorite_projects', to=settings.AUTH_USER_MODEL)),
                ('partner', models.ForeignKey(blank=True, help_text='Customer', null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.partner')),
            ],
        ),
        migrations.CreateModel(
            name='ProjectTags',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(help_text='Tag Name', max_length=255, unique=True)),
                ('color', models.IntegerField(default=0, help_text='Color Index')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ProjectTaskType',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(help_text='Stage Name', max_length=255)),
                ('description', models.TextField(blank=True, help_text='Description')),
                ('sequence', models.IntegerField(default=1, help_text='Sequence')),
                ('fold', models.BooleanField(default=False, help_text='Folded in Kanban')),
                ('rating_template', models.CharField(blank=True, max_length=255)),
                ('auto_validation_kanban_state', models.BooleanField(default=False)),
                ('mail_template', models.CharField(blank=True, max_length=255)),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('projects', models.ManyToManyField(blank=True, help_text='Projects', to='project.projectproject')),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['sequence', 'name'],
            },
        ),
        migrations.CreateModel(
            name='ProjectTask',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(help_text='Task Title', max_length=255)),
                ('active', models.BooleanField(default=True)),
                ('sequence', models.IntegerField(default=10)),
                ('priority', models.CharField(choices=[('0', 'Normal'), ('1', 'High')], default='0', max_length=1)),
                ('date_assign', models.DateTimeField(blank=True, help_text='Assigning Date', null=True)),
                ('date_deadline', models.DateField(blank=True, help_text='Deadline', null=True)),
                ('date_last_stage_update', models.DateTimeField(auto_now=True)),
                ('description', models.TextField(blank=True, help_text='Description')),
                ('notes', models.TextField(blank=True, help_text='Extra Notes')),
                ('kanban_state', models.CharField(choices=[('normal', 'In Progress'), ('done', 'Ready'), ('blocked', 'Blocked')], default='normal', max_length=20)),
                ('kanban_state_label', models.CharField(blank=True, max_length=255)),
                ('planned_hours', models.FloatField(default=0.0, help_text='Initially Planned Hours')),
                ('effective_hours', models.FloatField(default=0.0, help_text='Hours Spent')),
                ('remaining_hours', models.FloatField(default=0.0, help_text='Remaining Hours')),
                ('progress', models.FloatField(default=0.0, help_text='Progress (%)')),
                ('overtime', models.FloatField(default=0.0, help_text='Overtime')),
                ('subtask_effective_hours', models.FloatField(default=0.0)),
                ('subtask_planned_hours', models.FloatField(default=0.0)),
                ('email_from', models.EmailField(blank=True, help_text='Email', max_length=254)),
                ('email_cc', models.TextField(blank=True, help_text='Watchers Emails')),
                ('color', models.IntegerField(default=0, help_text='Color Index')),
                ('displayed_image', models.CharField(blank=True, max_length=255)),
                ('legend_blocked', models.CharField(default='Blocked', max_length=255)),
                ('legend_done', models.CharField(default='Ready', max_length=255)),
                ('legend_normal', models.CharField(default='In Progress', max_length=255)),
                ('portal_user_names', models.TextField(blank=True)),
                ('childs', models.ManyToManyField(blank=True, related_name='parent_tasks', to='project.projecttask')),
                ('commercial_partner', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='commercial_tasks', to='core.partner')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('depend_ons', models.ManyToManyField(blank=True, related_name='dependent_tasks', to='project.projecttask')),
                ('milestone', models.ForeignKey(blank=True, help_text='Milestone', null=True, on_delete=django.db.models.deletion.SET_NULL, to='project.projectmilestone')),
                ('parent', models.ForeignKey(blank=True, help_text='Parent Task', null=True, on_delete=django.db.models.deletion.CASCADE, to='project.projecttask')),
                ('partner', models.ForeignKey(blank=True, help_text='Customer', null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.partner')),
                ('project', models.ForeignKey(help_text='Project', on_delete=django.db.models.deletion.CASCADE, related_name='task_ids_related', to='project.projectproject')),
                ('stage', models.ForeignKey(blank=True, help_text='Stage', null=True, on_delete=django.db.models.deletion.SET_NULL, to='project.projecttasktype')),
                ('tags', models.ManyToManyField(blank=True, to='project.projecttags')),
                ('users', models.ManyToManyField(blank=True, help_text='Assignees', related_name='tasks', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddField(
            model_name='projectproject',
            name='tags',
            field=models.ManyToManyField(blank=True, to='project.projecttags'),
        ),
        migrations.AddField(
            model_name='projectproject',
            name='tasks',
            field=models.ManyToManyField(blank=True, related_name='projects_m2m', to='project.projecttask'),
        ),
        migrations.AddField(
            model_name='projectproject',
            name='user',
            field=models.ForeignKey(blank=True, help_text='Project Manager', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='projectproject',
            name='write_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='projectmilestone',
            name='project',
            field=models.ForeignKey(help_text='Project', on_delete=django.db.models.deletion.CASCADE, related_name='milestones', to='project.projectproject'),
        ),
        migrations.AddField(
            model_name='projectmilestone',
            name='write_uid',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='AccountAnalyticTag',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(help_text='Tag Name', max_length=255, unique=True)),
                ('color', models.IntegerField(default=0, help_text='Color Index')),
                ('active', models.BooleanField(default=True)),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='AccountAnalyticLine',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('name', models.CharField(help_text='Description', max_length=255)),
                ('date', models.DateField(default=django.utils.timezone.now, help_text='Date')),
                ('unit_amount', models.FloatField(default=0.0, help_text='Quantity')),
                ('product_uom', models.CharField(default='Hours', help_text='Unit of Measure', max_length=255)),
                ('employee', models.CharField(blank=True, help_text='Employee', max_length=255)),
                ('amount', models.DecimalField(decimal_places=2, default=0.0, help_text='Amount', max_digits=20)),
                ('account', models.CharField(blank=True, help_text='Analytic Account', max_length=255)),
                ('general_account', models.CharField(blank=True, help_text='General Account', max_length=255)),
                ('validated', models.BooleanField(default=False, help_text='Validated')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.company')),
                ('create_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('currency', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.currency')),
                ('partner', models.ForeignKey(blank=True, help_text='Customer', null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.partner')),
                ('project', models.ForeignKey(help_text='Project', on_delete=django.db.models.deletion.CASCADE, related_name='timesheets', to='project.projectproject')),
                ('tags', models.ManyToManyField(blank=True, to='project.accountanalytictag')),
                ('task', models.ForeignKey(blank=True, help_text='Task', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='timesheets', to='project.projecttask')),
                ('user', models.ForeignKey(help_text='User', on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('write_uid', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_modified', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddIndex(
            model_name='projecttasktype',
            index=models.Index(fields=['sequence'], name='project_pro_sequenc_647105_idx'),
        ),
        migrations.AddIndex(
            model_name='projecttasktype',
            index=models.Index(fields=['name'], name='project_pro_name_3a2361_idx'),
        ),
        migrations.AddIndex(
            model_name='projecttask',
            index=models.Index(fields=['name'], name='project_pro_name_6fa234_idx'),
        ),
        migrations.AddIndex(
            model_name='projecttask',
            index=models.Index(fields=['project'], name='project_pro_project_041503_idx'),
        ),
        migrations.AddIndex(
            model_name='projecttask',
            index=models.Index(fields=['stage'], name='project_pro_stage_i_60aa90_idx'),
        ),
        migrations.AddIndex(
            model_name='projecttask',
            index=models.Index(fields=['priority'], name='project_pro_priorit_dd2889_idx'),
        ),
        migrations.AddIndex(
            model_name='projecttask',
            index=models.Index(fields=['date_deadline'], name='project_pro_date_de_ebddba_idx'),
        ),
        migrations.AddIndex(
            model_name='projecttask',
            index=models.Index(fields=['kanban_state'], name='project_pro_kanban__e6fd87_idx'),
        ),
        migrations.AddIndex(
            model_name='projecttask',
            index=models.Index(fields=['active'], name='project_pro_active_69d141_idx'),
        ),
        migrations.AddIndex(
            model_name='projecttask',
            index=models.Index(fields=['sequence'], name='project_pro_sequenc_8e03af_idx'),
        ),
        migrations.AddIndex(
            model_name='projecttask',
            index=models.Index(fields=['parent'], name='project_pro_parent__4380ce_idx'),
        ),
        migrations.AddConstraint(
            model_name='projecttask',
            constraint=models.CheckConstraint(check=models.Q(('planned_hours__gte', 0)), name='project_task_positive_planned_hours'),
        ),
        migrations.AddConstraint(
            model_name='projecttask',
            constraint=models.CheckConstraint(check=models.Q(('effective_hours__gte', 0)), name='project_task_positive_effective_hours'),
        ),
        migrations.AddConstraint(
            model_name='projecttask',
            constraint=models.CheckConstraint(check=models.Q(('progress__gte', 0), ('progress__lte', 100)), name='project_task_progress_range'),
        ),
        migrations.AddIndex(
            model_name='projectproject',
            index=models.Index(fields=['name'], name='project_pro_name_91c794_idx'),
        ),
        migrations.AddIndex(
            model_name='projectproject',
            index=models.Index(fields=['user'], name='project_pro_user_id_9e1d7e_idx'),
        ),
        migrations.AddIndex(
            model_name='projectproject',
            index=models.Index(fields=['partner'], name='project_pro_partner_988095_idx'),
        ),
        migrations.AddIndex(
            model_name='projectproject',
            index=models.Index(fields=['company'], name='project_pro_company_9897d8_idx'),
        ),
        migrations.AddIndex(
            model_name='projectproject',
            index=models.Index(fields=['active'], name='project_pro_active_9151b0_idx'),
        ),
        migrations.AddIndex(
            model_name='projectproject',
            index=models.Index(fields=['sequence'], name='project_pro_sequenc_ecdf28_idx'),
        ),
        migrations.AddConstraint(
            model_name='projectproject',
            constraint=models.UniqueConstraint(fields=('name', 'company'), name='project_project_name_company_uniq'),
        ),
        migrations.AddIndex(
            model_name='projectmilestone',
            index=models.Index(fields=['project'], name='project_pro_project_fc2924_idx'),
        ),
        migrations.AddIndex(
            model_name='projectmilestone',
            index=models.Index(fields=['deadline'], name='project_pro_deadlin_927164_idx'),
        ),
        migrations.AddIndex(
            model_name='projectmilestone',
            index=models.Index(fields=['is_reached'], name='project_pro_is_reac_a8fec8_idx'),
        ),
        migrations.AddConstraint(
            model_name='projectmilestone',
            constraint=models.UniqueConstraint(fields=('name', 'project'), name='project_milestone_name_project_uniq'),
        ),
        migrations.AddIndex(
            model_name='accountanalyticline',
            index=models.Index(fields=['date'], name='project_acc_date_9b6d62_idx'),
        ),
        migrations.AddIndex(
            model_name='accountanalyticline',
            index=models.Index(fields=['project'], name='project_acc_project_961852_idx'),
        ),
        migrations.AddIndex(
            model_name='accountanalyticline',
            index=models.Index(fields=['task'], name='project_acc_task_id_d5c466_idx'),
        ),
        migrations.AddIndex(
            model_name='accountanalyticline',
            index=models.Index(fields=['user'], name='project_acc_user_id_3f7914_idx'),
        ),
        migrations.AddIndex(
            model_name='accountanalyticline',
            index=models.Index(fields=['company'], name='project_acc_company_cab7a3_idx'),
        ),
        migrations.AddConstraint(
            model_name='accountanalyticline',
            constraint=models.CheckConstraint(check=models.Q(('unit_amount__gte', 0)), name='account_analytic_line_positive_amount'),
        ),
    ]
