/* Odoo Typography System - Exact Match */

/* Import Odoo's actual fonts */
@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@200;300;400;500&display=swap');

/* Typography Variables */
:root {
  /* Font Families */
  --odoo-font-family-primary: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --odoo-font-family-mono: 'Roboto Mono', 'SF Mono', Monaco, Inconsolata, 'Courier New', monospace;
  
  /* Font Weights - Ultra Light for Odoo Match */
  --odoo-font-weight-thin: 100;
  --odoo-font-weight-light: 200;
  --odoo-font-weight-normal: 300;
  --odoo-font-weight-medium: 400;
  --odoo-font-weight-semibold: 500;
  --odoo-font-weight-bold: 600;
  --odoo-font-weight-heavy: 700;
  
  /* Font Sizes - Odoo Scale */
  --odoo-font-size-xs: 0.75rem;    /* 12px - Small labels */
  --odoo-font-size-sm: 0.875rem;   /* 14px - Regular labels */
  --odoo-font-size-base: 1rem;     /* 16px - Body text */
  --odoo-font-size-lg: 1.125rem;   /* 18px - Subheadings */
  --odoo-font-size-xl: 1.25rem;    /* 20px - Small headings */
  --odoo-font-size-2xl: 1.5rem;    /* 24px - Medium headings */
  --odoo-font-size-3xl: 1.875rem;  /* 30px - Large headings */
  --odoo-font-size-4xl: 2.25rem;   /* 36px - Hero headings */
  
  /* Line Heights */
  --odoo-line-height-tight: 1.25;
  --odoo-line-height-normal: 1.5;
  --odoo-line-height-relaxed: 1.75;
  
  /* Letter Spacing */
  --odoo-letter-spacing-tight: -0.025em;
  --odoo-letter-spacing-normal: 0;
  --odoo-letter-spacing-wide: 0.025em;
}

/* Base Typography */
body {
  font-family: var(--odoo-font-family-primary);
  font-weight: var(--odoo-font-weight-normal);
  font-size: var(--odoo-font-size-base);
  line-height: var(--odoo-line-height-normal);
  color: var(--odoo-text-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Heading Hierarchy - Odoo Style */
.odoo-heading-1,
.odoo-h1 {
  font-size: var(--odoo-font-size-3xl);
  font-weight: var(--odoo-font-weight-light);
  line-height: var(--odoo-line-height-tight);
  color: var(--odoo-text-primary);
  margin-bottom: 1rem;
  letter-spacing: var(--odoo-letter-spacing-tight);
}

.odoo-heading-2,
.odoo-h2 {
  font-size: var(--odoo-font-size-2xl);
  font-weight: var(--odoo-font-weight-normal);
  line-height: var(--odoo-line-height-tight);
  color: var(--odoo-text-primary);
  margin-bottom: 0.75rem;
}

.odoo-heading-3,
.odoo-h3 {
  font-size: var(--odoo-font-size-xl);
  font-weight: var(--odoo-font-weight-medium);
  line-height: var(--odoo-line-height-normal);
  color: var(--odoo-text-primary);
  margin-bottom: 0.5rem;
}

.odoo-heading-4,
.odoo-h4 {
  font-size: var(--odoo-font-size-lg);
  font-weight: var(--odoo-font-weight-medium);
  line-height: var(--odoo-line-height-normal);
  color: var(--odoo-text-secondary);
  margin-bottom: 0.5rem;
}

/* Focus Areas - Primary Content */
.odoo-focus-primary {
  font-size: var(--odoo-font-size-2xl);
  font-weight: var(--odoo-font-weight-light);
  color: var(--odoo-text-primary);
  line-height: var(--odoo-line-height-tight);
  letter-spacing: var(--odoo-letter-spacing-tight);
}

.odoo-focus-secondary {
  font-size: var(--odoo-font-size-xl);
  font-weight: var(--odoo-font-weight-normal);
  color: var(--odoo-text-primary);
  line-height: var(--odoo-line-height-normal);
}

/* Field Labels - Subtle */
.odoo-label {
  font-size: var(--odoo-font-size-sm);
  font-weight: var(--odoo-font-weight-medium);
  color: var(--odoo-text-secondary);
  text-transform: none;
  letter-spacing: var(--odoo-letter-spacing-normal);
  margin-bottom: 0.25rem;
}

.odoo-label-small {
  font-size: var(--odoo-font-size-xs);
  font-weight: var(--odoo-font-weight-medium);
  color: var(--odoo-text-muted);
  text-transform: uppercase;
  letter-spacing: var(--odoo-letter-spacing-wide);
}

/* Numbers and Digits - Monospace */
.odoo-number,
.odoo-digits {
  font-family: var(--odoo-font-family-mono);
  font-weight: var(--odoo-font-weight-light);
  font-variant-numeric: tabular-nums;
  letter-spacing: var(--odoo-letter-spacing-normal);
}

.odoo-number-large {
  font-size: var(--odoo-font-size-2xl);
  font-weight: var(--odoo-font-weight-light);
}

.odoo-number-medium {
  font-size: var(--odoo-font-size-xl);
  font-weight: var(--odoo-font-weight-light);
}

.odoo-number-small {
  font-size: var(--odoo-font-size-base);
  font-weight: var(--odoo-font-weight-normal);
}

/* Code Fields - Special Monospace */
.odoo-code {
  font-family: var(--odoo-font-family-mono);
  font-weight: var(--odoo-font-weight-light);
  font-size: var(--odoo-font-size-xl);
  color: var(--odoo-text-primary);
  font-variant-numeric: tabular-nums;
  letter-spacing: 0.05em;
}

/* Body Text Variants */
.odoo-text-body {
  font-size: var(--odoo-font-size-base);
  font-weight: var(--odoo-font-weight-normal);
  line-height: var(--odoo-line-height-normal);
  color: var(--odoo-text-primary);
}

.odoo-text-small {
  font-size: var(--odoo-font-size-sm);
  font-weight: var(--odoo-font-weight-normal);
  color: var(--odoo-text-secondary);
}

.odoo-text-muted {
  color: var(--odoo-text-muted);
  font-weight: var(--odoo-font-weight-normal);
}

/* Interactive Text */
.odoo-link {
  color: var(--odoo-primary);
  text-decoration: none;
  font-weight: var(--odoo-font-weight-medium);
  transition: color 0.15s ease;
}

.odoo-link:hover {
  color: var(--odoo-primary-hover);
  text-decoration: underline;
}

/* Status and Badge Text */
.odoo-badge-text {
  font-size: var(--odoo-font-size-xs);
  font-weight: var(--odoo-font-weight-semibold);
  text-transform: uppercase;
  letter-spacing: var(--odoo-letter-spacing-wide);
}

/* Form Input Text Styles */
.odoo-input-text {
  font-family: var(--odoo-font-family-primary);
  font-weight: var(--odoo-font-weight-normal);
  font-size: var(--odoo-font-size-base);
  color: var(--odoo-text-primary);
}

.odoo-input-large {
  font-size: var(--odoo-font-size-xl);
  font-weight: var(--odoo-font-weight-light);
}

.odoo-input-code {
  font-family: var(--odoo-font-family-mono);
  font-weight: var(--odoo-font-weight-light);
  font-variant-numeric: tabular-nums;
}

/* Responsive Typography */
@media (max-width: 768px) {
  .odoo-heading-1, .odoo-h1 {
    font-size: var(--odoo-font-size-2xl);
  }
  
  .odoo-heading-2, .odoo-h2 {
    font-size: var(--odoo-font-size-xl);
  }
  
  .odoo-focus-primary {
    font-size: var(--odoo-font-size-xl);
  }
  
  .odoo-code {
    font-size: var(--odoo-font-size-lg);
  }
}

/* Utility Classes */
.odoo-font-thin { font-weight: var(--odoo-font-weight-thin); }
.odoo-font-light { font-weight: var(--odoo-font-weight-light); }
.odoo-font-normal { font-weight: var(--odoo-font-weight-normal); }
.odoo-font-medium { font-weight: var(--odoo-font-weight-medium); }
.odoo-font-semibold { font-weight: var(--odoo-font-weight-semibold); }
.odoo-font-bold { font-weight: var(--odoo-font-weight-bold); }

.odoo-font-mono { font-family: var(--odoo-font-family-mono); }
.odoo-font-primary { font-family: var(--odoo-font-family-primary); }
