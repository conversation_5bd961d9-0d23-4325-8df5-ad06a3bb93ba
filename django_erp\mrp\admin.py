from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import (
    MrpBom, MrpBomLine, MrpWorkcenter, MrpRouting, MrpRoutingWorkcenter,
    MrpProduction, MrpWorkorder, MrpByproduct, MrpUnbuild,
    ProductAttribute, ProductAttributeValue, QualityPoint, HrSkill
)


# ===== BOM ADMINISTRATION =====

class MrpBomLineInline(admin.TabularInline):
    model = MrpBomLine
    extra = 1
    fields = ('sequence', 'product', 'product_qty', 'product_uom', 'operation')
    ordering = ('sequence',)


class MrpByproductInline(admin.TabularInline):
    model = MrpByproduct
    extra = 0
    fields = ('sequence', 'product', 'product_qty', 'product_uom', 'cost_share')
    ordering = ('sequence',)


@admin.register(MrpBom)
class MrpBomAdmin(admin.ModelAdmin):
    list_display = ('product_tmpl', 'product', 'product_qty', 'product_uom', 
                   'type', 'active', 'company')
    list_filter = ('type', 'active', 'company', 'create_date')
    search_fields = ('product_tmpl_id__name', 'product_id__name', 'code')
    ordering = ('sequence', 'product_tmpl_id__name')
    
    fieldsets = (
        ('Product Information', {
            'fields': ('product_tmpl', 'product', 'product_qty', 'product_uom')
        }),
        ('BOM Configuration', {
            'fields': ('type', 'code', 'version', 'routing', 'ready_to_produce')
        }),
        ('Settings', {
            'fields': ('active', 'sequence', 'consumption', 'company')
        }),
        ('Subcontracting', {
            'fields': ('subcontractors',),
            'classes': ('collapse',)
        }),
    )
    
    inlines = [MrpBomLineInline, MrpByproductInline]
    filter_horizontal = ('subcontractors',)
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'product_tmpl', 'product', 'product_uom', 'routing', 'company'
        )


# ===== WORK CENTER ADMINISTRATION =====

@admin.register(MrpWorkcenter)
class MrpWorkcenterAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'capacity', 'time_efficiency', 'costs_hour', 
                   'active', 'company')
    list_filter = ('active', 'company')
    search_fields = ('name', 'code')
    ordering = ('sequence', 'name')
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'code', 'active', 'sequence', 'color')
        }),
        ('Capacity & Time', {
            'fields': ('capacity', 'time_efficiency', 'default_capacity')
        }),
        ('Costs & Time', {
            'fields': ('costs_hour', 'time_start', 'time_stop')
        }),
        ('OEE', {
            'fields': ('oee_target',),
            'classes': ('collapse',)
        }),
        ('Company', {
            'fields': ('company',)
        }),
    )


# ===== ROUTING ADMINISTRATION =====

class MrpRoutingWorkcenterInline(admin.TabularInline):
    model = MrpRoutingWorkcenter
    extra = 1
    fields = ('sequence', 'name', 'workcenter', 'time_cycle_manual', 'time_mode')
    ordering = ('sequence',)


@admin.register(MrpRouting)
class MrpRoutingAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'active', 'company')
    list_filter = ('active', 'company')
    search_fields = ('name', 'code')
    ordering = ('name',)
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'code', 'active', 'note')
        }),
        ('Company', {
            'fields': ('company',)
        }),
    )
    
    inlines = [MrpRoutingWorkcenterInline]


@admin.register(MrpRoutingWorkcenter)
class MrpRoutingWorkcenterAdmin(admin.ModelAdmin):
    list_display = ('name', 'routing', 'workcenter', 'sequence', 'time_cycle')
    list_filter = ('routing', 'workcenter', 'time_mode')
    search_fields = ('name', 'routing_id__name', 'workcenter_id__name')
    ordering = ('routing', 'sequence')
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('routing', 'name', 'workcenter', 'sequence', 'note')
        }),
        ('Time Configuration', {
            'fields': ('time_mode', 'time_mode_batch', 'time_cycle_manual', 'time_cycle')
        }),
        ('Batch Configuration', {
            'fields': ('batch', 'batch_size'),
            'classes': ('collapse',)
        }),
        ('Quality & Skills', {
            'fields': ('quality_points', 'skills'),
            'classes': ('collapse',)
        }),
    )
    
    filter_horizontal = ('quality_points', 'skills')


# ===== MANUFACTURING ORDER ADMINISTRATION =====

class MrpWorkorderInline(admin.TabularInline):
    model = MrpWorkorder
    extra = 0
    fields = ('name', 'workcenter', 'state', 'qty_produced', 'date_planned_start')
    readonly_fields = ('name', 'state', 'qty_produced')
    ordering = ('operation_id__sequence',)


@admin.register(MrpProduction)
class MrpProductionAdmin(admin.ModelAdmin):
    list_display = ('name', 'product', 'product_qty', 'state', 'priority', 
                   'date_planned_start', 'user')
    list_filter = ('state', 'priority', 'company', 'date_planned_start')
    search_fields = ('name', 'product_id__name', 'origin')
    ordering = ('-date_planned_start', 'priority')
    date_hierarchy = 'date_planned_start'
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'origin', 'state', 'priority')
        }),
        ('Product Information', {
            'fields': ('product', 'product_tmpl', 'product_qty', 'product_uom',
                      'qty_producing', 'qty_produced')
        }),
        ('Manufacturing', {
            'fields': ('bom', 'routing', 'picking_type')
        }),
        ('Dates', {
            'fields': ('date_planned_start', 'date_planned_finished', 'date_deadline',
                      'date_start', 'date_finished')
        }),
        ('Locations', {
            'fields': ('location_src', 'location_dest')
        }),
        ('Planning', {
            'fields': ('procurement_group', 'orderpoint', 'reservation_state'),
            'classes': ('collapse',)
        }),
        ('Analytics', {
            'fields': ('analytic_account', 'user', 'extra_cost'),
            'classes': ('collapse',)
        }),
        ('Company', {
            'fields': ('company',)
        }),
    )
    
    readonly_fields = ('qty_produced', 'reservation_state', 'date_start', 'date_finished')
    inlines = [MrpWorkorderInline]
    
    actions = ['action_confirm', 'action_assign', 'action_mark_done']
    
    def action_confirm(self, request, queryset):
        for production in queryset:
            if production.state == 'draft':
                production.action_confirm()
        self.message_user(request, f"Confirmed {queryset.count()} manufacturing orders.")
    action_confirm.short_description = "Confirm selected manufacturing orders"
    
    def action_assign(self, request, queryset):
        for production in queryset:
            if production.state in ['confirmed', 'progress']:
                production.action_assign()
        self.message_user(request, f"Reserved materials for {queryset.count()} manufacturing orders.")
    action_assign.short_description = "Reserve materials for selected orders"
    
    def action_mark_done(self, request, queryset):
        for production in queryset:
            if production.state in ['progress', 'to_close']:
                try:
                    production.button_mark_done()
                except Exception as e:
                    self.message_user(request, f"Error marking {production.name} as done: {e}", level='ERROR')
        self.message_user(request, f"Marked {queryset.count()} manufacturing orders as done.")
    action_mark_done.short_description = "Mark selected orders as done"
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'product', 'product_tmpl', 'product_uom', 'bom', 
            'routing', 'company', 'user'
        )


# ===== WORK ORDER ADMINISTRATION =====

@admin.register(MrpWorkorder)
class MrpWorkorderAdmin(admin.ModelAdmin):
    list_display = ('name', 'production', 'workcenter', 'state', 'qty_produced',
                   'date_planned_start', 'duration')
    list_filter = ('state', 'workcenter', 'date_planned_start')
    search_fields = ('name', 'production_id__name', 'workcenter_id__name')
    ordering = ('date_planned_start', 'production')
    date_hierarchy = 'date_planned_start'
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'production', 'operation', 'workcenter', 'state')
        }),
        ('Quantities', {
            'fields': ('qty_production', 'qty_producing', 'qty_produced', 'qty_remaining')
        }),
        ('Planning', {
            'fields': ('date_planned_start', 'date_planned_finished', 'date_start', 'date_finished')
        }),
        ('Time & Costs', {
            'fields': ('duration_expected', 'duration', 'duration_unit', 'duration_percent', 'costs_hour')
        }),
        ('Instructions', {
            'fields': ('worksheet_type', 'worksheet', 'note'),
            'classes': ('collapse',)
        }),
        ('Company', {
            'fields': ('company',)
        }),
    )
    
    readonly_fields = ('qty_produced', 'duration', 'duration_percent', 'date_start', 'date_finished')
    
    actions = ['action_start', 'action_finish']
    
    def action_start(self, request, queryset):
        for workorder in queryset:
            if workorder.state == 'ready':
                workorder.button_start()
        self.message_user(request, f"Started {queryset.count()} work orders.")
    action_start.short_description = "Start selected work orders"
    
    def action_finish(self, request, queryset):
        for workorder in queryset:
            if workorder.state == 'progress':
                workorder.button_finish()
        self.message_user(request, f"Finished {queryset.count()} work orders.")
    action_finish.short_description = "Finish selected work orders"


# ===== UNBUILD ADMINISTRATION =====

@admin.register(MrpUnbuild)
class MrpUnbuildAdmin(admin.ModelAdmin):
    list_display = ('name', 'product', 'product_qty', 'state', 'company')
    list_filter = ('state', 'company')
    search_fields = ('name', 'product_id__name')
    ordering = ('-create_date',)
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'product', 'product_qty', 'product_uom', 'state')
        }),
        ('Configuration', {
            'fields': ('bom', 'lot')
        }),
        ('Locations', {
            'fields': ('location', 'location_dest')
        }),
        ('Company', {
            'fields': ('company',)
        }),
    )
    
    actions = ['action_unbuild']
    
    def action_unbuild(self, request, queryset):
        for unbuild in queryset:
            if unbuild.state == 'draft':
                unbuild.action_unbuild()
        self.message_user(request, f"Processed {queryset.count()} unbuild orders.")
    action_unbuild.short_description = "Process selected unbuild orders"


# ===== SUPPORTING MODEL ADMINISTRATION =====

@admin.register(ProductAttribute)
class ProductAttributeAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)


@admin.register(ProductAttributeValue)
class ProductAttributeValueAdmin(admin.ModelAdmin):
    list_display = ('name', 'attribute')
    list_filter = ('attribute',)
    search_fields = ('name', 'attribute_id__name')


@admin.register(QualityPoint)
class QualityPointAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)


@admin.register(HrSkill)
class HrSkillAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)


# Note: ProcurementGroup, StockWarehouseOrderpoint, StockPickingType, and StockLot
# are registered in their respective modules (sales, inventory, purchases)
