from django.db import models
from django.core.exceptions import ValidationError
from django.utils import timezone
from decimal import Decimal
from datetime import timedelta
from core.models import BaseModel, Company, Partner, Currency, Country, CountryState
from django.contrib.auth.models import User


class CrmTeam(BaseModel):
    """Sales Team model - equivalent to crm.team in Odoo"""
    
    name = models.CharField(max_length=255, help_text="Team Name")
    active = models.BooleanField(default=True)
    sequence = models.IntegerField(default=10)
    
    # Team configuration
    use_leads = models.BooleanField(default=True, help_text="Use Leads")
    use_opportunities = models.BooleanField(default=True, help_text="Use Opportunities")
    
    # Team members
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                               related_name='crm_team_leader', help_text="Team Leader")
    members = models.ManyToManyField(User, blank=True, related_name='crm_teams',
                                       help_text="Team Members")
    
    # Company
    company = models.ForeignKey(Company, on_delete=models.PROTECT)
    
    # Invoicing configuration
    invoiced_target = models.DecimalField(max_digits=20, decimal_places=2, default=0.0,
                                        help_text="Invoicing Target")
    
    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['name', 'company'],
                name='crm_team_name_company_uniq'
            )
        ]
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['company']),
        ]
    
    def __str__(self):
        return self.name


class CrmStage(BaseModel):
    """CRM Stage model - equivalent to crm.stage in Odoo"""
    
    name = models.CharField(max_length=255, help_text="Stage Name")
    sequence = models.IntegerField(default=1)
    
    # Stage configuration
    is_won = models.BooleanField(default=False, help_text="Is Won Stage")
    fold = models.BooleanField(default=False, help_text="Folded in Kanban")
    
    # Probability
    probability = models.FloatField(default=0.0, help_text="Probability (%)")
    
    # Team restriction
    teams = models.ManyToManyField(CrmTeam, blank=True, 
                                     help_text="Specific teams for this stage")
    
    # Requirements
    requirements = models.TextField(blank=True, help_text="Requirements to reach this stage")
    
    class Meta:
        constraints = [
            models.CheckConstraint(
                check=models.Q(probability__gte=0) & models.Q(probability__lte=100),
                name='crm_stage_probability_range'
            )
        ]
        indexes = [
            models.Index(fields=['sequence']),
        ]
    
    def __str__(self):
        return self.name


class CrmLostReason(BaseModel):
    """Lost Reason model - equivalent to crm.lost.reason in Odoo"""
    
    name = models.CharField(max_length=255, help_text="Lost Reason")
    active = models.BooleanField(default=True)
    
    def __str__(self):
        return self.name


class CrmLead(BaseModel):
    """Lead/Opportunity model - equivalent to crm.lead in Odoo"""
    
    TYPE_CHOICES = [
        ('lead', 'Lead'),
        ('opportunity', 'Opportunity'),
    ]
    
    PRIORITY_CHOICES = [
        ('0', 'Low'),
        ('1', 'Normal'),
        ('2', 'High'),
        ('3', 'Very High'),
    ]
    
    # Basic information
    name = models.CharField(max_length=255, help_text="Opportunity/Lead Name")
    active = models.BooleanField(default=True)
    type = models.CharField(max_length=20, choices=TYPE_CHOICES, default='lead')
    priority = models.CharField(max_length=1, choices=PRIORITY_CHOICES, default='1')
    
    # Contact information
    partner = models.ForeignKey(Partner, on_delete=models.SET_NULL, null=True, blank=True,
                                  help_text="Customer")
    partner_name = models.CharField(max_length=255, blank=True, help_text="Customer Name")
    contact_name = models.CharField(max_length=255, blank=True, help_text="Contact Name")
    title = models.CharField(max_length=255, blank=True, help_text="Title")
    email_from = models.EmailField(blank=True, help_text="Email")
    phone = models.CharField(max_length=50, blank=True, help_text="Phone")
    mobile = models.CharField(max_length=50, blank=True, help_text="Mobile")
    website = models.URLField(blank=True, help_text="Website")
    
    # Address information
    street = models.CharField(max_length=255, blank=True)
    street2 = models.CharField(max_length=255, blank=True)
    city = models.CharField(max_length=100, blank=True)
    state = models.ForeignKey(CountryState, on_delete=models.SET_NULL, null=True, blank=True)
    zip = models.CharField(max_length=20, blank=True)
    country = models.ForeignKey(Country, on_delete=models.SET_NULL, null=True, blank=True)
    
    # Sales information
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                               help_text="Salesperson")
    team = models.ForeignKey(CrmTeam, on_delete=models.SET_NULL, null=True, blank=True,
                               help_text="Sales Team")
    stage = models.ForeignKey(CrmStage, on_delete=models.SET_NULL, null=True, blank=True,
                                help_text="Stage")
    
    # Financial information
    expected_revenue = models.DecimalField(max_digits=20, decimal_places=2, default=0.0,
                                         help_text="Expected Revenue")
    prorated_revenue = models.DecimalField(max_digits=20, decimal_places=2, default=0.0,
                                         help_text="Prorated Revenue")
    recurring_revenue = models.DecimalField(max_digits=20, decimal_places=2, default=0.0,
                                          help_text="Recurring Revenue")
    recurring_revenue_monthly = models.DecimalField(max_digits=20, decimal_places=2, default=0.0,
                                                   help_text="Monthly Recurring Revenue")
    
    # Probability and dates
    probability = models.FloatField(default=0.0, help_text="Success Probability (%)")
    date_deadline = models.DateField(null=True, blank=True, help_text="Expected Closing")
    date_closed = models.DateTimeField(null=True, blank=True, help_text="Closed Date")
    date_conversion = models.DateTimeField(null=True, blank=True, help_text="Conversion Date")
    date_last_stage_update = models.DateTimeField(auto_now=True)
    
    # Description and notes
    description = models.TextField(blank=True, help_text="Notes")
    
    # Source information
    source = models.CharField(max_length=255, blank=True, help_text="Source")
    referred = models.CharField(max_length=255, blank=True, help_text="Referred By")
    
    # Company and currency
    company = models.ForeignKey(Company, on_delete=models.PROTECT)
    currency = models.ForeignKey(Currency, on_delete=models.PROTECT, null=True, blank=True)
    
    # Lost information
    lost_reason = models.ForeignKey(CrmLostReason, on_delete=models.SET_NULL, null=True, blank=True)
    
    # Automated fields
    day_open = models.FloatField(default=0.0, help_text="Days to Assign")
    day_close = models.FloatField(default=0.0, help_text="Days to Close")
    
    class Meta:
        constraints = [
            models.CheckConstraint(
                check=models.Q(probability__gte=0) & models.Q(probability__lte=100),
                name='crm_lead_probability_range'
            ),
            models.CheckConstraint(
                check=models.Q(expected_revenue__gte=0),
                name='crm_lead_positive_revenue'
            ),
        ]
        indexes = [
            models.Index(fields=['type']),
            models.Index(fields=['user']),
            models.Index(fields=['team']),
            models.Index(fields=['stage']),
            models.Index(fields=['partner']),
            models.Index(fields=['date_deadline']),
            models.Index(fields=['probability']),
        ]
    
    def __str__(self):
        return self.name or f"{self.partner_name or self.contact_name} - {self.type}"
    
    def clean(self):
        super().clean()
        
        # Validate that opportunities have expected revenue
        if self.type == 'opportunity' and self.expected_revenue <= 0:
            raise ValidationError("Opportunities must have expected revenue > 0")
        
        # Validate probability consistency with stage
        if self.stage and self.stage.probability != self.probability:
            self.probability = self.stage.probability
    
    def save(self, *args, **kwargs):
        # Set currency from company if not set
        if not self.currency and self.company:
            self.currency = self.company.currency

        # Set stage probability FIRST
        if self.stage:
            self.probability = self.stage.probability

        # Update prorated revenue based on probability
        if self.expected_revenue and self.probability:
            self.prorated_revenue = self.expected_revenue * (Decimal(str(self.probability)) / Decimal('100'))

        super().save(*args, **kwargs)
    
    def action_set_won(self):
        """Mark opportunity as won"""
        if self.type != 'opportunity':
            raise ValidationError("Only opportunities can be marked as won")
        
        won_stage = CrmStage.objects.filter(is_won=True).first()
        if won_stage:
            self.stage = won_stage
            self.probability = 100.0
            self.date_closed = timezone.now()
            self.save()
    
    def action_set_lost(self, lost_reason=None):
        """Mark opportunity as lost"""
        if self.type != 'opportunity':
            raise ValidationError("Only opportunities can be marked as lost")

        # Find a lost stage (probability = 0) or create one
        lost_stage = CrmStage.objects.filter(probability=0.0).first()
        if lost_stage:
            self.stage = lost_stage
        else:
            # If no lost stage, manually set probability
            self.probability = 0.0

        self.date_closed = timezone.now()
        if lost_reason_id:
            self.lost_reason = lost_reason_id
        self.save()
    
    def convert_to_opportunity(self):
        """Convert lead to opportunity"""
        if self.type != 'lead':
            raise ValidationError("Only leads can be converted to opportunities")
        
        self.type = 'opportunity'
        self.date_conversion = timezone.now()
        
        # Set default stage for opportunities
        if not self.stage:
            default_stage = CrmStage.objects.filter(
                models.Q(team_ids=self.team) | models.Q(team_ids__isnull=True)
            ).order_by('sequence').first()
            if default_stage:
                self.stage = default_stage
        
        self.save()


class CrmActivityType(BaseModel):
    """Activity Type model - equivalent to mail.activity.type in Odoo"""

    name = models.CharField(max_length=255, help_text="Activity Type")
    category = models.CharField(max_length=50, default='default')
    sequence = models.IntegerField(default=1)

    # Timing
    delay_count = models.IntegerField(default=0, help_text="Number of days/hours")
    delay_unit = models.CharField(max_length=10, choices=[
        ('days', 'Days'),
        ('weeks', 'Weeks'),
        ('months', 'Months'),
    ], default='days')

    # Configuration
    summary = models.CharField(max_length=255, blank=True, help_text="Default Summary")
    default_note = models.TextField(blank=True, help_text="Default Note")

    # Automation
    chaining_type = models.CharField(max_length=20, choices=[
        ('suggest', 'Suggest Next Activity'),
        ('trigger', 'Trigger Next Activity'),
    ], default='suggest')

    def __str__(self):
        return self.name


class CrmActivity(BaseModel):
    """Activity model - equivalent to mail.activity in Odoo"""

    STATE_CHOICES = [
        ('overdue', 'Overdue'),
        ('today', 'Today'),
        ('planned', 'Planned'),
        ('done', 'Done'),
    ]

    # Basic information
    summary = models.CharField(max_length=255, help_text="Summary")
    note = models.TextField(blank=True, help_text="Note")

    # Type and assignment
    activity_type = models.ForeignKey(CrmActivityType, on_delete=models.PROTECT)
    user = models.ForeignKey(User, on_delete=models.CASCADE, help_text="Assigned to")

    # Related record
    res_model = models.CharField(max_length=255, default='crm.lead')
    res = models.PositiveIntegerField()
    lead = models.ForeignKey(CrmLead, on_delete=models.CASCADE, null=True, blank=True)

    # Dates
    date_deadline = models.DateField(help_text="Due Date")
    date_done = models.DateTimeField(null=True, blank=True)

    # State
    state = models.CharField(max_length=20, choices=STATE_CHOICES, default='planned')

    # Feedback
    feedback = models.TextField(blank=True, help_text="Feedback")

    class Meta:
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['date_deadline']),
            models.Index(fields=['state']),
            models.Index(fields=['lead']),
        ]

    def __str__(self):
        return f"{self.summary} - {self.user.username}"

    def save(self, *args, **kwargs):
        # Auto-compute state based on date
        if not self.date_done:
            today = timezone.now().date()
            if self.date_deadline < today:
                self.state = 'overdue'
            elif self.date_deadline == today:
                self.state = 'today'
            else:
                self.state = 'planned'
        else:
            self.state = 'done'

        super().save(*args, **kwargs)

    def action_done(self, feedback=None):
        """Mark activity as done"""
        self.date_done = timezone.now()
        self.state = 'done'
        if feedback:
            self.feedback = feedback
        self.save()


class CrmTag(BaseModel):
    """Tag model for CRM - equivalent to crm.tag in Odoo"""

    name = models.CharField(max_length=255, unique=True)
    color = models.IntegerField(default=0, help_text="Color Index")

    def __str__(self):
        return self.name
