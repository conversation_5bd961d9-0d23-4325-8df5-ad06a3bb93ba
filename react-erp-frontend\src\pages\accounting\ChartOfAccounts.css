/* Chart of Accounts Styles */
.chart-of-accounts {
  min-height: 100vh;
  background-color: var(--odoo-bg-secondary);
  padding: var(--odoo-spacing-lg);
}

.chart-of-accounts.loading {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  text-align: center;
  color: var(--odoo-text-secondary);
}

.loading-spinner i {
  font-size: 2rem;
  color: var(--odoo-primary);
  margin-bottom: var(--odoo-spacing-md);
}

/* Page Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--odoo-spacing-xl);
  background: var(--odoo-white);
  padding: var(--odoo-spacing-lg);
  border-radius: var(--odoo-border-radius-lg);
  box-shadow: var(--odoo-shadow-sm);
}

.header-content h1 {
  font-size: var(--odoo-font-size-2xl);
  font-weight: var(--odoo-font-weight-bold);
  color: var(--odoo-text-primary);
  margin-bottom: var(--odoo-spacing-xs);
  display: flex;
  align-items: center;
  gap: var(--odoo-spacing-sm);
}

.header-content h1 i {
  color: var(--odoo-primary);
}

.header-content p {
  color: var(--odoo-text-secondary);
  margin: 0;
}

/* Filters Section */
.filters-section {
  display: flex;
  gap: var(--odoo-spacing-md);
  margin-bottom: var(--odoo-spacing-lg);
  background: var(--odoo-white);
  padding: var(--odoo-spacing-md);
  border-radius: var(--odoo-border-radius-lg);
  box-shadow: var(--odoo-shadow-sm);
}

.search-box {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
}

.search-box i {
  position: absolute;
  left: var(--odoo-spacing-md);
  color: var(--odoo-text-muted);
  z-index: 2;
}

.search-box input {
  width: 100%;
  padding: var(--odoo-spacing-sm) var(--odoo-spacing-md) var(--odoo-spacing-sm) 2.5rem;
  border: 1px solid var(--odoo-border-medium);
  border-radius: var(--odoo-border-radius);
  font-size: var(--odoo-font-size-base);
  transition: border-color var(--odoo-transition-fast);
}

.search-box input:focus {
  outline: none;
  border-color: var(--odoo-primary);
  box-shadow: 0 0 0 0.2rem rgba(113, 75, 103, 0.25);
}

.filter-dropdown select {
  padding: var(--odoo-spacing-sm) var(--odoo-spacing-md);
  border: 1px solid var(--odoo-border-medium);
  border-radius: var(--odoo-border-radius);
  font-size: var(--odoo-font-size-base);
  background: var(--odoo-white);
  cursor: pointer;
  min-width: 150px;
}

.filter-dropdown select:focus {
  outline: none;
  border-color: var(--odoo-primary);
  box-shadow: 0 0 0 0.2rem rgba(113, 75, 103, 0.25);
}

/* Accounts Table */
.accounts-table-container {
  background: var(--odoo-white);
  border-radius: var(--odoo-border-radius-lg);
  box-shadow: var(--odoo-shadow-sm);
  overflow: hidden;
  margin-bottom: var(--odoo-spacing-lg);
}

.accounts-table {
  width: 100%;
  border-collapse: collapse;
}

.accounts-table th {
  background: var(--odoo-bg-light);
  padding: var(--odoo-spacing-md);
  text-align: left;
  font-weight: var(--odoo-font-weight-semibold);
  color: var(--odoo-text-primary);
  border-bottom: 2px solid var(--odoo-border-light);
  font-size: var(--odoo-font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.accounts-table td {
  padding: var(--odoo-spacing-md);
  border-bottom: 1px solid var(--odoo-border-light);
  vertical-align: middle;
}

.accounts-table tr:hover {
  background: var(--odoo-bg-light);
}

.accounts-table tr.inactive {
  opacity: 0.6;
}

.account-code {
  font-family: var(--odoo-font-family-monospace);
  font-weight: var(--odoo-font-weight-normal);
  color: var(--odoo-primary);
  font-size: var(--odoo-font-size-sm);
  font-variant-numeric: tabular-nums;
}

.account-name .name {
  font-family: var(--odoo-font-family-sans-serif);
  font-size: var(--odoo-font-size-name);
  font-weight: var(--odoo-font-weight-normal);
  color: var(--odoo-text-primary);
}

.account-type-badge {
  display: inline-block;
  padding: var(--odoo-spacing-xs) var(--odoo-spacing-sm);
  border-radius: var(--odoo-border-radius-xl);
  color: var(--odoo-white);
  font-size: var(--odoo-font-size-xs);
  font-weight: var(--odoo-font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.balance {
  font-family: var(--odoo-font-family-monospace);
  font-weight: var(--odoo-font-weight-normal);
  text-align: right;
  position: relative;
  font-variant-numeric: tabular-nums;
}

.balance.debit {
  color: var(--odoo-success);
}

.balance.credit {
  color: var(--odoo-danger);
}

.balance-type {
  font-size: var(--odoo-font-size-xs);
  margin-left: var(--odoo-spacing-xs);
  opacity: 0.7;
}

.reconcile-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--odoo-spacing-xs);
  padding: var(--odoo-spacing-xs) var(--odoo-spacing-sm);
  border-radius: var(--odoo-border-radius-sm);
  font-size: var(--odoo-font-size-xs);
  font-weight: var(--odoo-font-weight-medium);
}

.reconcile-badge.active {
  background: rgba(40, 167, 69, 0.1);
  color: var(--odoo-success);
}

.reconcile-badge.inactive {
  background: rgba(108, 117, 125, 0.1);
  color: var(--odoo-text-muted);
}

.status-badge {
  display: inline-block;
  padding: var(--odoo-spacing-xs) var(--odoo-spacing-sm);
  border-radius: var(--odoo-border-radius-sm);
  font-size: var(--odoo-font-size-xs);
  font-weight: var(--odoo-font-weight-medium);
  text-transform: uppercase;
}

.status-badge.active {
  background: rgba(40, 167, 69, 0.1);
  color: var(--odoo-success);
}

.status-badge.inactive {
  background: rgba(220, 53, 69, 0.1);
  color: var(--odoo-danger);
}

.actions {
  text-align: center;
}

.action-buttons {
  display: flex;
  gap: var(--odoo-spacing-xs);
  justify-content: center;
}

.btn-icon {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--odoo-border-radius-sm);
  background: var(--odoo-bg-light);
  color: var(--odoo-text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--odoo-transition-fast);
}

.btn-icon:hover {
  background: var(--odoo-primary);
  color: var(--odoo-white);
  transform: translateY(-1px);
}

.btn-icon.danger:hover {
  background: var(--odoo-danger);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: var(--odoo-spacing-xl);
  color: var(--odoo-text-secondary);
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: var(--odoo-spacing-md);
  color: var(--odoo-text-muted);
}

.empty-state h3 {
  font-size: var(--odoo-font-size-lg);
  margin-bottom: var(--odoo-spacing-sm);
  color: var(--odoo-text-primary);
}

/* Summary */
.accounts-summary {
  display: flex;
  gap: var(--odoo-spacing-lg);
  background: var(--odoo-white);
  padding: var(--odoo-spacing-md);
  border-radius: var(--odoo-border-radius-lg);
  box-shadow: var(--odoo-shadow-sm);
}

.summary-item {
  display: flex;
  align-items: center;
  gap: var(--odoo-spacing-sm);
}

.summary-item .label {
  color: var(--odoo-text-secondary);
  font-size: var(--odoo-font-size-sm);
}

.summary-item .value {
  font-weight: var(--odoo-font-weight-bold);
  color: var(--odoo-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .chart-of-accounts {
    padding: var(--odoo-spacing-md);
  }

  .page-header {
    flex-direction: column;
    gap: var(--odoo-spacing-md);
    text-align: center;
  }

  .filters-section {
    flex-direction: column;
  }

  .accounts-table-container {
    overflow-x: auto;
  }

  .accounts-table {
    min-width: 800px;
  }

  .accounts-summary {
    flex-direction: column;
    gap: var(--odoo-spacing-sm);
  }
}
