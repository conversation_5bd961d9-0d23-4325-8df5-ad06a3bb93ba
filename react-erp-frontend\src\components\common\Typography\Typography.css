/* Typography Component Styles */

/* Required indicator for labels */
.odoo-required-indicator {
  color: var(--odoo-danger);
  font-weight: var(--odoo-font-weight-bold);
  margin-left: 0.25rem;
}

/* Focus area - simplified without pseudo-elements */
.odoo-focus-primary {
  position: relative;
  /* Removed ::after pseudo-element that was causing double baseline */
}

/* Code component enhancements */
.odoo-code {
  position: relative;
  display: inline-block;
}

.odoo-code-small {
  font-size: var(--odoo-font-size-base);
}

.odoo-code-large {
  font-size: var(--odoo-font-size-2xl);
}

/* Number component variants */
.odoo-number--currency::before {
  content: attr(data-currency);
  font-family: var(--odoo-font-family-primary);
  font-weight: var(--odoo-font-weight-normal);
  margin-right: 0.25rem;
  color: var(--odoo-text-secondary);
}

.odoo-number--percentage::after {
  content: '%';
  font-family: var(--odoo-font-family-primary);
  font-weight: var(--odoo-font-weight-normal);
  margin-left: 0.1rem;
  color: var(--odoo-text-secondary);
}

/* Badge variants */
.odoo-badge--success {
  color: var(--odoo-success);
}

.odoo-badge--warning {
  color: var(--odoo-warning);
}

.odoo-badge--danger {
  color: var(--odoo-danger);
}

.odoo-badge--info {
  color: var(--odoo-info);
}

/* Link button styling */
.odoo-link[type="button"] {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  font-family: inherit;
  font-size: inherit;
}

/* Heading variants */
.odoo-heading--muted {
  color: var(--odoo-text-muted);
}

.odoo-heading--secondary {
  color: var(--odoo-text-secondary);
}

.odoo-heading--primary-color {
  color: var(--odoo-primary);
}

/* Text selection styling */
::selection {
  background-color: var(--odoo-primary);
  color: var(--odoo-white);
}

::-moz-selection {
  background-color: var(--odoo-primary);
  color: var(--odoo-white);
}
