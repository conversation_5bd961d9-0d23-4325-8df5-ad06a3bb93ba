/* Odoo Input Styles */
.odoo-input-container {
  margin-bottom: var(--odoo-spacing-md);
}

.odoo-input-label {
  display: block;
  margin-bottom: var(--odoo-spacing-xs);
  font-size: var(--odoo-font-size-sm);
  font-weight: var(--odoo-font-weight-medium);
  color: var(--odoo-text-primary);
  line-height: 1.4;
}

.odoo-input-required {
  color: var(--odoo-danger);
  margin-left: 2px;
}

.odoo-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.odoo-input {
  display: block;
  width: 100%;
  padding: var(--odoo-spacing-sm) var(--odoo-spacing-md);
  font-size: var(--odoo-font-size-base);
  font-weight: var(--odoo-font-weight-normal);
  line-height: 1.5;
  color: var(--odoo-text-primary);
  background-color: var(--odoo-white);
  background-clip: padding-box;
  border: 1px solid var(--odoo-border-medium);
  border-radius: var(--odoo-border-radius);
  transition: border-color var(--odoo-transition-fast), box-shadow var(--odoo-transition-fast);
}

.odoo-input:focus {
  outline: 0;
  border-color: var(--odoo-primary);
  box-shadow: 0 0 0 0.2rem rgba(113, 75, 103, 0.25);
}

.odoo-input::placeholder {
  color: var(--odoo-text-muted);
  opacity: 1;
}

/* Input States */
.odoo-input--focused {
  border-color: var(--odoo-primary);
  box-shadow: 0 0 0 0.2rem rgba(113, 75, 103, 0.25);
}

.odoo-input--error {
  border-color: var(--odoo-danger);
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.odoo-input--disabled {
  background-color: var(--odoo-bg-light);
  opacity: 1;
  cursor: not-allowed;
}

.odoo-input--disabled::placeholder {
  color: var(--odoo-text-muted);
}

/* Input with Icons */
.odoo-input--with-icon-left {
  padding-left: 2.5rem;
}

.odoo-input--with-icon-right {
  padding-right: 2.5rem;
}

.odoo-input-icon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  color: var(--odoo-text-muted);
  font-size: var(--odoo-font-size-base);
  z-index: 2;
}

.odoo-input-icon--left {
  left: var(--odoo-spacing-md);
}

.odoo-input-icon--right {
  right: var(--odoo-spacing-md);
}

/* Password Toggle */
.odoo-input-password-toggle {
  position: absolute;
  right: var(--odoo-spacing-md);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--odoo-text-muted);
  cursor: pointer;
  padding: 0;
  font-size: var(--odoo-font-size-base);
  z-index: 2;
  transition: color var(--odoo-transition-fast);
}

.odoo-input-password-toggle:hover {
  color: var(--odoo-text-primary);
}

.odoo-input-password-toggle:focus {
  outline: none;
  color: var(--odoo-primary);
}

/* Error Message */
.odoo-input-error {
  display: flex;
  align-items: center;
  gap: var(--odoo-spacing-xs);
  margin-top: var(--odoo-spacing-xs);
  font-size: var(--odoo-font-size-sm);
  color: var(--odoo-danger);
}

.odoo-input-error i {
  font-size: var(--odoo-font-size-xs);
}

/* Container States */
.odoo-input-container--focused .odoo-input-label {
  color: var(--odoo-primary);
}

.odoo-input-container--error .odoo-input-label {
  color: var(--odoo-danger);
}

.odoo-input-container--disabled .odoo-input-label {
  color: var(--odoo-text-muted);
}

/* Responsive */
@media (max-width: 767px) {
  .odoo-input {
    padding: var(--odoo-spacing-sm);
    font-size: var(--odoo-font-size-base);
  }
  
  .odoo-input--with-icon-left {
    padding-left: 2.25rem;
  }
  
  .odoo-input--with-icon-right {
    padding-right: 2.25rem;
  }
  
  .odoo-input-icon--left {
    left: var(--odoo-spacing-sm);
  }
  
  .odoo-input-icon--right {
    right: var(--odoo-spacing-sm);
  }
  
  .odoo-input-password-toggle {
    right: var(--odoo-spacing-sm);
  }
}
