from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.db.models import Q
from django.utils import timezone
from django.db import transaction
from datetime import date, datetime

from core.models import Company, Currency, Country, CountryState, FiscalYear
from ..serializers.settings import (
    CompanySerializer, CompanyUpdateSerializer, CurrencySerializer,
    CountrySerializer, CountryStateSerializer, FiscalYearSerializer, SettingsDashboardSerializer
)


class CompanyViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Companies
    """
    serializer_class = CompanySerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """Filter companies by user access"""
        return Company.objects.select_related('country', 'currency').all()
    
    def get_serializer_class(self):
        """Use different serializer for updates"""
        if self.action in ['update', 'partial_update']:
            return CompanyUpdateSerializer
        return CompanySerializer
    
    def perform_create(self, serializer):
        """Set create_uid and write_uid when creating company"""
        serializer.save(
            create_uid=self.request.user,
            write_uid=self.request.user
        )
    
    def perform_update(self, serializer):
        """Set write_uid when updating company"""
        serializer.save(write_uid=self.request.user)
    
    @action(detail=False, methods=['get'])
    def current(self, request):
        """Get current user's company"""
        # For now, get the first company - implement user company logic later
        company = Company.objects.select_related('country', 'currency').first()
        if company:
            serializer = CompanySerializer(company)
            return Response(serializer.data)
        return Response({'error': 'No company found'}, status=status.HTTP_404_NOT_FOUND)
    
    @action(detail=False, methods=['patch'])
    def update_current(self, request):
        """Update current user's company"""
        company = Company.objects.first()
        if not company:
            return Response({'error': 'No company found'}, status=status.HTTP_404_NOT_FOUND)

        serializer = CompanyUpdateSerializer(company, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save(write_uid=request.user)
            # Return full company data
            full_serializer = CompanySerializer(company)
            return Response(full_serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def setup(self, request):
        """Create a new company with complete setup"""
        try:
            with transaction.atomic():
                setup_data = request.data

                # Create the company
                company_data = {
                    'name': setup_data.get('name'),
                    'code': setup_data.get('code'),
                    'email': setup_data.get('email'),
                    'phone': setup_data.get('phone'),
                    'website': setup_data.get('website'),
                    'vat': setup_data.get('vat'),
                    'street': setup_data.get('street'),
                    'street2': setup_data.get('street2'),
                    'city': setup_data.get('city'),
                    'zip': setup_data.get('zip'),
                    'country': setup_data.get('country'),
                    'currency': setup_data.get('currency'),
                    'active': setup_data.get('active', True)
                }

                company_serializer = CompanyUpdateSerializer(data=company_data)
                if company_serializer.is_valid():
                    company = company_serializer.save(
                        create_uid=request.user,
                        write_uid=request.user
                    )
                else:
                    return Response(company_serializer.errors, status=status.HTTP_400_BAD_REQUEST)

                # Create fiscal year if requested
                if setup_data.get('create_fiscal_year', True):
                    fiscal_year_start = setup_data.get('fiscal_year_start', 'january')
                    current_year = date.today().year

                    # Determine fiscal year dates based on start month
                    if fiscal_year_start == 'january':
                        start_date = date(current_year, 1, 1)
                        end_date = date(current_year, 12, 31)
                    elif fiscal_year_start == 'april':
                        start_date = date(current_year, 4, 1)
                        end_date = date(current_year + 1, 3, 31)
                    elif fiscal_year_start == 'july':
                        start_date = date(current_year, 7, 1)
                        end_date = date(current_year + 1, 6, 30)
                    elif fiscal_year_start == 'october':
                        start_date = date(current_year, 10, 1)
                        end_date = date(current_year + 1, 9, 30)
                    else:
                        start_date = date(current_year, 1, 1)
                        end_date = date(current_year, 12, 31)

                    fiscal_year = FiscalYear.objects.create(
                        name=f'Fiscal Year {current_year}',
                        code=f'FY{current_year}',
                        date_start=start_date,
                        date_end=end_date,
                        company=company,
                        state='open',
                        active=True,
                        create_uid=request.user,
                        write_uid=request.user
                    )

                # TODO: Create Chart of Accounts based on template
                coa_template = setup_data.get('coa_template', 'standard')
                self._create_chart_of_accounts(company, coa_template, request.user)

                # TODO: Create sample data if requested
                if setup_data.get('create_sample_data', False):
                    self._create_sample_data(company, request.user)

                # Return the created company with full details
                full_serializer = CompanySerializer(company)
                return Response({
                    'company': full_serializer.data,
                    'message': 'Company created successfully with complete setup'
                }, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response({
                'error': f'Failed to create company: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)

    def _create_chart_of_accounts(self, company, template, user):
        """Create chart of accounts for the company"""
        from django.core.management import call_command
        from io import StringIO

        try:
            # Capture command output
            out = StringIO()
            call_command(
                'create_chart_of_accounts',
                company_id=str(company.id),
                template=template,
                stdout=out
            )
            print(f"Chart of accounts created: {out.getvalue()}")
        except Exception as e:
            print(f"Error creating chart of accounts: {str(e)}")
            # Don't fail the entire company creation if COA creation fails
            pass

    def _create_sample_data(self, company, user):
        """Create sample data for the company"""
        # TODO: Implement sample data creation
        # This would create sample customers, vendors, products, etc.
        pass


class CurrencyViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Currencies
    """
    serializer_class = CurrencySerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """Get all currencies"""
        return Currency.objects.all().order_by('name')
    
    def perform_create(self, serializer):
        """Set create_uid and write_uid when creating currency"""
        serializer.save(
            create_uid=self.request.user,
            write_uid=self.request.user
        )
    
    def perform_update(self, serializer):
        """Set write_uid when updating currency"""
        serializer.save(write_uid=self.request.user)
    
    @action(detail=True, methods=['post'])
    def set_base(self, request, pk=None):
        """Set a currency as the base currency"""
        currency = self.get_object()
        # TODO: Implement base currency logic
        # For now, just return success
        return Response({'message': f'{currency.name} set as base currency'})
    
    @action(detail=True, methods=['patch'])
    def update_rate(self, request, pk=None):
        """Update currency exchange rate"""
        currency = self.get_object()
        rate = request.data.get('rate')
        
        if rate is None:
            return Response({'error': 'Rate is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            rate = float(rate)
            if rate <= 0:
                return Response({'error': 'Rate must be positive'}, status=status.HTTP_400_BAD_REQUEST)
            
            # TODO: Store rate in a separate model
            # For now, just return success
            return Response({'message': f'Rate updated to {rate} for {currency.name}'})
            
        except ValueError:
            return Response({'error': 'Invalid rate format'}, status=status.HTTP_400_BAD_REQUEST)


class CountryViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Countries
    """
    serializer_class = CountrySerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """Get all countries"""
        return Country.objects.all().order_by('name')
    
    def perform_create(self, serializer):
        """Set create_uid and write_uid when creating country"""
        serializer.save(
            create_uid=self.request.user,
            write_uid=self.request.user
        )
    
    def perform_update(self, serializer):
        """Set write_uid when updating country"""
        serializer.save(write_uid=self.request.user)
    
    @action(detail=True, methods=['get'])
    def states(self, request, pk=None):
        """Get states/provinces for a country"""
        country = self.get_object()
        states = CountryState.objects.filter(country=country).order_by('name')
        serializer = CountryStateSerializer(states, many=True)
        return Response(serializer.data)


class CountryStateViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Country States/Provinces
    """
    serializer_class = CountryStateSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """Get all states"""
        return CountryState.objects.select_related('country').all().order_by('country__name', 'name')
    
    def perform_create(self, serializer):
        """Set create_uid and write_uid when creating state"""
        serializer.save(
            create_uid=self.request.user,
            write_uid=self.request.user
        )
    
    def perform_update(self, serializer):
        """Set write_uid when updating state"""
        serializer.save(write_uid=self.request.user)


class FiscalYearViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Fiscal Years
    """
    serializer_class = FiscalYearSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Get fiscal years for current company"""
        # For now, get all fiscal years - implement company filtering later
        return FiscalYear.objects.select_related('company').all().order_by('-date_start')

    def perform_create(self, serializer):
        """Set create_uid and write_uid when creating fiscal year"""
        # Get current company - for now use first company
        company = Company.objects.first()
        serializer.save(
            company=company,
            create_uid=self.request.user,
            write_uid=self.request.user
        )

    def perform_update(self, serializer):
        """Set write_uid when updating fiscal year"""
        serializer.save(write_uid=self.request.user)

    @action(detail=True, methods=['post'])
    def open_year(self, request, pk=None):
        """Open a fiscal year"""
        fiscal_year = self.get_object()
        fiscal_year.state = 'open'
        fiscal_year.save()
        return Response({'message': f'Fiscal year {fiscal_year.name} opened'})

    @action(detail=True, methods=['post'])
    def close_year(self, request, pk=None):
        """Close a fiscal year"""
        fiscal_year = self.get_object()
        fiscal_year.state = 'closed'
        fiscal_year.save()
        return Response({'message': f'Fiscal year {fiscal_year.name} closed'})


class SettingsViewSet(viewsets.ViewSet):
    """
    ViewSet for general settings operations
    """
    permission_classes = [IsAuthenticated]
    
    @action(detail=False, methods=['get'])
    def dashboard(self, request):
        """Get settings dashboard summary"""
        # Calculate summary statistics
        company_count = Company.objects.count()
        currency_count = Currency.objects.count()
        country_count = Country.objects.count()
        active_currencies = Currency.objects.filter(active=True).count()
        
        # Get base currency (for now, USD)
        base_currency = 'USD'
        
        # Get current company
        current_company = Company.objects.select_related('country', 'currency').first()
        
        # Mock recent changes
        recent_changes = [
            {
                'type': 'company',
                'action': 'updated',
                'description': 'Company information updated',
                'timestamp': timezone.now().isoformat(),
                'user': request.user.username
            },
            {
                'type': 'currency',
                'action': 'rate_updated',
                'description': 'EUR exchange rate updated',
                'timestamp': timezone.now().isoformat(),
                'user': request.user.username
            }
        ]
        
        data = {
            'company_count': company_count,
            'currency_count': currency_count,
            'country_count': country_count,
            'active_currencies': active_currencies,
            'base_currency': base_currency,
            'current_company': CompanySerializer(current_company).data if current_company else None,
            'recent_changes': recent_changes
        }
        
        serializer = SettingsDashboardSerializer(data)
        return Response(serializer.data)
    
    @action(detail=False, methods=['post'])
    def export_settings(self, request):
        """Export system settings"""
        # TODO: Implement settings export
        return Response({'message': 'Settings export functionality will be implemented'})
    
    @action(detail=False, methods=['post'])
    def import_settings(self, request):
        """Import system settings"""
        # TODO: Implement settings import
        return Response({'message': 'Settings import functionality will be implemented'})
    
    @action(detail=False, methods=['post'])
    def reset_defaults(self, request):
        """Reset settings to defaults"""
        # TODO: Implement reset to defaults
        return Response({'message': 'Reset to defaults functionality will be implemented'})
