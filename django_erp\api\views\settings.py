from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.db.models import Q
from django.utils import timezone

from core.models import Company, Currency, Country, CountryState, FiscalYear
from ..serializers.settings import (
    CompanySerializer, CompanyUpdateSerializer, CurrencySerializer,
    CountrySerializer, CountryStateSerializer, FiscalYearSerializer, SettingsDashboardSerializer
)


class CompanyViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Companies
    """
    serializer_class = CompanySerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """Filter companies by user access"""
        return Company.objects.select_related('country', 'currency').all()
    
    def get_serializer_class(self):
        """Use different serializer for updates"""
        if self.action in ['update', 'partial_update']:
            return CompanyUpdateSerializer
        return CompanySerializer
    
    def perform_create(self, serializer):
        """Set create_uid and write_uid when creating company"""
        serializer.save(
            create_uid=self.request.user,
            write_uid=self.request.user
        )
    
    def perform_update(self, serializer):
        """Set write_uid when updating company"""
        serializer.save(write_uid=self.request.user)
    
    @action(detail=False, methods=['get'])
    def current(self, request):
        """Get current user's company"""
        # For now, get the first company - implement user company logic later
        company = Company.objects.select_related('country', 'currency').first()
        if company:
            serializer = CompanySerializer(company)
            return Response(serializer.data)
        return Response({'error': 'No company found'}, status=status.HTTP_404_NOT_FOUND)
    
    @action(detail=False, methods=['patch'])
    def update_current(self, request):
        """Update current user's company"""
        company = Company.objects.first()
        if not company:
            return Response({'error': 'No company found'}, status=status.HTTP_404_NOT_FOUND)
        
        serializer = CompanyUpdateSerializer(company, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save(write_uid=request.user)
            # Return full company data
            full_serializer = CompanySerializer(company)
            return Response(full_serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class CurrencyViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Currencies
    """
    serializer_class = CurrencySerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """Get all currencies"""
        return Currency.objects.all().order_by('name')
    
    def perform_create(self, serializer):
        """Set create_uid and write_uid when creating currency"""
        serializer.save(
            create_uid=self.request.user,
            write_uid=self.request.user
        )
    
    def perform_update(self, serializer):
        """Set write_uid when updating currency"""
        serializer.save(write_uid=self.request.user)
    
    @action(detail=True, methods=['post'])
    def set_base(self, request, pk=None):
        """Set a currency as the base currency"""
        currency = self.get_object()
        # TODO: Implement base currency logic
        # For now, just return success
        return Response({'message': f'{currency.name} set as base currency'})
    
    @action(detail=True, methods=['patch'])
    def update_rate(self, request, pk=None):
        """Update currency exchange rate"""
        currency = self.get_object()
        rate = request.data.get('rate')
        
        if rate is None:
            return Response({'error': 'Rate is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            rate = float(rate)
            if rate <= 0:
                return Response({'error': 'Rate must be positive'}, status=status.HTTP_400_BAD_REQUEST)
            
            # TODO: Store rate in a separate model
            # For now, just return success
            return Response({'message': f'Rate updated to {rate} for {currency.name}'})
            
        except ValueError:
            return Response({'error': 'Invalid rate format'}, status=status.HTTP_400_BAD_REQUEST)


class CountryViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Countries
    """
    serializer_class = CountrySerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """Get all countries"""
        return Country.objects.all().order_by('name')
    
    def perform_create(self, serializer):
        """Set create_uid and write_uid when creating country"""
        serializer.save(
            create_uid=self.request.user,
            write_uid=self.request.user
        )
    
    def perform_update(self, serializer):
        """Set write_uid when updating country"""
        serializer.save(write_uid=self.request.user)
    
    @action(detail=True, methods=['get'])
    def states(self, request, pk=None):
        """Get states/provinces for a country"""
        country = self.get_object()
        states = CountryState.objects.filter(country=country).order_by('name')
        serializer = CountryStateSerializer(states, many=True)
        return Response(serializer.data)


class CountryStateViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Country States/Provinces
    """
    serializer_class = CountryStateSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """Get all states"""
        return CountryState.objects.select_related('country').all().order_by('country__name', 'name')
    
    def perform_create(self, serializer):
        """Set create_uid and write_uid when creating state"""
        serializer.save(
            create_uid=self.request.user,
            write_uid=self.request.user
        )
    
    def perform_update(self, serializer):
        """Set write_uid when updating state"""
        serializer.save(write_uid=self.request.user)


class FiscalYearViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Fiscal Years
    """
    serializer_class = FiscalYearSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Get fiscal years for current company"""
        # For now, get all fiscal years - implement company filtering later
        return FiscalYear.objects.select_related('company').all().order_by('-date_start')

    def perform_create(self, serializer):
        """Set create_uid and write_uid when creating fiscal year"""
        # Get current company - for now use first company
        company = Company.objects.first()
        serializer.save(
            company=company,
            create_uid=self.request.user,
            write_uid=self.request.user
        )

    def perform_update(self, serializer):
        """Set write_uid when updating fiscal year"""
        serializer.save(write_uid=self.request.user)

    @action(detail=True, methods=['post'])
    def open_year(self, request, pk=None):
        """Open a fiscal year"""
        fiscal_year = self.get_object()
        fiscal_year.state = 'open'
        fiscal_year.save()
        return Response({'message': f'Fiscal year {fiscal_year.name} opened'})

    @action(detail=True, methods=['post'])
    def close_year(self, request, pk=None):
        """Close a fiscal year"""
        fiscal_year = self.get_object()
        fiscal_year.state = 'closed'
        fiscal_year.save()
        return Response({'message': f'Fiscal year {fiscal_year.name} closed'})


class SettingsViewSet(viewsets.ViewSet):
    """
    ViewSet for general settings operations
    """
    permission_classes = [IsAuthenticated]
    
    @action(detail=False, methods=['get'])
    def dashboard(self, request):
        """Get settings dashboard summary"""
        # Calculate summary statistics
        company_count = Company.objects.count()
        currency_count = Currency.objects.count()
        country_count = Country.objects.count()
        active_currencies = Currency.objects.filter(active=True).count()
        
        # Get base currency (for now, USD)
        base_currency = 'USD'
        
        # Get current company
        current_company = Company.objects.select_related('country', 'currency').first()
        
        # Mock recent changes
        recent_changes = [
            {
                'type': 'company',
                'action': 'updated',
                'description': 'Company information updated',
                'timestamp': timezone.now().isoformat(),
                'user': request.user.username
            },
            {
                'type': 'currency',
                'action': 'rate_updated',
                'description': 'EUR exchange rate updated',
                'timestamp': timezone.now().isoformat(),
                'user': request.user.username
            }
        ]
        
        data = {
            'company_count': company_count,
            'currency_count': currency_count,
            'country_count': country_count,
            'active_currencies': active_currencies,
            'base_currency': base_currency,
            'current_company': CompanySerializer(current_company).data if current_company else None,
            'recent_changes': recent_changes
        }
        
        serializer = SettingsDashboardSerializer(data)
        return Response(serializer.data)
    
    @action(detail=False, methods=['post'])
    def export_settings(self, request):
        """Export system settings"""
        # TODO: Implement settings export
        return Response({'message': 'Settings export functionality will be implemented'})
    
    @action(detail=False, methods=['post'])
    def import_settings(self, request):
        """Import system settings"""
        # TODO: Implement settings import
        return Response({'message': 'Settings import functionality will be implemented'})
    
    @action(detail=False, methods=['post'])
    def reset_defaults(self, request):
        """Reset settings to defaults"""
        # TODO: Implement reset to defaults
        return Response({'message': 'Reset to defaults functionality will be implemented'})
