/* Currency Settings - Odoo Style */
.currency-settings {
  max-width: 1000px;
}

.currency-settings.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.loading-spinner {
  text-align: center;
  color: var(--odoo-text-secondary);
}

.loading-spinner i {
  font-size: 2rem;
  color: var(--odoo-primary);
  margin-bottom: var(--odoo-spacing-md);
}

/* Header */
.currency-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--odoo-spacing-lg);
  gap: var(--odoo-spacing-md);
}

.search-section {
  flex: 1;
  max-width: 400px;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-box i {
  position: absolute;
  left: var(--odoo-spacing-md);
  color: var(--odoo-text-muted);
  z-index: 2;
}

.search-box input {
  width: 100%;
  padding: var(--odoo-spacing-sm) var(--odoo-spacing-md) var(--odoo-spacing-sm) 2.5rem;
  border: 1px solid var(--odoo-border-medium);
  border-radius: var(--odoo-border-radius);
  font-size: var(--odoo-font-size-base);
  transition: border-color var(--odoo-transition-fast);
}

.search-box input:focus {
  outline: none;
  border-color: var(--odoo-primary);
  box-shadow: 0 0 0 0.2rem rgba(113, 75, 103, 0.25);
}

.header-actions {
  display: flex;
  gap: var(--odoo-spacing-sm);
}

/* Currency List */
.currency-list {
  margin-bottom: var(--odoo-spacing-xl);
}

.list-header {
  margin-bottom: var(--odoo-spacing-lg);
}

.list-header h3 {
  font-size: var(--odoo-font-size-lg);
  font-weight: var(--odoo-font-weight-semibold);
  color: var(--odoo-text-primary);
  margin-bottom: var(--odoo-spacing-xs);
  display: flex;
  align-items: center;
  gap: var(--odoo-spacing-sm);
}

.list-header h3 i {
  color: var(--odoo-primary);
}

.list-header p {
  color: var(--odoo-text-secondary);
  margin: 0;
  font-size: var(--odoo-font-size-sm);
}

/* Currency Table */
.currency-table {
  background: var(--odoo-white);
  border-radius: var(--odoo-border-radius-lg);
  box-shadow: var(--odoo-shadow-sm);
  overflow: hidden;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1.5fr 1.5fr 1fr 1fr;
  gap: var(--odoo-spacing-md);
  padding: var(--odoo-spacing-md);
  background: var(--odoo-bg-light);
  border-bottom: 2px solid var(--odoo-border-light);
  font-weight: var(--odoo-font-weight-semibold);
  color: var(--odoo-text-primary);
  font-size: var(--odoo-font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table-body {
  max-height: 500px;
  overflow-y: auto;
}

.currency-row {
  display: grid;
  grid-template-columns: 2fr 1.5fr 1.5fr 1fr 1fr;
  gap: var(--odoo-spacing-md);
  padding: var(--odoo-spacing-md);
  border-bottom: 1px solid var(--odoo-border-light);
  transition: background-color var(--odoo-transition-fast);
}

.currency-row:hover {
  background: var(--odoo-bg-light);
}

.currency-row.inactive {
  opacity: 0.6;
}

/* Currency Info */
.currency-info {
  display: flex;
  align-items: center;
  gap: var(--odoo-spacing-md);
}

.currency-symbol {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--odoo-primary), var(--odoo-primary-light));
  color: var(--odoo-white);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--odoo-font-weight-bold);
  font-size: var(--odoo-font-size-lg);
}

.currency-details h4 {
  font-size: var(--odoo-font-size-base);
  font-weight: var(--odoo-font-weight-medium);
  color: var(--odoo-text-primary);
  margin-bottom: var(--odoo-spacing-xs);
}

.currency-code {
  font-size: var(--odoo-font-size-xs);
  color: var(--odoo-text-muted);
  font-family: 'Courier New', monospace;
  font-weight: var(--odoo-font-weight-bold);
}

.base-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  background: linear-gradient(135deg, #ffc107, #ffca2c);
  color: #856404;
  border-radius: var(--odoo-border-radius-sm);
  font-size: var(--odoo-font-size-xs);
  font-weight: var(--odoo-font-weight-medium);
}

/* Rate Input */
.rate-input {
  display: flex;
  flex-direction: column;
  gap: var(--odoo-spacing-xs);
}

.rate-input input {
  padding: var(--odoo-spacing-xs) var(--odoo-spacing-sm);
  border: 1px solid var(--odoo-border-medium);
  border-radius: var(--odoo-border-radius);
  font-size: var(--odoo-font-size-sm);
  font-family: 'Courier New', monospace;
  text-align: right;
}

.rate-input input:focus {
  outline: none;
  border-color: var(--odoo-primary);
  box-shadow: 0 0 0 0.2rem rgba(113, 75, 103, 0.25);
}

.rate-input input.disabled {
  background: var(--odoo-bg-light);
  color: var(--odoo-text-muted);
  cursor: not-allowed;
}

.rate-label {
  font-size: var(--odoo-font-size-xs);
  color: var(--odoo-text-muted);
}

/* Format Preview */
.format-preview {
  display: flex;
  flex-direction: column;
  gap: var(--odoo-spacing-xs);
}

.format-example {
  font-size: var(--odoo-font-size-base);
  font-weight: var(--odoo-font-weight-medium);
  color: var(--odoo-text-primary);
  font-family: 'Courier New', monospace;
}

.format-details {
  font-size: var(--odoo-font-size-xs);
  color: var(--odoo-text-muted);
}

/* Status Controls */
.status-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--odoo-spacing-xs);
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--odoo-border-medium);
  transition: var(--odoo-transition-fast);
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: var(--odoo-white);
  transition: var(--odoo-transition-fast);
  border-radius: 50%;
}

.toggle-switch input:checked + .toggle-slider {
  background-color: var(--odoo-primary);
}

.toggle-switch input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

.status-label {
  font-size: var(--odoo-font-size-xs);
  font-weight: var(--odoo-font-weight-medium);
}

.status-label.active {
  color: var(--odoo-success);
}

.status-label.inactive {
  color: var(--odoo-text-muted);
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: var(--odoo-spacing-xs);
  justify-content: center;
}

.btn-action {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--odoo-border-radius-sm);
  background: var(--odoo-bg-light);
  color: var(--odoo-text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--odoo-transition-fast);
}

.btn-action:hover {
  background: var(--odoo-primary);
  color: var(--odoo-white);
  transform: translateY(-1px);
}

.btn-action.danger:hover {
  background: var(--odoo-danger);
}

.btn-action:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-action:disabled:hover {
  background: var(--odoo-bg-light);
  color: var(--odoo-text-secondary);
  transform: none;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: var(--odoo-spacing-xl);
  color: var(--odoo-text-secondary);
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: var(--odoo-spacing-md);
  color: var(--odoo-text-muted);
}

.empty-state h3 {
  font-size: var(--odoo-font-size-lg);
  margin-bottom: var(--odoo-spacing-sm);
  color: var(--odoo-text-primary);
}

/* Exchange Info */
.exchange-info {
  margin-top: var(--odoo-spacing-xl);
}

.info-card {
  background: var(--odoo-bg-light);
  border: 1px solid var(--odoo-border-light);
  border-radius: var(--odoo-border-radius-lg);
  padding: var(--odoo-spacing-lg);
}

.info-card h4 {
  font-size: var(--odoo-font-size-base);
  font-weight: var(--odoo-font-weight-semibold);
  color: var(--odoo-text-primary);
  margin-bottom: var(--odoo-spacing-md);
  display: flex;
  align-items: center;
  gap: var(--odoo-spacing-sm);
}

.info-card h4 i {
  color: var(--odoo-info);
}

.info-card ul {
  margin: 0;
  padding-left: var(--odoo-spacing-lg);
  color: var(--odoo-text-secondary);
}

.info-card li {
  margin-bottom: var(--odoo-spacing-xs);
  font-size: var(--odoo-font-size-sm);
}

/* Responsive Design */
@media (max-width: 768px) {
  .currency-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: center;
  }
  
  .table-header,
  .currency-row {
    grid-template-columns: 1fr;
    gap: var(--odoo-spacing-sm);
  }
  
  .currency-row {
    padding: var(--odoo-spacing-sm);
  }
  
  .currency-info {
    justify-content: center;
  }
  
  .action-buttons {
    justify-content: center;
  }
}
