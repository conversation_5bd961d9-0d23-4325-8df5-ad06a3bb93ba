from django.contrib import admin
from .models import (
    ProjectProject, ProjectTags, ProjectTaskType, ProjectTask,
    ProjectMilestone, AccountAnalyticLine, AccountAnalyticTag
)


@admin.register(ProjectProject)
class ProjectProjectAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'user', 'partner', 'company', 
        'task_count', 'date_start', 'date', 'active'
    ]
    list_filter = [
        'active', 'company', 'user', 'privacy_visibility',
        'allow_timesheets', 'allow_billable_hours', 'date_start'
    ]
    search_fields = ['name', 'description']
    filter_horizontal = ['favorite_users', 'tags', 'tasks']
    readonly_fields = ['task_count']
    
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'active', 'sequence', 'description']
        }),
        ('Management', {
            'fields': ['user', 'partner', 'company']
        }),
        ('Dates', {
            'fields': ['date_start', 'date']
        }),
        ('Configuration', {
            'fields': [
                'allow_timesheets', 'allow_billable_hours', 'allow_material',
                'allow_milestones', 'allow_forecast'
            ]
        }),
        ('Privacy & Access', {
            'fields': ['privacy_visibility']
        }),
        ('Analytics', {
            'fields': ['analytic_account']
        }),
        ('Favorites & Tags', {
            'fields': ['is_favorite', 'favorite_users', 'tags']
        }),
        ('Display', {
            'fields': ['color']
        }),
        ('Tasks', {
            'fields': ['task_count', 'tasks']
        }),
        ('Rating', {
            'fields': ['rating_request_deadline', 'portal_show_rating']
        }),
    ]
    
    actions = ['action_close_projects', 'action_open_projects']
    
    def action_close_projects(self, request, queryset):
        for project in queryset:
            project.action_close()
        self.message_user(request, f"Closed {queryset.count()} projects.")
    action_close_projects.short_description = "Close selected projects"
    
    def action_open_projects(self, request, queryset):
        for project in queryset:
            project.action_open()
        self.message_user(request, f"Opened {queryset.count()} projects.")
    action_open_projects.short_description = "Open selected projects"


@admin.register(ProjectTags)
class ProjectTagsAdmin(admin.ModelAdmin):
    list_display = ['name', 'color']
    search_fields = ['name']


@admin.register(ProjectTaskType)
class ProjectTaskTypeAdmin(admin.ModelAdmin):
    list_display = ['name', 'sequence', 'fold']
    list_filter = ['fold']
    search_fields = ['name', 'description']
    filter_horizontal = ['projects']
    ordering = ['sequence', 'name']
    
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'description', 'sequence']
        }),
        ('Configuration', {
            'fields': ['fold', 'auto_validation_kanban_state']
        }),
        ('Projects', {
            'fields': ['projects']
        }),
        ('Templates', {
            'fields': ['rating_template', 'mail_template']
        }),
    ]


@admin.register(ProjectTask)
class ProjectTaskAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'project', 'stage', 'priority', 'kanban_state',
        'planned_hours', 'effective_hours', 'progress', 'date_deadline'
    ]
    list_filter = [
        'project', 'stage', 'priority', 'kanban_state', 
        'active', 'date_deadline', 'company'
    ]
    search_fields = ['name', 'description']
    filter_horizontal = ['users', 'tags', 'childs', 'depend_ons']
    readonly_fields = ['date_last_stage_update', 'effective_hours', 'remaining_hours', 'progress']
    date_hierarchy = 'date_deadline'
    
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'active', 'sequence', 'priority']
        }),
        ('Project & Stage', {
            'fields': ['project', 'stage']
        }),
        ('Assignment', {
            'fields': ['users']
        }),
        ('Dates', {
            'fields': ['date_assign', 'date_deadline', 'date_last_stage_update']
        }),
        ('Description', {
            'fields': ['description', 'notes']
        }),
        ('Hierarchy', {
            'fields': ['parent', 'childs']
        }),
        ('Progress', {
            'fields': ['kanban_state', 'kanban_state_label']
        }),
        ('Time Tracking', {
            'fields': [
                'planned_hours', 'effective_hours', 'remaining_hours', 
                'progress', 'overtime'
            ]
        }),
        ('Subtasks', {
            'fields': ['subtask_effective_hours', 'subtask_planned_hours']
        }),
        ('Customer', {
            'fields': ['partner', 'commercial_partner']
        }),
        ('Communication', {
            'fields': ['email_from', 'email_cc']
        }),
        ('Tags & Labels', {
            'fields': ['tags']
        }),
        ('Display', {
            'fields': ['color', 'displayed_image']
        }),
        ('Legends', {
            'fields': ['legend_blocked', 'legend_done', 'legend_normal']
        }),
        ('Portal', {
            'fields': ['portal_user_names']
        }),
        ('Milestone', {
            'fields': ['milestone']
        }),
        ('Dependencies', {
            'fields': ['depend_ons']
        }),
        ('System', {
            'fields': ['company']
        }),
    ]
    
    actions = ['action_close_tasks', 'action_open_tasks']
    
    def action_close_tasks(self, request, queryset):
        for task in queryset:
            task.action_close()
        self.message_user(request, f"Closed {queryset.count()} tasks.")
    action_close_tasks.short_description = "Close selected tasks"
    
    def action_open_tasks(self, request, queryset):
        for task in queryset:
            task.action_open()
        self.message_user(request, f"Opened {queryset.count()} tasks.")
    action_open_tasks.short_description = "Open selected tasks"


@admin.register(ProjectMilestone)
class ProjectMilestoneAdmin(admin.ModelAdmin):
    list_display = ['name', 'project', 'deadline', 'is_reached']
    list_filter = ['is_reached', 'project', 'deadline']
    search_fields = ['name', 'description']
    date_hierarchy = 'deadline'
    
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'project']
        }),
        ('Schedule', {
            'fields': ['deadline']
        }),
        ('Progress', {
            'fields': ['is_reached']
        }),
        ('Description', {
            'fields': ['description']
        }),
    ]
    
    actions = ['action_reach_milestones', 'action_unreach_milestones']
    
    def action_reach_milestones(self, request, queryset):
        for milestone in queryset:
            milestone.action_reach()
        self.message_user(request, f"Marked {queryset.count()} milestones as reached.")
    action_reach_milestones.short_description = "Mark selected milestones as reached"
    
    def action_unreach_milestones(self, request, queryset):
        for milestone in queryset:
            milestone.action_unreach()
        self.message_user(request, f"Marked {queryset.count()} milestones as not reached.")
    action_unreach_milestones.short_description = "Mark selected milestones as not reached"


@admin.register(AccountAnalyticLine)
class AccountAnalyticLineAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'date', 'project', 'task', 'user', 
        'unit_amount', 'amount', 'validated'
    ]
    list_filter = [
        'date', 'project', 'task', 'user', 
        'validated', 'company'
    ]
    search_fields = ['name', 'project_id__name', 'task_id__name']
    filter_horizontal = ['tags']
    date_hierarchy = 'date'
    
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'date']
        }),
        ('Time Tracking', {
            'fields': ['unit_amount', 'product_uom']
        }),
        ('Project & Task', {
            'fields': ['project', 'task']
        }),
        ('Assignment', {
            'fields': ['user', 'employee']
        }),
        ('Customer', {
            'fields': ['partner']
        }),
        ('Financial', {
            'fields': ['amount', 'currency']
        }),
        ('Analytics', {
            'fields': ['account', 'general_account']
        }),
        ('Validation', {
            'fields': ['validated']
        }),
        ('Tags', {
            'fields': ['tags']
        }),
        ('System', {
            'fields': ['company']
        }),
    ]


@admin.register(AccountAnalyticTag)
class AccountAnalyticTagAdmin(admin.ModelAdmin):
    list_display = ['name', 'color', 'active']
    list_filter = ['active', 'color']
    search_fields = ['name']
