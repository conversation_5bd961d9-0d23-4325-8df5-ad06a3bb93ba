from django.contrib import admin
from .models import (
    ProductCategory, ProductTemplate, Product, ProductPackaging,
    StockLocation, StockPicking, StockMove
)


@admin.register(ProductCategory)
class ProductCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'parent_id', 'complete_name']
    list_filter = ['parent_id']
    search_fields = ['name', 'complete_name']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'parent_id']
        }),
        ('Additional', {
            'fields': ['complete_name']
        }),
    ]


@admin.register(ProductTemplate)
class ProductTemplateAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'categ_id', 'detailed_type', 'list_price', 'standard_price', 'active'
    ]
    list_filter = [
        'detailed_type', 'categ_id', 'active', 'tracking'
    ]
    search_fields = ['name', 'default_code', 'barcode']

    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'active', 'categ_id', 'detailed_type']
        }),
        ('Pricing', {
            'fields': ['list_price', 'standard_price']
        }),
        ('Inventory', {
            'fields': ['tracking']
        }),
        ('Identification', {
            'fields': ['default_code', 'barcode']
        }),
        ('Units of Measure', {
            'fields': ['uom_id', 'uom_po_id']
        }),
        ('Additional', {
            'fields': ['description', 'description_purchase', 'description_sale']
        }),
        ('Company', {
            'fields': ['company_id']
        }),
    ]


@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = [
        'display_name', 'product_tmpl_id', 'default_code', 'barcode',
        'qty_available', 'active'
    ]
    list_filter = ['active', 'product_tmpl_id__categ_id', 'product_tmpl_id__detailed_type']
    search_fields = ['product_tmpl_id__name', 'default_code', 'barcode']

    fieldsets = [
        ('Basic Information', {
            'fields': ['product_tmpl_id', 'active']
        }),
        ('Identification', {
            'fields': ['default_code', 'barcode']
        }),
        ('Stock Quantities', {
            'fields': ['qty_available', 'virtual_available', 'incoming_qty', 'outgoing_qty']
        }),
    ]


@admin.register(ProductPackaging)
class ProductPackagingAdmin(admin.ModelAdmin):
    list_display = ['name', 'product_id', 'qty', 'barcode']
    list_filter = ['product_id']
    search_fields = ['name', 'barcode']


@admin.register(StockLocation)
class StockLocationAdmin(admin.ModelAdmin):
    list_display = ['name', 'location_id', 'usage', 'company_id', 'active']
    list_filter = ['usage', 'company_id', 'active']
    search_fields = ['name', 'complete_name']

    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'active', 'location_id', 'company_id']
        }),
        ('Type', {
            'fields': ['usage']
        }),
        ('Additional', {
            'fields': ['complete_name', 'comment']
        }),
    ]


# StockWarehouse is imported from sales.models, so we don't register it here


@admin.register(StockPicking)
class StockPickingAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'partner_id', 'picking_type_id', 'state',
        'scheduled_date', 'date_done'
    ]
    list_filter = ['state', 'picking_type_id', 'company_id', 'scheduled_date']
    search_fields = ['name', 'origin', 'partner_id__name']
    date_hierarchy = 'scheduled_date'

    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'origin', 'picking_type_id']
        }),
        ('Partner', {
            'fields': ['partner_id']
        }),
        ('Locations', {
            'fields': ['location_id', 'location_dest_id']
        }),
        ('Dates', {
            'fields': ['scheduled_date', 'date_done']
        }),
        ('Status', {
            'fields': ['state', 'priority']
        }),
        ('Additional', {
            'fields': ['company_id', 'note']
        }),
    ]

    actions = ['action_confirm', 'action_assign', 'action_done']

    def action_confirm(self, request, queryset):
        for picking in queryset.filter(state='draft'):
            picking.action_confirm()
        self.message_user(request, f"Confirmed {queryset.count()} pickings.")
    action_confirm.short_description = "Confirm selected pickings"

    def action_assign(self, request, queryset):
        for picking in queryset.filter(state='confirmed'):
            picking.action_assign()
        self.message_user(request, f"Assigned {queryset.count()} pickings.")
    action_assign.short_description = "Assign selected pickings"

    def action_done(self, request, queryset):
        for picking in queryset.filter(state='assigned'):
            picking.action_done()
        self.message_user(request, f"Completed {queryset.count()} pickings.")
    action_done.short_description = "Mark selected pickings as done"


@admin.register(StockMove)
class StockMoveAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'product_id', 'product_uom_qty', 'location_id',
        'location_dest_id', 'state', 'date', 'is_done'
    ]
    list_filter = [
        'state', 'company_id', 'product_id', 'location_id',
        'location_dest_id', 'date', 'is_done'
    ]
    search_fields = ['name', 'origin', 'product_id__name']
    date_hierarchy = 'date'
    readonly_fields = ['is_done']

    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'origin', 'product_id']
        }),
        ('Quantity', {
            'fields': ['product_uom_qty', 'product_uom', 'quantity_done']
        }),
        ('Locations', {
            'fields': ['location_id', 'location_dest_id']
        }),
        ('Dates', {
            'fields': ['date', 'date_expected']
        }),
        ('Status', {
            'fields': ['state', 'is_done']
        }),
        ('References', {
            'fields': ['picking_id', 'production_id', 'raw_material_production_id']
        }),
        ('Additional', {
            'fields': ['company_id', 'reference', 'description_picking']
        }),
    ]

    actions = ['action_confirm', 'action_done']

    def action_confirm(self, request, queryset):
        for move in queryset.filter(state='draft'):
            move._action_confirm()
        self.message_user(request, f"Confirmed {queryset.count()} stock moves.")
    action_confirm.short_description = "Confirm selected stock moves"

    def action_done(self, request, queryset):
        for move in queryset.filter(state='assigned'):
            move._action_done()
        self.message_user(request, f"Completed {queryset.count()} stock moves.")
    action_done.short_description = "Mark selected stock moves as done"


# StockMoveLine model doesn't exist in the current models.py, so we skip it
