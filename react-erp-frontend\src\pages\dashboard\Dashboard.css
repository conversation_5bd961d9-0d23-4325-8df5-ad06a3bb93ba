/* Dashboard Styles */
.dashboard {
  min-height: 100vh;
  background-color: var(--odoo-bg-secondary);
  display: flex;
  flex-direction: column;
}

.dashboard-header {
  background: var(--odoo-white);
  padding: var(--odoo-spacing-lg) var(--odoo-spacing-xl);
  box-shadow: var(--odoo-shadow-sm);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--odoo-border-light);
}

.dashboard-welcome h1 {
  font-size: var(--odoo-font-size-2xl);
  font-weight: var(--odoo-font-weight-bold);
  color: var(--odoo-text-primary);
  margin-bottom: var(--odoo-spacing-xs);
}

.dashboard-welcome p {
  font-size: var(--odoo-font-size-base);
  color: var(--odoo-text-secondary);
  margin: 0;
}

.dashboard-actions {
  display: flex;
  gap: var(--odoo-spacing-sm);
}

.dashboard-content {
  flex: 1;
  padding: var(--odoo-spacing-xl);
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--odoo-spacing-lg);
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-card {
  background: var(--odoo-white);
  border-radius: var(--odoo-border-radius-lg);
  padding: var(--odoo-spacing-lg);
  box-shadow: var(--odoo-shadow-sm);
  border: 1px solid var(--odoo-border-light);
  transition: all var(--odoo-transition-fast);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--odoo-spacing-md);
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--odoo-shadow);
  border-color: var(--odoo-primary);
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: var(--odoo-border-radius);
  background: linear-gradient(135deg, var(--odoo-primary), var(--odoo-primary-light));
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--odoo-white);
  font-size: var(--odoo-font-size-xl);
  flex-shrink: 0;
}

.card-content {
  flex: 1;
}

.card-content h3 {
  font-size: var(--odoo-font-size-lg);
  font-weight: var(--odoo-font-weight-semibold);
  color: var(--odoo-text-primary);
  margin-bottom: var(--odoo-spacing-xs);
}

.card-content p {
  font-size: var(--odoo-font-size-sm);
  color: var(--odoo-text-secondary);
  margin: 0;
  line-height: 1.4;
}

.dashboard-footer {
  background: var(--odoo-white);
  padding: var(--odoo-spacing-md) var(--odoo-spacing-xl);
  border-top: 1px solid var(--odoo-border-light);
  text-align: center;
}

.dashboard-footer p {
  font-size: var(--odoo-font-size-sm);
  color: var(--odoo-text-muted);
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    gap: var(--odoo-spacing-md);
    text-align: center;
    padding: var(--odoo-spacing-md);
  }

  .dashboard-content {
    padding: var(--odoo-spacing-md);
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
    gap: var(--odoo-spacing-md);
  }

  .dashboard-card {
    padding: var(--odoo-spacing-md);
  }

  .card-icon {
    width: 50px;
    height: 50px;
    font-size: var(--odoo-font-size-lg);
  }

  .dashboard-footer {
    padding: var(--odoo-spacing-md);
  }
}

@media (max-width: 480px) {
  .dashboard-welcome h1 {
    font-size: var(--odoo-font-size-xl);
  }

  .dashboard-card {
    flex-direction: column;
    text-align: center;
    gap: var(--odoo-spacing-sm);
  }

  .card-content h3 {
    font-size: var(--odoo-font-size-base);
  }

  .card-content p {
    font-size: var(--odoo-font-size-xs);
  }
}
