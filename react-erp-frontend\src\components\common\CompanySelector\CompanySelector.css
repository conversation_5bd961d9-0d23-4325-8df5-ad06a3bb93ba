/* Company Selector - Odoo Style */
.company-selector {
  position: relative;
  display: inline-block;
}

.company-selector.loading {
  display: flex;
  align-items: center;
  gap: var(--odoo-spacing-sm);
  padding: var(--odoo-spacing-sm);
  color: var(--odoo-text-muted);
  font-size: var(--odoo-font-size-sm);
}

.company-selector-trigger {
  display: flex;
  align-items: center;
  gap: var(--odoo-spacing-sm);
  padding: var(--odoo-spacing-sm) var(--odoo-spacing-md);
  background: var(--odoo-white);
  border: 1px solid var(--odoo-border-medium);
  border-radius: var(--odoo-border-radius);
  cursor: pointer;
  transition: all var(--odoo-transition-fast);
  min-width: 200px;
}

.company-selector-trigger:hover {
  border-color: var(--odoo-primary);
  box-shadow: 0 0 0 0.2rem rgba(113, 75, 103, 0.25);
}

.company-info {
  display: flex;
  align-items: center;
  gap: var(--odoo-spacing-sm);
  flex: 1;
}

.company-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--odoo-primary), var(--odoo-primary-light));
  color: var(--odoo-white);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--odoo-font-size-sm);
  flex-shrink: 0;
}

.company-details {
  flex: 1;
  min-width: 0;
}

.company-name {
  font-size: var(--odoo-font-size-base);
  font-weight: var(--odoo-font-weight-semibold);
  color: var(--odoo-text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.company-code {
  font-size: var(--odoo-font-size-xs);
  color: var(--odoo-text-muted);
  font-family: 'Courier New', monospace;
  font-weight: var(--odoo-font-weight-bold);
}

.dropdown-arrow {
  color: var(--odoo-text-muted);
  font-size: var(--odoo-font-size-sm);
  transition: transform var(--odoo-transition-fast);
}

/* Dropdown */
.company-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--odoo-white);
  border: 1px solid var(--odoo-border-medium);
  border-radius: var(--odoo-border-radius-lg);
  box-shadow: var(--odoo-shadow-lg);
  z-index: 1000;
  margin-top: 4px;
  overflow: hidden;
}

.dropdown-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--odoo-spacing-md);
  border-bottom: 1px solid var(--odoo-border-light);
  background: var(--odoo-bg-light);
}

.dropdown-header h4 {
  font-size: var(--odoo-font-size-base);
  font-weight: var(--odoo-font-weight-semibold);
  color: var(--odoo-text-primary);
  margin: 0;
}

.close-dropdown {
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  color: var(--odoo-text-muted);
  cursor: pointer;
  border-radius: var(--odoo-border-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--odoo-transition-fast);
}

.close-dropdown:hover {
  background: var(--odoo-danger);
  color: var(--odoo-white);
}

/* Company List */
.company-list {
  max-height: 300px;
  overflow-y: auto;
}

.company-item {
  display: flex;
  align-items: center;
  gap: var(--odoo-spacing-md);
  padding: var(--odoo-spacing-md);
  cursor: pointer;
  transition: background-color var(--odoo-transition-fast);
  border-bottom: 1px solid var(--odoo-border-light);
}

.company-item:last-child {
  border-bottom: none;
}

.company-item:hover {
  background: var(--odoo-bg-light);
}

.company-item.active {
  background: rgba(113, 75, 103, 0.1);
  border-left: 3px solid var(--odoo-primary);
}

.company-item .company-icon {
  width: 40px;
  height: 40px;
  font-size: var(--odoo-font-size-base);
}

.company-item .company-details {
  flex: 1;
}

.company-item .company-name {
  font-size: var(--odoo-font-size-name);
  margin-bottom: 2px;
}

.company-meta {
  font-size: var(--odoo-font-size-xs);
  color: var(--odoo-text-secondary);
  margin-top: 2px;
}

.active-indicator {
  color: var(--odoo-success);
  font-size: var(--odoo-font-size-base);
}

/* Dropdown Footer */
.dropdown-footer {
  padding: var(--odoo-spacing-md);
  border-top: 1px solid var(--odoo-border-light);
  background: var(--odoo-bg-light);
}

.create-company-btn {
  width: 100%;
  padding: var(--odoo-spacing-sm);
  border: 1px dashed var(--odoo-border-medium);
  background: none;
  color: var(--odoo-text-secondary);
  border-radius: var(--odoo-border-radius);
  cursor: pointer;
  font-size: var(--odoo-font-size-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--odoo-spacing-sm);
  transition: all var(--odoo-transition-fast);
}

.create-company-btn:hover {
  border-color: var(--odoo-primary);
  color: var(--odoo-primary);
  background: rgba(113, 75, 103, 0.05);
}

/* Dropdown Overlay */
.dropdown-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}

/* Responsive Design */
@media (max-width: 768px) {
  .company-selector-trigger {
    min-width: 150px;
  }
  
  .company-dropdown {
    left: -50px;
    right: -50px;
    width: auto;
  }
  
  .company-list {
    max-height: 250px;
  }
}
