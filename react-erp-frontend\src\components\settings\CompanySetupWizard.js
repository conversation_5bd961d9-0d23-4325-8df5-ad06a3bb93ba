import React, { useState, useEffect } from 'react';
import { settingsAPI } from '../../services/api';
import Button from '../common/Button/Button';
import './CompanySetupWizard.css';

const CompanySetupWizard = ({ isOpen, onClose, onCompanyCreated }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  
  // Available options
  const [countries, setCountries] = useState([]);
  const [currencies, setCurrencies] = useState([]);
  
  // Form data
  const [companyData, setCompanyData] = useState({
    // Step 1: Basic Information
    name: '',
    code: '',
    email: '',
    phone: '',
    website: '',
    vat: '',
    
    // Step 2: Address Information
    street: '',
    street2: '',
    city: '',
    zip: '',
    country: '',
    
    // Step 3: Localization & Financial Setup
    currency: '',
    localization: '', // Country-specific localization package
    fiscal_year_end: '12-31', // MM-DD format (Odoo standard)

    // Step 4: Chart of Accounts (based on localization)
    chart_template: '', // Will be populated based on localization
    install_sample_data: false,
    
    active: true
  });

  const steps = [
    { id: 1, title: 'Company Information', icon: 'fas fa-building' },
    { id: 2, title: 'Address & Contact', icon: 'fas fa-map-marker-alt' },
    { id: 3, title: 'Localization', icon: 'fas fa-globe' },
    { id: 4, title: 'Accounting Setup', icon: 'fas fa-chart-bar' }
  ];

  useEffect(() => {
    if (isOpen) {
      loadOptions();
    }
  }, [isOpen]);

  const loadOptions = async () => {
    try {
      // Load countries and currencies
      const [countriesResponse, currenciesResponse] = await Promise.all([
        settingsAPI.getCountries(),
        settingsAPI.getCurrencies()
      ]);
      
      setCountries(countriesResponse.results || countriesResponse);
      setCurrencies(currenciesResponse.results || currenciesResponse);
    } catch (error) {
      console.error('Error loading options:', error);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setCompanyData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // Auto-generate company code from name
    if (name === 'name' && value) {
      const code = value.replace(/[^A-Za-z0-9]/g, '').toUpperCase().substring(0, 10);
      setCompanyData(prev => ({
        ...prev,
        code: code
      }));
    }
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateStep = (step) => {
    const newErrors = {};
    
    switch (step) {
      case 1:
        if (!companyData.name.trim()) newErrors.name = 'Company name is required';
        if (!companyData.code.trim()) newErrors.code = 'Company code is required';
        if (companyData.email && !/\S+@\S+\.\S+/.test(companyData.email)) {
          newErrors.email = 'Invalid email format';
        }
        break;
        
      case 2:
        if (!companyData.country) newErrors.country = 'Country is required';
        if (!companyData.city.trim()) newErrors.city = 'City is required';
        break;
        
      case 3:
        if (!companyData.currency) newErrors.currency = 'Currency is required';
        break;
        
      case 4:
        if (!companyData.chart_template) newErrors.chart_template = 'Chart of accounts template is required';
        break;
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, 4));
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async () => {
    if (!validateStep(4)) return;
    
    setLoading(true);
    try {
      console.log('Creating company with setup:', companyData);
      
      // Create company with full setup
      const response = await settingsAPI.createCompanyWithSetup(companyData);
      
      console.log('Company created with setup:', response);
      
      // Reset form
      setCompanyData({
        name: '', code: '', email: '', phone: '', website: '', vat: '',
        street: '', street2: '', city: '', zip: '', country: '',
        currency: '', localization: '', fiscal_year_end: '12-31',
        chart_template: '', install_sample_data: false, active: true
      });
      setCurrentStep(1);
      
      // Notify parent component
      onCompanyCreated(response);
      
      // Close wizard
      onClose();
      
    } catch (error) {
      console.error('Error creating company:', error);
      if (error.response?.data) {
        setErrors(error.response.data);
      } else {
        alert('Failed to create company. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setCurrentStep(1);
      setErrors({});
      onClose();
    }
  };

  if (!isOpen) return null;

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="step-content">
            <h3>Basic Company Information</h3>
            <p>Enter the basic details about your company</p>
            
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="name" className="required">Company Name</label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={companyData.name}
                  onChange={handleInputChange}
                  className={errors.name ? 'error' : ''}
                  placeholder="My Company Ltd."
                  disabled={loading}
                />
                {errors.name && <span className="error-text">{errors.name}</span>}
              </div>
              
              <div className="form-group">
                <label htmlFor="code" className="required">Company Code</label>
                <input
                  type="text"
                  id="code"
                  name="code"
                  value={companyData.code}
                  onChange={handleInputChange}
                  className={errors.code ? 'error' : ''}
                  placeholder="MYCOMP"
                  maxLength="10"
                  disabled={loading}
                />
                {errors.code && <span className="error-text">{errors.code}</span>}
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="email">Email</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={companyData.email}
                  onChange={handleInputChange}
                  className={errors.email ? 'error' : ''}
                  placeholder="<EMAIL>"
                  disabled={loading}
                />
                {errors.email && <span className="error-text">{errors.email}</span>}
              </div>
              
              <div className="form-group">
                <label htmlFor="phone">Phone</label>
                <input
                  type="text"
                  id="phone"
                  name="phone"
                  value={companyData.phone}
                  onChange={handleInputChange}
                  placeholder="******-0123"
                  disabled={loading}
                />
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="website">Website</label>
                <input
                  type="url"
                  id="website"
                  name="website"
                  value={companyData.website}
                  onChange={handleInputChange}
                  placeholder="https://mycompany.com"
                  disabled={loading}
                />
              </div>
              
              <div className="form-group">
                <label htmlFor="vat">Tax ID / VAT</label>
                <input
                  type="text"
                  id="vat"
                  name="vat"
                  value={companyData.vat}
                  onChange={handleInputChange}
                  placeholder="US123456789"
                  disabled={loading}
                />
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="step-content">
            <h3>Address Information</h3>
            <p>Enter your company's address details</p>
            
            <div className="form-row">
              <div className="form-group full-width">
                <label htmlFor="street">Street Address</label>
                <input
                  type="text"
                  id="street"
                  name="street"
                  value={companyData.street}
                  onChange={handleInputChange}
                  placeholder="123 Business Street"
                  disabled={loading}
                />
              </div>
            </div>

            <div className="form-row">
              <div className="form-group full-width">
                <label htmlFor="street2">Street Address 2</label>
                <input
                  type="text"
                  id="street2"
                  name="street2"
                  value={companyData.street2}
                  onChange={handleInputChange}
                  placeholder="Suite 100"
                  disabled={loading}
                />
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="city" className="required">City</label>
                <input
                  type="text"
                  id="city"
                  name="city"
                  value={companyData.city}
                  onChange={handleInputChange}
                  className={errors.city ? 'error' : ''}
                  placeholder="New York"
                  disabled={loading}
                />
                {errors.city && <span className="error-text">{errors.city}</span>}
              </div>
              
              <div className="form-group">
                <label htmlFor="zip">ZIP / Postal Code</label>
                <input
                  type="text"
                  id="zip"
                  name="zip"
                  value={companyData.zip}
                  onChange={handleInputChange}
                  placeholder="10001"
                  disabled={loading}
                />
              </div>
            </div>

            <div className="form-row">
              <div className="form-group full-width">
                <label htmlFor="country" className="required">Country</label>
                <select
                  id="country"
                  name="country"
                  value={companyData.country}
                  onChange={handleInputChange}
                  className={errors.country ? 'error' : ''}
                  disabled={loading}
                >
                  <option value="">Select a country</option>
                  {countries.map(country => (
                    <option key={country.id} value={country.id}>
                      {country.name}
                    </option>
                  ))}
                </select>
                {errors.country && <span className="error-text">{errors.country}</span>}
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="step-content">
            <h3>Localization</h3>
            <p>Configure your company's localization and financial settings</p>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="currency" className="required">Currency</label>
                <select
                  id="currency"
                  name="currency"
                  value={companyData.currency}
                  onChange={handleInputChange}
                  className={errors.currency ? 'error' : ''}
                  disabled={loading}
                >
                  <option value="">Select currency</option>
                  {currencies.map(currency => (
                    <option key={currency.id} value={currency.id}>
                      {currency.name} - {currency.full_name} ({currency.symbol})
                    </option>
                  ))}
                </select>
                {errors.currency && <span className="error-text">{errors.currency}</span>}
              </div>

              <div className="form-group">
                <label htmlFor="localization">Localization Package</label>
                <select
                  id="localization"
                  name="localization"
                  value={companyData.localization}
                  onChange={handleInputChange}
                  disabled={loading}
                >
                  <option value="">Generic - International</option>
                  <option value="us">United States - US GAAP</option>
                  <option value="uk">United Kingdom - UK GAAP</option>
                  <option value="fr">France - French GAAP</option>
                  <option value="de">Germany - German GAAP</option>
                  <option value="ca">Canada - Canadian GAAP</option>
                  <option value="au">Australia - Australian GAAP</option>
                </select>
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="fiscal_year_end">Fiscal Year End</label>
                <select
                  id="fiscal_year_end"
                  name="fiscal_year_end"
                  value={companyData.fiscal_year_end}
                  onChange={handleInputChange}
                  disabled={loading}
                >
                  <option value="12-31">December 31 (Calendar Year)</option>
                  <option value="03-31">March 31</option>
                  <option value="06-30">June 30</option>
                  <option value="09-30">September 30</option>
                </select>
              </div>

              <div className="form-group">
                <div className="info-box">
                  <i className="fas fa-info-circle"></i>
                  <div>
                    <strong>Localization Package</strong>
                    <p>Includes country-specific chart of accounts, taxes, and reports</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 4:
        const chartTemplates = companyData.localization ? [
          { id: 'generic', name: 'Generic Chart of Accounts', description: 'Basic international chart of accounts' },
          { id: 'us_gaap', name: 'US GAAP', description: 'United States Generally Accepted Accounting Principles' },
          { id: 'ifrs', name: 'IFRS', description: 'International Financial Reporting Standards' }
        ] : [
          { id: 'generic', name: 'Generic Chart of Accounts', description: 'Basic international chart of accounts' }
        ];

        return (
          <div className="step-content">
            <h3>Accounting Setup</h3>
            <p>Configure your chart of accounts and accounting settings</p>

            <div className="form-row">
              <div className="form-group full-width">
                <label htmlFor="chart_template" className="required">Chart of Accounts Template</label>
                <div className="chart-templates">
                  {chartTemplates.map(template => (
                    <div
                      key={template.id}
                      className={`chart-template ${companyData.chart_template === template.id ? 'selected' : ''}`}
                      onClick={() => setCompanyData(prev => ({ ...prev, chart_template: template.id }))}
                    >
                      <div className="template-header">
                        <input
                          type="radio"
                          name="chart_template"
                          value={template.id}
                          checked={companyData.chart_template === template.id}
                          onChange={handleInputChange}
                          disabled={loading}
                        />
                        <h4>{template.name}</h4>
                      </div>
                      <p>{template.description}</p>
                    </div>
                  ))}
                </div>
                {errors.chart_template && <span className="error-text">{errors.chart_template}</span>}
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    name="install_sample_data"
                    checked={companyData.install_sample_data}
                    onChange={handleInputChange}
                    disabled={loading}
                  />
                  <span className="checkbox-text">Install sample data</span>
                </label>
                <small style={{ color: 'var(--odoo-text-muted)', fontSize: 'var(--odoo-font-size-xs)', marginTop: '4px' }}>
                  Recommended for testing and learning purposes
                </small>
              </div>

              <div className="form-group">
                <div className="info-box">
                  <i className="fas fa-lightbulb"></i>
                  <div>
                    <strong>Chart of Accounts</strong>
                    <p>The chart of accounts will be automatically configured based on your localization</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="modal-overlay" onClick={handleClose}>
      <div className="wizard-modal" onClick={(e) => e.stopPropagation()}>
        <div className="wizard-header">
          <h2>
            <i className="fas fa-magic"></i>
            Company Setup Wizard
          </h2>
          <button className="modal-close" onClick={handleClose} disabled={loading}>
            <i className="fas fa-times"></i>
          </button>
        </div>

        {/* Step Progress */}
        <div className="step-progress">
          {steps.map(step => (
            <div
              key={step.id}
              className={`step-item ${currentStep >= step.id ? 'active' : ''} ${currentStep === step.id ? 'current' : ''}`}
            >
              <div className="step-icon">
                <i className={step.icon}></i>
              </div>
              <div className="step-info">
                <div className="step-number">Step {step.id}</div>
                <div className="step-title">{step.title}</div>
              </div>
            </div>
          ))}
        </div>

        {/* Step Content */}
        <div className="wizard-content">
          {renderStepContent()}
        </div>

        {/* Navigation */}
        <div className="wizard-actions">
          <div className="left-actions">
            {currentStep > 1 && (
              <Button
                type="button"
                variant="secondary"
                onClick={handlePrevious}
                disabled={loading}
              >
                <i className="fas fa-arrow-left"></i>
                Previous
              </Button>
            )}
          </div>
          
          <div className="right-actions">
            {currentStep < 4 ? (
              <Button
                type="button"
                variant="primary"
                onClick={handleNext}
                disabled={loading}
              >
                Next
                <i className="fas fa-arrow-right"></i>
              </Button>
            ) : (
              <Button
                type="button"
                variant="primary"
                onClick={handleSubmit}
                loading={loading}
                disabled={loading}
              >
                {loading ? 'Creating Company...' : 'Create Company'}
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompanySetupWizard;
