from django.core.management.base import BaseCommand
from django.contrib.auth.models import User, Group
from core.models import UserRole, UserRoleAssignment, UserProfile, Company, Currency, Country
from django.db import transaction


class Command(BaseCommand):
    help = 'Setup default roles and users for the ERP system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Reset all roles and assignments',
        )

    def handle(self, *args, **options):
        if options['reset']:
            self.stdout.write('Resetting roles and assignments...')
            UserRoleAssignment.objects.all().delete()
            UserRole.objects.all().delete()

        with transaction.atomic():
            self.create_default_roles()
            self.create_demo_users()
            self.assign_roles()

        self.stdout.write(
            self.style.SUCCESS('Successfully set up roles and users!')
        )

    def create_default_roles(self):
        """Create default ERP roles"""
        self.stdout.write('Creating default roles...')

        # Get admin user for create_uid/write_uid
        admin_user = User.objects.get(username='admin')

        roles_data = [
            {
                'name': 'System Administrator',
                'code': 'admin',
                'description': 'Full system access with all permissions',
                'permissions': {
                    'can_access_accounting': True,
                    'can_access_sales': True,
                    'can_access_purchases': True,
                    'can_access_inventory': True,
                    'can_access_hr': True,
                    'can_access_project': True,
                    'can_access_manufacturing': True,
                    'can_access_crm': True,
                    'can_create': True,
                    'can_read': True,
                    'can_update': True,
                    'can_delete': True,
                    'can_approve_orders': True,
                    'can_manage_users': True,
                    'can_view_reports': True,
                    'can_export_data': True,
                }
            },
            {
                'name': 'Sales Manager',
                'code': 'sales_manager',
                'description': 'Manage sales operations and team',
                'permissions': {
                    'can_access_sales': True,
                    'can_access_crm': True,
                    'can_access_inventory': True,
                    'can_create': True,
                    'can_read': True,
                    'can_update': True,
                    'can_delete': False,
                    'can_approve_orders': True,
                    'can_view_reports': True,
                    'can_export_data': True,
                }
            },
            {
                'name': 'Sales User',
                'code': 'sales_user',
                'description': 'Create and manage sales orders',
                'permissions': {
                    'can_access_sales': True,
                    'can_access_crm': True,
                    'can_create': True,
                    'can_read': True,
                    'can_update': True,
                    'can_view_reports': True,
                }
            },
            {
                'name': 'Accountant',
                'code': 'accountant',
                'description': 'Manage accounting and financial operations',
                'permissions': {
                    'can_access_accounting': True,
                    'can_access_sales': True,
                    'can_access_purchases': True,
                    'can_create': True,
                    'can_read': True,
                    'can_update': True,
                    'can_view_reports': True,
                    'can_export_data': True,
                }
            },
            {
                'name': 'Purchase Manager',
                'code': 'purchase_manager',
                'description': 'Manage purchase operations',
                'permissions': {
                    'can_access_purchases': True,
                    'can_access_inventory': True,
                    'can_create': True,
                    'can_read': True,
                    'can_update': True,
                    'can_approve_orders': True,
                    'can_view_reports': True,
                    'can_export_data': True,
                }
            },
            {
                'name': 'Inventory Manager',
                'code': 'inventory_manager',
                'description': 'Manage inventory and stock operations',
                'permissions': {
                    'can_access_inventory': True,
                    'can_access_sales': True,
                    'can_access_purchases': True,
                    'can_create': True,
                    'can_read': True,
                    'can_update': True,
                    'can_view_reports': True,
                    'can_export_data': True,
                }
            },
            {
                'name': 'HR Manager',
                'code': 'hr_manager',
                'description': 'Manage human resources',
                'permissions': {
                    'can_access_hr': True,
                    'can_create': True,
                    'can_read': True,
                    'can_update': True,
                    'can_view_reports': True,
                    'can_export_data': True,
                }
            },
            {
                'name': 'Read Only User',
                'code': 'readonly',
                'description': 'View-only access to all modules',
                'permissions': {
                    'can_access_accounting': True,
                    'can_access_sales': True,
                    'can_access_purchases': True,
                    'can_access_inventory': True,
                    'can_access_hr': True,
                    'can_access_project': True,
                    'can_access_manufacturing': True,
                    'can_access_crm': True,
                    'can_read': True,
                    'can_view_reports': True,
                }
            },
        ]

        for role_data in roles_data:
            permissions = role_data.pop('permissions')

            # Add create_uid and write_uid to defaults
            defaults = role_data.copy()
            defaults.update({
                'create_uid': admin_user,
                'write_uid': admin_user,
            })

            role, created = UserRole.objects.get_or_create(
                code=role_data['code'],
                defaults=defaults
            )
            
            if created or True:  # Update permissions even if role exists
                for perm, value in permissions.items():
                    setattr(role, perm, value)
                role.save()
                
                self.stdout.write(f'  ✓ Created/Updated role: {role.name}')

    def create_demo_users(self):
        """Create demo users for testing"""
        self.stdout.write('Creating demo users...')

        # Get admin user for create_uid/write_uid
        admin_user = User.objects.get(username='admin')

        users_data = [
            {
                'username': 'sales_manager',
                'email': '<EMAIL>',
                'first_name': 'John',
                'last_name': 'Smith',
                'password': 'demo123',
                'role_code': 'sales_manager'
            },
            {
                'username': 'sales_user',
                'email': '<EMAIL>',
                'first_name': 'Sarah',
                'last_name': 'Johnson',
                'password': 'demo123',
                'role_code': 'sales_user'
            },
            {
                'username': 'accountant',
                'email': '<EMAIL>',
                'first_name': 'Mike',
                'last_name': 'Davis',
                'password': 'demo123',
                'role_code': 'accountant'
            },
            {
                'username': 'purchase_manager',
                'email': '<EMAIL>',
                'first_name': 'Lisa',
                'last_name': 'Wilson',
                'password': 'demo123',
                'role_code': 'purchase_manager'
            },
            {
                'username': 'readonly_user',
                'email': '<EMAIL>',
                'first_name': 'Tom',
                'last_name': 'Brown',
                'password': 'demo123',
                'role_code': 'readonly'
            },
        ]

        for user_data in users_data:
            role_code = user_data.pop('role_code')
            password = user_data.pop('password')
            
            user, created = User.objects.get_or_create(
                username=user_data['username'],
                defaults=user_data
            )
            
            if created:
                user.set_password(password)
                user.save()
                
                # Create user profile
                profile, _ = UserProfile.objects.get_or_create(
                    user=user,
                    defaults={
                        'create_uid': admin_user,
                        'write_uid': admin_user,
                    }
                )
                
                self.stdout.write(f'  ✓ Created user: {user.username}')
            else:
                self.stdout.write(f'  - User already exists: {user.username}')

    def assign_roles(self):
        """Assign roles to users"""
        self.stdout.write('Assigning roles to users...')

        # Get admin user for create_uid/write_uid
        admin_user = User.objects.get(username='admin')
        
        # Assign admin role to admin user
        admin_role = UserRole.objects.get(code='admin')
        assignment, created = UserRoleAssignment.objects.get_or_create(
            user=admin_user,
            role=admin_role,
            defaults={
                'create_uid': admin_user,
                'write_uid': admin_user,
            }
        )
        if created:
            self.stdout.write(f'  ✓ Assigned {admin_role.name} to {admin_user.username}')

        # Assign roles to demo users
        role_assignments = [
            ('sales_manager', 'sales_manager'),
            ('sales_user', 'sales_user'),
            ('accountant', 'accountant'),
            ('purchase_manager', 'purchase_manager'),
            ('readonly_user', 'readonly'),
        ]

        for username, role_code in role_assignments:
            try:
                user = User.objects.get(username=username)
                role = UserRole.objects.get(code=role_code)
                
                assignment, created = UserRoleAssignment.objects.get_or_create(
                    user=user,
                    role=role,
                    defaults={
                        'create_uid': admin_user,
                        'write_uid': admin_user,
                    }
                )
                
                if created:
                    self.stdout.write(f'  ✓ Assigned {role.name} to {user.username}')
                else:
                    self.stdout.write(f'  - Role already assigned: {role.name} to {user.username}')
                    
            except (User.DoesNotExist, UserRole.DoesNotExist) as e:
                self.stdout.write(f'  ✗ Error assigning role: {e}')

        self.stdout.write('\n' + '='*50)
        self.stdout.write('DEMO USERS CREATED:')
        self.stdout.write('='*50)
        self.stdout.write('Username: admin | Password: admin | Role: System Administrator')
        self.stdout.write('Username: sales_manager | Password: demo123 | Role: Sales Manager')
        self.stdout.write('Username: sales_user | Password: demo123 | Role: Sales User')
        self.stdout.write('Username: accountant | Password: demo123 | Role: Accountant')
        self.stdout.write('Username: purchase_manager | Password: demo123 | Role: Purchase Manager')
        self.stdout.write('Username: readonly_user | Password: demo123 | Role: Read Only User')
        self.stdout.write('='*50)
