import React, { useState } from 'react';
import './Input.css';

const Input = ({
  type = 'text',
  label,
  placeholder,
  value,
  onChange,
  onBlur,
  onFocus,
  error,
  disabled = false,
  required = false,
  icon = null,
  iconPosition = 'left',
  className = '',
  id,
  name,
  autoComplete,
  ...props
}) => {
  const [focused, setFocused] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const inputId = id || name || `input-${Math.random().toString(36).substr(2, 9)}`;

  const handleFocus = (e) => {
    setFocused(true);
    if (onFocus) onFocus(e);
  };

  const handleBlur = (e) => {
    setFocused(false);
    if (onBlur) onBlur(e);
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const inputClass = [
    'odoo-input',
    focused ? 'odoo-input--focused' : '',
    error ? 'odoo-input--error' : '',
    disabled ? 'odoo-input--disabled' : '',
    icon ? `odoo-input--with-icon-${iconPosition}` : '',
    className
  ].filter(Boolean).join(' ');

  const containerClass = [
    'odoo-input-container',
    focused ? 'odoo-input-container--focused' : '',
    error ? 'odoo-input-container--error' : '',
    disabled ? 'odoo-input-container--disabled' : ''
  ].filter(Boolean).join(' ');

  return (
    <div className={containerClass}>
      {label && (
        <label htmlFor={inputId} className="odoo-input-label">
          {label}
          {required && <span className="odoo-input-required">*</span>}
        </label>
      )}
      
      <div className="odoo-input-wrapper">
        {icon && iconPosition === 'left' && (
          <i className={`${icon} odoo-input-icon odoo-input-icon--left`}></i>
        )}
        
        <input
          id={inputId}
          name={name}
          type={type === 'password' && showPassword ? 'text' : type}
          className={inputClass}
          placeholder={placeholder}
          value={value}
          onChange={onChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          disabled={disabled}
          required={required}
          autoComplete={autoComplete}
          {...props}
        />
        
        {type === 'password' && (
          <button
            type="button"
            className="odoo-input-password-toggle"
            onClick={togglePasswordVisibility}
            tabIndex={-1}
          >
            <i className={`fas ${showPassword ? 'fa-eye-slash' : 'fa-eye'}`}></i>
          </button>
        )}
        
        {icon && iconPosition === 'right' && type !== 'password' && (
          <i className={`${icon} odoo-input-icon odoo-input-icon--right`}></i>
        )}
      </div>
      
      {error && (
        <div className="odoo-input-error">
          <i className="fas fa-exclamation-circle"></i>
          <span>{error}</span>
        </div>
      )}
    </div>
  );
};

export default Input;
