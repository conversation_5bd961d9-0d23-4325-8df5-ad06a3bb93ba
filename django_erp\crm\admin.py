from django.contrib import admin
from .models import (
    CrmTeam, CrmStage, CrmLostReason, CrmLead, 
    CrmActivityType, CrmActivity, CrmTag
)


@admin.register(CrmTeam)
class CrmTeamAdmin(admin.ModelAdmin):
    list_display = ['name', 'user', 'company', 'active', 'sequence']
    list_filter = ['active', 'company', 'use_leads', 'use_opportunities']
    search_fields = ['name']
    filter_horizontal = ['members']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'active', 'sequence', 'company']
        }),
        ('Configuration', {
            'fields': ['use_leads', 'use_opportunities']
        }),
        ('Team Management', {
            'fields': ['user', 'members']
        }),
        ('Targets', {
            'fields': ['invoiced_target']
        }),
    ]


@admin.register(CrmStage)
class CrmStageAdmin(admin.ModelAdmin):
    list_display = ['name', 'sequence', 'probability', 'is_won', 'fold']
    list_filter = ['is_won', 'fold']
    search_fields = ['name']
    filter_horizontal = ['teams']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'sequence']
        }),
        ('Configuration', {
            'fields': ['probability', 'is_won', 'fold']
        }),
        ('Team Restriction', {
            'fields': ['teams']
        }),
        ('Requirements', {
            'fields': ['requirements']
        }),
    ]


@admin.register(CrmLostReason)
class CrmLostReasonAdmin(admin.ModelAdmin):
    list_display = ['name', 'active']
    list_filter = ['active']
    search_fields = ['name']


@admin.register(CrmLead)
class CrmLeadAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'partner_name', 'type', 'stage', 'user', 
        'expected_revenue', 'probability', 'date_deadline'
    ]
    list_filter = [
        'type', 'priority', 'stage', 'team', 'user', 
        'country', 'date_deadline'
    ]
    search_fields = ['name', 'partner_name', 'contact_name', 'email_from', 'phone']
    readonly_fields = ['prorated_revenue', 'date_last_stage_update', 'day_open', 'day_close']
    
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'active', 'type', 'priority']
        }),
        ('Contact Information', {
            'fields': [
                'partner', 'partner_name', 'contact_name', 'title',
                'email_from', 'phone', 'mobile', 'website'
            ]
        }),
        ('Address', {
            'fields': ['street', 'street2', 'city', 'state', 'zip', 'country']
        }),
        ('Sales Information', {
            'fields': ['user', 'team', 'stage']
        }),
        ('Financial Information', {
            'fields': [
                'expected_revenue', 'prorated_revenue', 'probability',
                'recurring_revenue', 'recurring_revenue_monthly',
                'currency'
            ]
        }),
        ('Dates', {
            'fields': [
                'date_deadline', 'date_closed', 'date_conversion',
                'date_last_stage_update', 'day_open', 'day_close'
            ]
        }),
        ('Additional Information', {
            'fields': ['description', 'source', 'referred', 'lost_reason']
        }),
        ('System', {
            'fields': ['company']
        }),
    ]
    
    actions = ['convert_to_opportunity', 'mark_as_won', 'mark_as_lost']
    
    def convert_to_opportunity(self, request, queryset):
        for lead in queryset.filter(type='lead'):
            lead.convert_to_opportunity()
        self.message_user(request, f"Converted {queryset.count()} leads to opportunities.")
    convert_to_opportunity.short_description = "Convert selected leads to opportunities"
    
    def mark_as_won(self, request, queryset):
        for opportunity in queryset.filter(type='opportunity'):
            opportunity.action_set_won()
        self.message_user(request, f"Marked {queryset.count()} opportunities as won.")
    mark_as_won.short_description = "Mark selected opportunities as won"
    
    def mark_as_lost(self, request, queryset):
        for opportunity in queryset.filter(type='opportunity'):
            opportunity.action_set_lost()
        self.message_user(request, f"Marked {queryset.count()} opportunities as lost.")
    mark_as_lost.short_description = "Mark selected opportunities as lost"


@admin.register(CrmActivityType)
class CrmActivityTypeAdmin(admin.ModelAdmin):
    list_display = ['name', 'category', 'sequence', 'delay_count', 'delay_unit']
    list_filter = ['category', 'delay_unit', 'chaining_type']
    search_fields = ['name']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'category', 'sequence']
        }),
        ('Timing', {
            'fields': ['delay_count', 'delay_unit']
        }),
        ('Configuration', {
            'fields': ['summary', 'default_note']
        }),
        ('Automation', {
            'fields': ['chaining_type']
        }),
    ]


@admin.register(CrmActivity)
class CrmActivityAdmin(admin.ModelAdmin):
    list_display = [
        'summary', 'activity_type', 'user', 'lead', 
        'date_deadline', 'state'
    ]
    list_filter = ['state', 'activity_type', 'user', 'date_deadline']
    search_fields = ['summary', 'note']
    readonly_fields = ['state', 'date_done']
    
    fieldsets = [
        ('Basic Information', {
            'fields': ['summary', 'note', 'activity_type']
        }),
        ('Assignment', {
            'fields': ['user', 'lead']
        }),
        ('Dates', {
            'fields': ['date_deadline', 'date_done', 'state']
        }),
        ('Feedback', {
            'fields': ['feedback']
        }),
        ('System', {
            'fields': ['res_model', 'res']
        }),
    ]
    
    actions = ['mark_as_done']
    
    def mark_as_done(self, request, queryset):
        for activity in queryset:
            activity.action_done()
        self.message_user(request, f"Marked {queryset.count()} activities as done.")
    mark_as_done.short_description = "Mark selected activities as done"


@admin.register(CrmTag)
class CrmTagAdmin(admin.ModelAdmin):
    list_display = ['name', 'color']
    search_fields = ['name']
