/* Odoo Enterprise Color Variables */
:root {
  /* Primary Colors - Odoo Purple Theme */
  --odoo-primary: #714B67;
  --odoo-primary-light: #8B5A7F;
  --odoo-primary-dark: #5A3B52;
  --odoo-primary-hover: #6A4460;
  
  /* Secondary Colors */
  --odoo-secondary: #17A2B8;
  --odoo-success: #28A745;
  --odoo-warning: #FFC107;
  --odoo-danger: #DC3545;
  --odoo-info: #17A2B8;
  
  /* Neutral Colors */
  --odoo-white: #FFFFFF;
  --odoo-light: #F8F9FA;
  --odoo-light-gray: #E9ECEF;
  --odoo-gray: #6C757D;
  --odoo-dark-gray: #495057;
  --odoo-dark: #343A40;
  --odoo-black: #000000;
  
  /* Background Colors */
  --odoo-bg-primary: #FFFFFF;
  --odoo-bg-secondary: #F8F9FA;
  --odoo-bg-light: #E9ECEF;
  --odoo-bg-dark: #343A40;
  
  /* Border Colors */
  --odoo-border-light: #DEE2E6;
  --odoo-border-medium: #CED4DA;
  --odoo-border-dark: #ADB5BD;
  
  /* Text Colors */
  --odoo-text-primary: #212529;
  --odoo-text-secondary: #6C757D;
  --odoo-text-muted: #868E96;
  --odoo-text-white: #FFFFFF;
  
  /* Shadow */
  --odoo-shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --odoo-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --odoo-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  
  /* Border Radius */
  --odoo-border-radius-sm: 0.25rem;
  --odoo-border-radius: 0.375rem;
  --odoo-border-radius-lg: 0.5rem;
  --odoo-border-radius-xl: 1rem;
  
  /* Spacing */
  --odoo-spacing-xs: 0.25rem;
  --odoo-spacing-sm: 0.5rem;
  --odoo-spacing-md: 1rem;
  --odoo-spacing-lg: 1.5rem;
  --odoo-spacing-xl: 3rem;
  
  /* Font Sizes - Updated to match Odoo's larger typography */
  --odoo-font-size-xs: 0.75rem;      /* 12px - Small labels, badges */
  --odoo-font-size-sm: 0.875rem;     /* 14px - Secondary text, descriptions */
  --odoo-font-size-base: 1rem;       /* 16px - Body text, form inputs */
  --odoo-font-size-lg: 1.125rem;     /* 18px - Larger body text */
  --odoo-font-size-xl: 1.25rem;      /* 20px - Small headings */
  --odoo-font-size-2xl: 1.5rem;      /* 24px - Medium headings */
  --odoo-font-size-3xl: 1.875rem;    /* 30px - Large headings */
  --odoo-font-size-4xl: 2.25rem;     /* 36px - Extra large headings */

  /* Specific font sizes for common elements */
  --odoo-font-size-name: 1.125rem;   /* 18px - Names (company, account, etc.) */
  --odoo-font-size-title: 1.25rem;   /* 20px - Card titles, section headers */
  --odoo-font-size-heading: 1.5rem;  /* 24px - Page headings */
  --odoo-font-size-display: 1.875rem; /* 30px - Display text */
  
  /* Font Families - Exact Odoo System Fonts */
  --odoo-font-family-sans-serif: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Ubuntu, "Noto Sans", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --odoo-font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;

  /* Font Weights - Exact Odoo Definitions */
  --odoo-font-weight-normal: 400;
  --odoo-font-weight-medium: 500;
  --odoo-font-weight-bold: 700;
  
  /* Line Heights */
  --odoo-line-height-sm: 1.25;
  --odoo-line-height-base: 1.5;
  --odoo-line-height-lg: 1.75;
  
  /* Transitions */
  --odoo-transition-fast: 0.15s ease-in-out;
  --odoo-transition-base: 0.3s ease-in-out;
  --odoo-transition-slow: 0.5s ease-in-out;
  
  /* Z-Index */
  --odoo-z-dropdown: 1000;
  --odoo-z-sticky: 1020;
  --odoo-z-fixed: 1030;
  --odoo-z-modal-backdrop: 1040;
  --odoo-z-modal: 1050;
  --odoo-z-popover: 1060;
  --odoo-z-tooltip: 1070;
  
  /* Breakpoints */
  --odoo-breakpoint-sm: 576px;
  --odoo-breakpoint-md: 768px;
  --odoo-breakpoint-lg: 992px;
  --odoo-breakpoint-xl: 1200px;
  --odoo-breakpoint-xxl: 1400px;
}

/* Typography Utility Classes */
.odoo-text-name {
  font-size: var(--odoo-font-size-name);
  font-weight: var(--odoo-font-weight-semibold);
  color: var(--odoo-text-primary);
}

.odoo-text-title {
  font-size: var(--odoo-font-size-title);
  font-weight: var(--odoo-font-weight-semibold);
  color: var(--odoo-text-primary);
}

.odoo-text-heading {
  font-size: var(--odoo-font-size-heading);
  font-weight: var(--odoo-font-weight-bold);
  color: var(--odoo-text-primary);
}

.odoo-text-display {
  font-size: var(--odoo-font-size-display);
  font-weight: var(--odoo-font-weight-bold);
  color: var(--odoo-text-primary);
}

.odoo-text-large {
  font-size: var(--odoo-font-size-lg);
  font-weight: var(--odoo-font-weight-medium);
}

.odoo-text-small {
  font-size: var(--odoo-font-size-sm);
  color: var(--odoo-text-secondary);
}

.odoo-text-muted {
  color: var(--odoo-text-muted);
  font-size: var(--odoo-font-size-sm);
}
