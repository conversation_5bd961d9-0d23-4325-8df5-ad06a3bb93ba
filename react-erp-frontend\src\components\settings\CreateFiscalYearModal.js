import React, { useState } from 'react';
import { settingsAPI } from '../../services/api';
import Button from '../common/Button/Button';
import './CreateCurrencyModal.css'; // Reuse the same modal styles

const CreateFiscalYearModal = ({ isOpen, onClose, onFiscalYearCreated }) => {
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    date_start: '',
    date_end: '',
    state: 'draft',
    active: true
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // Auto-generate code from name
    if (name === 'name' && value) {
      const code = value.replace(/\s+/g, '').toUpperCase().substring(0, 10);
      setFormData(prev => ({
        ...prev,
        code: code
      }));
    }
    
    // Auto-generate end date from start date (add 1 year - 1 day)
    if (name === 'date_start' && value) {
      const startDate = new Date(value);
      const endDate = new Date(startDate.getFullYear() + 1, startDate.getMonth(), startDate.getDate() - 1);
      setFormData(prev => ({
        ...prev,
        date_end: endDate.toISOString().split('T')[0]
      }));
    }
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Fiscal year name is required';
    }
    
    if (!formData.code.trim()) {
      newErrors.code = 'Fiscal year code is required';
    }
    
    if (!formData.date_start) {
      newErrors.date_start = 'Start date is required';
    }
    
    if (!formData.date_end) {
      newErrors.date_end = 'End date is required';
    }
    
    if (formData.date_start && formData.date_end) {
      if (new Date(formData.date_start) >= new Date(formData.date_end)) {
        newErrors.date_end = 'End date must be after start date';
      }
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setLoading(true);
    try {
      console.log('Creating fiscal year:', formData);

      // Ensure dates are in proper format
      const fiscalYearData = {
        ...formData,
        date_start: formData.date_start,
        date_end: formData.date_end
      };

      console.log('Sending fiscal year data:', fiscalYearData);

      const response = await settingsAPI.createFiscalYear(fiscalYearData);
      
      console.log('Fiscal year created:', response);
      
      // Reset form
      setFormData({
        name: '',
        code: '',
        date_start: '',
        date_end: '',
        state: 'draft',
        active: true
      });
      
      // Notify parent component
      onFiscalYearCreated(response);
      
      // Close modal
      onClose();
      
    } catch (error) {
      console.error('Error creating fiscal year:', error);
      console.error('Error response:', error.response);

      if (error.response?.data) {
        console.error('Error data:', error.response.data);
        setErrors(error.response.data);

        // Show specific error message
        const errorMessages = Object.entries(error.response.data)
          .map(([field, messages]) => `${field}: ${Array.isArray(messages) ? messages.join(', ') : messages}`)
          .join('\n');
        alert(`Validation errors:\n${errorMessages}`);
      } else {
        alert('Failed to create fiscal year. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setFormData({
        name: '',
        code: '',
        date_start: '',
        date_end: '',
        state: 'draft',
        active: true
      });
      setErrors({});
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay" onClick={handleClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>
            <i className="fas fa-calendar-alt"></i>
            Create New Fiscal Year
          </h2>
          <button className="modal-close" onClick={handleClose} disabled={loading}>
            <i className="fas fa-times"></i>
          </button>
        </div>

        <form onSubmit={handleSubmit} className="modal-form">
          <div className="form-row">
            <div className="form-group">
              <label htmlFor="name" className="required">Fiscal Year Name</label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className={errors.name ? 'error' : ''}
                placeholder="Fiscal Year 2025"
                disabled={loading}
              />
              {errors.name && <span className="error-text">{errors.name}</span>}
            </div>
            
            <div className="form-group">
              <label htmlFor="code" className="required">Code</label>
              <input
                type="text"
                id="code"
                name="code"
                value={formData.code}
                onChange={handleInputChange}
                className={errors.code ? 'error' : ''}
                placeholder="FY2025"
                maxLength="10"
                disabled={loading}
              />
              {errors.code && <span className="error-text">{errors.code}</span>}
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="date_start" className="required">Start Date</label>
              <input
                type="date"
                id="date_start"
                name="date_start"
                value={formData.date_start}
                onChange={handleInputChange}
                className={errors.date_start ? 'error' : ''}
                disabled={loading}
              />
              {errors.date_start && <span className="error-text">{errors.date_start}</span>}
            </div>
            
            <div className="form-group">
              <label htmlFor="date_end" className="required">End Date</label>
              <input
                type="date"
                id="date_end"
                name="date_end"
                value={formData.date_end}
                onChange={handleInputChange}
                className={errors.date_end ? 'error' : ''}
                disabled={loading}
              />
              {errors.date_end && <span className="error-text">{errors.date_end}</span>}
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="state">Initial State</label>
              <select
                id="state"
                name="state"
                value={formData.state}
                onChange={handleInputChange}
                disabled={loading}
              >
                <option value="draft">Draft</option>
                <option value="open">Open</option>
              </select>
            </div>
            
            <div className="form-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  name="active"
                  checked={formData.active}
                  onChange={handleInputChange}
                  disabled={loading}
                />
                <span className="checkbox-text">Active</span>
              </label>
            </div>
          </div>

          <div className="modal-actions">
            <Button
              type="button"
              variant="secondary"
              onClick={handleClose}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={loading}
              disabled={loading}
            >
              {loading ? 'Creating...' : 'Create Fiscal Year'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateFiscalYearModal;
